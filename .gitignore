# Dependencies
node_modules/
*/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*/dist/
*/build/

# Test coverage
coverage/
*/coverage/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Database files
*.db
*.sqlite
*.sqlite3
data/
*/data/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Claude Code related files
CLAUDE.md
.claude/
.claude_history
claude.json
.claude_cache/

# Documentation and notes (keep only README.md)
*.md
!README.md
!docs/README.md
docs/
notes/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Local development
*.local
.cache/