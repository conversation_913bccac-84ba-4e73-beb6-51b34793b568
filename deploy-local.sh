#!/bin/bash

# Nine Light 本地部署脚本
# 作者: Nine Light Team
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置（与线上保持一致的端口分配）
DEFAULT_FRONTEND_PORT=3000
DEFAULT_BACKEND_PORT=3001
DEFAULT_ENV="production"

# 全局变量
FRONTEND_PORT=$DEFAULT_FRONTEND_PORT
BACKEND_PORT=$DEFAULT_BACKEND_PORT
NODE_ENV=$DEFAULT_ENV
FORCE_REBUILD=false
SKIP_DEPS=false
VERBOSE=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Nine Light 本地部署脚本

用法: $0 [选项]

选项:
    -f, --frontend-port PORT    设置前端端口 (默认: $DEFAULT_FRONTEND_PORT)
    -b, --backend-port PORT     设置后端端口 (默认: $DEFAULT_BACKEND_PORT)
    -e, --env ENV               运行环境 (development|production) (默认: $DEFAULT_ENV)
    --force-rebuild             强制重新构建镜像
    --skip-deps                 跳过依赖安装
    -v, --verbose               详细输出
    -h, --help                  显示此帮助信息

示例:
    $0                                          # 使用默认配置本地部署
    $0 -f 3000 -b 3001                        # 指定端口本地部署
    $0 -e development --verbose                # 开发环境详细输出
    $0 --force-rebuild                         # 强制重建镜像

环境变量:
    可以通过 .env 文件或环境变量覆盖默认配置
    重要：请确保在 .env 文件中配置正确的 DATABASE_URL
    
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--frontend-port)
                FRONTEND_PORT="$2"
                shift 2
                ;;
            -b|--backend-port)
                BACKEND_PORT="$2"
                shift 2
                ;;
            -e|--env)
                NODE_ENV="$2"
                shift 2
                ;;
            --force-rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    # 验证端口号
    if ! [[ "$FRONTEND_PORT" =~ ^[0-9]+$ ]] || [ "$FRONTEND_PORT" -lt 1 ] || [ "$FRONTEND_PORT" -gt 65535 ]; then
        log_error "无效的前端端口: $FRONTEND_PORT"
        exit 1
    fi
    
    if ! [[ "$BACKEND_PORT" =~ ^[0-9]+$ ]] || [ "$BACKEND_PORT" -lt 1 ] || [ "$BACKEND_PORT" -gt 65535 ]; then
        log_error "无效的后端端口: $BACKEND_PORT"
        exit 1
    fi
    
    # 验证环境
    if [[ "$NODE_ENV" != "development" && "$NODE_ENV" != "production" ]]; then
        log_error "无效的运行环境: $NODE_ENV (支持: development, production)"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose - 优先检查新版本
    if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."

    local ports=("$FRONTEND_PORT" "$BACKEND_PORT")
    local port_names=("前端" "后端")

    for i in "${!ports[@]}"; do
        local port="${ports[$i]}"
        local name="${port_names[$i]}"

        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "${name}端口 $port 已被占用"
            read -p "是否继续? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "部署已取消"
                exit 0
            fi
        fi
    done

    log_success "端口检查完成"
}

# 主函数
main() {
    echo -e "${BLUE}"
    cat << "EOF"
    _   _ _            _     _       _     _   
   | \ | (_)          | |   (_)     | |   | |  
   |  \| |_ _ __   ___| |    _  __ _| |__ | |_ 
   | . ` | | '_ \ / _ \ |   | |/ _` | '_ \| __|
   | |\  | | | | |  __/ |___| | (_| | | | | |_ 
   |_| \_|_|_| |_|\___|_____|_|\__, |_| |_|\__|
                               __/ |          
                              |___/           
EOF
    echo -e "${NC}"
    echo "Nine Light 本地部署脚本 v1.0.0"
    echo "=================================="
    echo
    
    # 解析参数
    parse_args "$@"
    
    # 验证参数
    validate_args
    
    # 显示配置
    log_info "本地部署配置:"
    echo "  前端端口: $FRONTEND_PORT"
    echo "  后端端口: $BACKEND_PORT"
    echo "  运行环境: $NODE_ENV"
    echo "  部署模式: Docker (本地)"
    echo "  数据库: 外部数据库 (需在 .env 中配置 DATABASE_URL)"
    echo
    
    # 检查系统要求
    check_requirements
    
    # 检查端口占用
    check_ports
    
    # 执行本地部署
    source ./scripts/deploy-local.sh
    deploy_local
    
    log_success "本地部署完成!"
    echo
    log_info "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:3001"
    echo
    log_info "管理命令:"
    echo "  停止服务: docker compose -f docker-compose.local.yml down"
    echo "  重启服务: docker compose -f docker-compose.local.yml restart"
    echo "  查看日志: docker compose -f docker-compose.local.yml logs"
    echo "  查看状态: docker compose -f docker-compose.local.yml ps"
}

# 执行主函数
main "$@"