#!/bin/bash

# Nine Light 线上部署脚本
# 使用 docker-compose.yml (生产环境配置)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
DEFAULT_FRONTEND_PORT=3000
DEFAULT_BACKEND_PORT=3001
DEFAULT_ENV=production

# 当前配置
FRONTEND_PORT=$DEFAULT_FRONTEND_PORT
BACKEND_PORT=$DEFAULT_BACKEND_PORT
ENV=$DEFAULT_ENV
FORCE_REBUILD=false
SKIP_DEPS=false
VERBOSE=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Nine Light 线上部署脚本

用法: $0 [选项]

选项:
    -f, --frontend-port PORT    设置前端端口 (默认: $DEFAULT_FRONTEND_PORT)
    -b, --backend-port PORT     设置后端端口 (默认: $DEFAULT_BACKEND_PORT)
    -e, --env ENV               运行环境 (development|production) (默认: $DEFAULT_ENV)
    --force-rebuild             强制重新构建镜像
    --skip-deps                 跳过依赖安装
    -v, --verbose               详细输出
    -h, --help                  显示此帮助信息

示例:
    $0                                          # 使用默认配置线上部署
    $0 -f 3000 -b 3001                        # 指定端口线上部署
    $0 -e production --verbose                 # 生产环境详细输出
    $0 --force-rebuild                         # 强制重建镜像

环境变量:
    可以通过 .env 文件或环境变量覆盖默认配置
    重要：请确保在 .env 文件中配置正确的 DATABASE_URL

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--frontend-port)
                FRONTEND_PORT="$2"
                shift 2
                ;;
            -b|--backend-port)
                BACKEND_PORT="$2"
                shift 2
                ;;
            -e|--env)
                ENV="$2"
                shift 2
                ;;
            --force-rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证管理员凭据
validate_admin_credentials() {
    if ! grep -q "^ADMIN_PASSWORD=" .env || grep -q "^ADMIN_PASSWORD=$" .env || grep -q "^ADMIN_PASSWORD=your_admin_password" .env; then
        log_error "请在 .env 文件中设置 ADMIN_PASSWORD"
        log_info "示例: ADMIN_PASSWORD=your_secure_admin_password"
        exit 1
    fi
}

# 设置环境配置
setup_environment() {
    log_info "设置环境配置..."

    # 检查是否已有 .env 文件
    if [ -f ".env" ]; then
        log_info "使用现有的 .env 配置文件"

        # 验证必要的配置项
        if ! grep -q "DATABASE_URL=" .env || grep -q "mysql://user:password@your-database-host" .env; then
            log_warning "检测到默认的数据库配置，请确保已正确配置 DATABASE_URL"
        fi

        # 验证管理员密码配置
        validate_admin_credentials

        # 更新端口配置（如果通过命令行指定）
        if [ "$FRONTEND_PORT" != "$DEFAULT_FRONTEND_PORT" ] || [ "$BACKEND_PORT" != "$DEFAULT_BACKEND_PORT" ]; then
            log_info "更新端口配置..."
            sed -i.bak "s/^FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" .env
            sed -i.bak "s/^BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" .env
            sed -i.bak "s/^PORT=.*/PORT=$BACKEND_PORT/" .env
        fi
    else
        log_info "创建新的环境配置文件..."
        cp .env.example .env 2>/dev/null || {
            log_error "未找到 .env.example 文件，请手动创建 .env 文件"
            exit 1
        }

        # 设置端口
        sed -i.bak "s/^FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" .env
        sed -i.bak "s/^BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" .env
        sed -i.bak "s/^PORT=.*/PORT=$BACKEND_PORT/" .env
        sed -i.bak "s/^NODE_ENV=.*/NODE_ENV=$ENV/" .env

        log_warning "请编辑 .env 文件并配置必要的环境变量，特别是 DATABASE_URL 和 ADMIN_PASSWORD"
        log_info "配置完成后重新运行此脚本"
        exit 1
    fi

    # 导出环境变量
    export FRONTEND_PORT
    export BACKEND_PORT
    export NODE_ENV=$ENV

    log_success "环境配置完成"
}

# 检查 Docker 环境
check_docker() {
    log_info "检查 Docker 环境..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi

    log_success "Docker 环境检查通过"
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."

    # 使用生产环境的 docker-compose.yml
    docker compose down --remove-orphans 2>/dev/null || true

    log_success "现有服务已停止"
}

# 构建和启动服务
build_and_start() {
    log_info "构建和启动服务..."

    # 首先构建后端代码
    log_info "构建后端TypeScript代码..."
    cd backend
    if [[ "$SKIP_DEPS" != true ]]; then
        log_info "安装后端依赖..."
        yarn install --frozen-lockfile
    fi
    yarn build
    cd ..

    local build_args=""
    if [ "$FORCE_REBUILD" = true ]; then
        build_args="--no-cache"
        log_info "强制重新构建镜像..."
    fi

    # 使用生产环境的 docker-compose.yml
    if [ "$VERBOSE" = true ]; then
        docker compose build $build_args
        docker compose up -d
    else
        docker compose build $build_args >/dev/null 2>&1
        docker compose up -d >/dev/null 2>&1
    fi

    log_success "服务构建和启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."

    local max_wait=120
    local wait_time=0

    # 等待前端服务（生产环境前端在 FRONTEND_PORT）
    while [ $wait_time -lt $max_wait ]; do
        if curl -s http://localhost:$FRONTEND_PORT >/dev/null 2>&1; then
            log_success "前端服务已就绪"
            break
        fi

        sleep 2
        ((wait_time+=2))

        if [ $wait_time -ge $max_wait ]; then
            log_warning "前端服务启动超时，但继续执行..."
            break
        fi
    done

    # 等待后端服务（通过前端代理访问）
    wait_time=0
    while [ $wait_time -lt $max_wait ]; do
        if curl -s http://localhost:$FRONTEND_PORT/api/auth/health >/dev/null 2>&1; then
            log_success "后端服务已就绪"
            break
        fi

        sleep 2
        ((wait_time+=2))

        if [ $wait_time -ge $max_wait ]; then
            log_warning "后端服务启动超时，但继续执行..."
            break
        fi
    done
}

# 显示部署结果
show_result() {
    echo
    log_success "🎉 Nine Light 线上部署完成!"
    echo
    log_info "服务信息:"
    echo "  前端地址: http://localhost:$FRONTEND_PORT"
    echo "  API 地址: http://localhost:$FRONTEND_PORT/api"
    echo "  API 文档: http://localhost:$FRONTEND_PORT/api"
    echo
    log_info "管理命令:"
    echo "  查看状态: docker compose ps"
    echo "  查看日志: docker compose logs -f"
    echo "  停止服务: docker compose down"
    echo "  重启服务: ./scripts/restart.sh"
    echo "  健康检查: ./scripts/health-check.sh"
    echo
}

# 主函数
main() {
    echo "🚀 Nine Light 线上部署脚本"
    echo "================================"
    echo

    parse_args "$@"
    setup_environment
    check_docker
    stop_existing_services
    build_and_start
    wait_for_services
    show_result
}

# 执行主函数
main "$@"
