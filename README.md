# Nine Light

一个基于 React + TypeScript + Vite 前端和 Node.js + NestJS 后端的现代化 Web 应用程序，专为智能交易和批量账户管理而设计。

## ✨ 特性

- 🚀 **一键部署** - 支持 Docker 和独立部署模式
- 🔧 **灵活配置** - 支持自定义端口和环境变量
- 🛡️ **安全认证** - JWT 认证和路由守卫
- 📊 **实时监控** - 健康检查和日志管理
- 🎨 **现代界面** - 基于 Ant Design 的响应式设计
- 🔄 **批量操作** - 高效的批量账户管理功能
- 🎯 **智能交易** - 随机池交易系统，支持自动化策略
- 📈 **实时数据** - SSE 事件推送，实时交易状态更新
- 🗄️ **双数据库** - MySQL + SQLite 双数据库架构，性能优化
- 🎲 **随机策略** - 智能随机化交易参数，规避检测

## 🏗️ 技术栈

### 前端
- **React 19** - 现代化 React 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **Ant Design** - 企业级 UI 组件库
- **React Router** - 客户端路由

### 后端
- **NestJS 11.x** - 渐进式 Node.js 框架
- **TypeScript** - 类型安全的服务端开发
- **双数据库架构** - MySQL (Prisma ORM) + SQLite (sql.js)
- **实时通信** - Server-Sent Events (SSE) + EventEmitter2
- **JWT + Passport** - 安全的身份认证
- **RxJS** - 响应式编程支持

### 部署
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排
- **Nginx** - 静态文件服务和反向代理

## 🚀 快速开始

### 1. 环境要求

- **Node.js** 18.0+
- **npm** 8.0+
- **Docker** 20.0+ (可选，用于 Docker 部署)
- **MySQL** 8.0+ (独立部署时需要)

### 2. 安装

```bash
# 克隆项目
git clone <repository-url>
cd nine-light

# 安装依赖
npm install

# 安装前端依赖
cd frontend && npm install && cd ..

# 安装后端依赖
cd backend && npm install && cd ..
```

### 3. 配置环境

```bash
# 复制环境配置模板
cp .env.template .env

# 编辑配置文件 (必须配置数据库连接)
nano .env
# 重要：修改 DATABASE_URL 为您的实际数据库连接信息
```

**统一配置管理**：
- ✅ 所有环境变量统一在根目录的 `.env` 文件中配置
- ✅ 前端和后端自动从根目录读取配置
- ✅ 避免配置重复和不一致问题

### 4. 部署

```bash
# Docker 模式 (推荐)
./deploy.sh

# 独立模式
./deploy.sh --mode standalone

# 自定义端口
./deploy.sh --frontend-port 8080 --backend-port 3000
```

### 5. 访问应用

- **前端**: http://localhost:8080
- **后端 API**: http://localhost:3000
- **API 文档**: http://localhost:3000/api

## 📖 文档

- [部署指南](DEPLOYMENT.md) - 详细的部署说明和配置选项
- [前端开发](frontend/README.md) - 前端开发指南
- [后端开发](backend/README.md) - 后端开发指南和API文档
- [API文档](backend/docs/api-documentation.json) - Postman导入格式的API文档

## 🎛️ 管理命令

```bash
# 健康检查
./scripts/health-check.sh

# 停止服务
./scripts/stop.sh

# 重启服务
./scripts/restart.sh

# 查看日志
./scripts/logs.sh

# 查看帮助
./deploy.sh --help
```

## 🔧 配置

### 环境变量

主要配置文件：`.env`

```bash
# 服务端口
FRONTEND_PORT=8080
BACKEND_PORT=3000
DB_PORT=3306

# 数据库配置
DB_NAME=nine_light
DB_USER=nine_user
DB_PASSWORD=your_secure_password

# 应用配置
NODE_ENV=production
JWT_SECRET=your_super_secret_jwt_key
```

### 部署模式

#### Docker 模式 (推荐)
- 完整的容器化环境
- 包含数据库服务
- 易于扩展和维护

#### 独立模式
- 直接在宿主机运行
- 便于开发和调试
- 需要外部数据库

## 🛠️ 开发

### 前端开发

```bash
cd frontend
npm run dev
```

### 后端开发

```bash
cd backend
npm run start:dev
```

### 数据库管理

```bash
cd backend
npx prisma studio    # 数据库可视化工具
npx prisma migrate dev  # 运行迁移
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -i :8080  # 检查端口占用
   ```

2. **Docker 服务异常**
   ```bash
   docker-compose logs  # 查看日志
   ```

3. **数据库连接失败**
   ```bash
   ./scripts/health-check.sh  # 运行健康检查
   ```

更多故障排除信息请查看 [部署指南](DEPLOYMENT.md)。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果您遇到问题或需要帮助：

1. 查看 [部署指南](DEPLOYMENT.md) 和 [后端开发文档](backend/README.md)
2. 运行健康检查：`./scripts/health-check.sh`
3. 查看应用日志：`./scripts/logs.sh`
4. 提交 Issue 并附上详细的错误信息

---

**Nine Light Team** | 版本 1.0.0
