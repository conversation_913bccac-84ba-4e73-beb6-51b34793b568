# Nine Light 部署配置示例
# 复制此文件为 .env 并根据实际情况修改

# ===========================================
# 服务端口配置
# ===========================================
FRONTEND_PORT=8080
BACKEND_PORT=3000

# ===========================================
# 数据库配置
# ===========================================
# 外部MySQL数据库 - 用于查询线上业务数据（用户、资产等）
# 重要：请修改为您的实际数据库连接信息
DATABASE_URL=mysql://mysqld_exporter:NineMySQL2025Monitor!@nine-mysql-production.c94eqyo4iffa.ap-southeast-1.rds.amazonaws.com:3306/Binineex

# Nine Light SQLite数据库 - 用于存储Nine Light项目的业务记录行为（交易记录、会话状态等）
# 使用sql.js纯JavaScript实现，数据存储在本地文件中
NINE_LIGHT_DATABASE_URL=file:./data/nine-light.db

# ===========================================
# Nine API 配置
# ===========================================
NINE_API_BASE_URL=https://openapi.binineex.com

# ===========================================
# 管理员账户配置
# ===========================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=VXR&y3QX&by75w

# ===========================================
# 内盘 API 配置
# ===========================================
# 内盘 API 凭据（可选，如果不设置将使用 AccountDataService 中的账户）
INNER_DISK_API_KEY=your_inner_disk_api_key
INNER_DISK_SECRET=your_inner_disk_secret

# ===========================================
# 应用配置
# ===========================================
NODE_ENV=production
JWT_SECRET=VXR&y3QX&by75w_jwt_secret
JWT_EXPIRES_IN=24h

# ===========================================
# 部署选项
# ===========================================
DEPLOY_MODE=docker
ENABLE_HTTPS=false
DOMAIN=localhost
