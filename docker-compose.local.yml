services:
  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nine-light-backend-local
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: 3000
      DATABASE_URL: ${DATABASE_URL}
      NINE_LIGHT_DATABASE_URL: ${NINE_LIGHT_DATABASE_URL:-file:./data/nine-light.db}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      NINE_API_BASE_URL: ${NINE_API_BASE_URL:-https://openapi.binineex.com}
      ADMIN_USERNAME: ${ADMIN_USERNAME:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      INNER_DISK_API_KEY: ${INNER_DISK_API_KEY}
      INNER_DISK_SECRET: ${INNER_DISK_SECRET}
    ports:
      - "3001:3000"  # 本地暴露到3001端口，避免与前端冲突
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data  # 挂载数据目录，解决权限问题
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/auth/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (Nginx) - 本地版本
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.local  # 使用本地版本的 Dockerfile
      args:
        VITE_API_URL: ${VITE_API_URL:-/api}
        VITE_APP_TITLE: ${VITE_APP_TITLE:-Nine Light}
        VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0}
        VITE_DEBUG: ${VITE_DEBUG:-true}
    container_name: nine-light-frontend-local
    restart: unless-stopped
    ports:
      - "3000:80"  # 本地前端使用3000端口
    depends_on:
      - backend
    extra_hosts:
      - "host.docker.internal:host-gateway"  # 允许容器访问宿主机
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3