# Nine Light Backend API

基于 NestJS 的后端服务，提供用户管理、批量交易处理和智能交易功能。

## 🚀 功能特性

- **认证系统**: JWT 令牌认证，管理员登录
- **用户管理**: 批量用户导入，用户查询统计
- **批处理**: 批量登录验证，批量交易处理
- **随机池交易**: 智能随机池子刷量系统，支持实时监控
- **内盘交易**: 内盘新币交易和管理功能
- **实时推送**: SSE 事件流支持，实时交易状态更新
- **双数据库**: MySQL + SQLite 双数据库架构，性能优化

## 📋 API 端点

### 认证相关
- `GET /auth/health` - 健康检查（公开）
- `POST /auth/login` - 管理员登录（公开）
- `GET /auth/profile` - 获取用户信息（需认证）
- `POST /auth/verify` - 验证令牌（需认证）

### 用户管理相关（需认证）
- `POST /users/init-accounts` - 初始化账户数据
- `GET /users/count` - 获取用户统计
- `GET /users` - 获取所有用户列表

### 批处理相关（需认证）
- `POST /batch/login` - 批量登录验证
- `POST /batch/innerTrade` - 批量交易处理
- `GET /batch/history` - 获取操作历史
- `GET /batch/records` - 获取点亮记录

### 随机池交易相关（需认证）
- `POST /random-pool-trade/start` - 启动随机池交易
- `POST /random-pool-trade/stop` - 停止随机池交易
- `GET /random-pool-trade/status` - 获取当前状态和统计
- `GET /random-pool-trade/history` - 获取交易历史记录
- `GET /random-pool-trade/pools` - 获取可选择的池子列表
- `GET /random-pool-trade/events` - SSE事件流（实时更新）

### 内盘交易相关（需认证）
- `POST /inner-disk/trade` - 执行内盘交易
- `GET /inner-disk/status` - 获取内盘交易状态
- `POST /inner-disk-new-coin/start` - 启动内盘新币交易
- `POST /inner-disk-new-coin/stop` - 停止内盘新币交易

## 🛠️ 开发环境

### 环境要求
- Node.js 18+
- MySQL 8.0+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 环境配置
复制 `.env.example` 到 `.env` 并配置：
```env
DATABASE_URL="mysql://user:password@localhost:3306/database"
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="24h"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="your-password"
```

### 数据库设置
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push

# 查看数据库
npx prisma studio
```

## 🚀 运行项目

### 开发模式
```bash
npm run start:dev
```

### 生产模式
```bash
npm run build
npm run start:prod
```

## 🧪 测试

```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# E2E 测试
npm run test:e2e
```

## 📊 项目状态

- **测试覆盖率**: 37.79%
- **认证系统**: ✅ 完整实现
- **API保护**: ✅ JWT守卫保护
- **数据验证**: 🔄 进行中

## 🏗️ 技术栈

- **框架**: NestJS 11.x
- **数据库**: MySQL (Prisma ORM) + SQLite (sql.js)
- **认证**: JWT + Passport
- **实时通信**: Server-Sent Events (SSE)
- **测试**: Jest
- **代码质量**: ESLint + Prettier
- **工具库**: RxJS, EventEmitter2

## 📝 开发说明

### 模块结构
```
src/
├── auth/                    # 认证模块
│   ├── controllers/         # 认证控制器
│   ├── services/           # 认证服务
│   ├── guards/             # JWT 守卫
│   ├── strategies/         # JWT 策略
│   ├── decorators/         # 装饰器
│   └── dto/               # 数据传输对象
├── users/                  # 用户管理模块
│   ├── users.controller.ts # 用户控制器
│   ├── users.service.ts    # 用户服务
│   └── users.module.ts     # 用户模块
├── batch/                  # 批处理模块
│   ├── controllers/        # 批处理控制器
│   ├── services/          # 批处理服务
│   ├── dto/              # 数据传输对象
│   └── types/            # 类型定义
├── random-pool-trade/      # 随机池交易模块
│   ├── controllers/        # 交易控制器
│   ├── services/          # 交易服务层
│   │   ├── random-pool-trade.service.ts    # 主服务
│   │   ├── trade-strategy.service.ts       # 交易策略
│   │   ├── pool-manager.service.ts         # 池管理
│   │   ├── database-trade-record.service.ts # 数据记录
│   │   └── pool-trade-executor.service.ts  # 交易执行
│   ├── dto/              # 数据传输对象
│   ├── types/            # 类型定义
│   └── constants/        # 常量配置
├── inner-disk/            # 内盘交易模块
│   ├── controllers/       # 内盘控制器
│   ├── services/         # 内盘服务
│   ├── dto/             # 数据传输对象
│   └── types/           # 类型定义
├── inner-disk-new-coin/   # 内盘新币模块
│   ├── controllers/       # 新币控制器
│   ├── services/         # 新币服务
│   └── types/           # 类型定义
├── common/               # 公共模块
│   ├── services/         # 公共服务
│   │   ├── nine-light-database.service.ts  # SQLite数据库
│   │   ├── account-selection.service.ts    # 账户选择
│   │   ├── base-session.service.ts         # 会话基类
│   │   └── base-trade.service.ts           # 交易基类
│   ├── utils/           # 工具函数
│   │   ├── error-handler.util.ts          # 错误处理
│   │   ├── random.utils.ts                # 随机工具
│   │   └── pool-availability.util.ts      # 池可用性
│   ├── types/           # 公共类型
│   ├── constants/       # 全局常量
│   ├── filters/         # 全局过滤器
│   └── interceptors/    # 全局拦截器
├── balance/             # 余额模块
├── database/            # MySQL数据库模块
└── types/              # 全局类型定义
```

### 双数据库架构

系统采用双数据库架构以优化性能：

#### **MySQL (Prisma ORM)**
- **用途**: 用户数据、批处理记录、配置信息
- **特点**: 持久化存储，支持复杂查询，ACID 事务
- **适用场景**: 长期数据存储，复杂关联查询

#### **SQLite (sql.js)**
- **用途**: 实时交易记录，临时会话数据，高频读写
- **特点**: 内存数据库，极高性能，轻量级
- **适用场景**: 实时交易数据，临时统计，会话管理

```typescript
// MySQL 使用示例
@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}
  
  async findUsers() {
    return this.prisma.user.findMany();
  }
}

// SQLite 使用示例  
@Injectable()
export class TradeRecordService {
  constructor(private nineLightDb: NineLightDatabaseService) {}
  
  saveTradeRecord(record: TradeRecord) {
    // 高性能实时写入
    return this.nineLightDb.insert('trades', record);
  }
}
```

### 添加新功能
1. 创建模块: `nest g module feature`
2. 创建服务: `nest g service feature`
3. 创建控制器: `nest g controller feature`
4. 添加测试文件
5. 更新模块导入

## 🔒 安全特性

- JWT 令牌认证
- 路由级别的访问控制
- 输入数据验证（开发中）
- CORS 支持

## 📈 性能优化

- 数据库连接池
- 请求验证管道
- 错误处理中间件

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
