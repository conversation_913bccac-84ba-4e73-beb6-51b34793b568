// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  apiKey    String   @unique @map("api_key")
  secret    String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联用户资产
  assets                  UserAsset[]
  // 关联点亮记录
  lightRecords            LightRecord[]
  // 关联帖子
  posts                   Post[]
  // 关联内盘打新配置
  innerDiskNewCoinConfigs InnerDiskNewCoinConfig[]
  // 关联随机池子交易会话
  randomPoolTradeSessions RandomPoolTradeSession[]
  // 关联随机池交易记录
  randomPoolTradeRecords  RandomPoolTradeRecord[]

  @@map("users")
}

model UserAsset {
  id      Int   @id @default(autoincrement())
  userId  Int   @map("user_id")
  assetId Int   @map("asset_id")
  balance Float @default(0)

  // 外键关联
  user User @relation(fields: [userId], references: [id])

  @@unique([userId, assetId])
  @@map("user_assets")
}

model LightRecord {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  postId    Int      @map("post_id")
  assetId   Int      @map("asset_id")
  amount    Float
  status    String   @default("pending") // pending, success, failed
  errorMsg  String?  @map("error_msg")
  createdAt DateTime @default(now()) @map("created_at")

  // 外键关联
  user User  @relation(fields: [userId], references: [id])
  post Post? @relation(fields: [postId], references: [id])

  @@map("light_records")
}

model BatchOperation {
  id           Int       @id @default(autoincrement())
  postId       Int       @map("post_id")
  assetId      Int       @map("asset_id")
  amount       Float
  totalUsers   Int       @map("total_users")
  successCount Int       @default(0) @map("success_count")
  failedCount  Int       @default(0) @map("failed_count")
  status       String    @default("pending") // pending, processing, completed, failed
  createdAt    DateTime  @default(now()) @map("created_at")
  completedAt  DateTime? @map("completed_at")

  // 关联帖子
  post Post? @relation(fields: [postId], references: [id])

  @@map("batch_operations")
}

model Post {
  id                   Int      @id @default(autoincrement())
  assetId              Int      @map("asset_id")
  userId               Int      @map("user_id")
  poolId               Int?     @map("pool_id")
  tokenId              Int      @map("token_id")
  tradePairId          Int?     @map("trade_pair_id")
  content              String   @db.Text
  source               Int      @default(0)
  createdTime          DateTime @default(now()) @map("created_time")
  tweetUserId          Int?     @map("tweet_user_id")
  tweetId              String?  @map("tweet_id")
  tweetUserName        String?  @map("tweet_user_name")
  tweetNickName        String?  @map("tweet_nick_name")
  tweetUrl             String?  @map("tweet_url")
  tweetLikeCount       Int      @default(0) @map("tweet_like_count")
  tweetUserPhotoUrl    String?  @map("tweet_user_photo_url")
  tweetRetweetCount    Int      @default(0) @map("tweet_retweet_count")
  tweetReplyCount      Int      @default(0) @map("tweet_reply_count")
  tweetQuoteCount      Int      @default(0) @map("tweet_quote_count")
  tweetBookmarkCount   Int      @default(0) @map("tweet_bookmark_count")
  tweetImpressionCount Int      @default(0) @map("tweet_impression_count")
  logo                 String?
  tokenSymbol          String   @map("token_symbol")
  tokenName            String   @map("token_name")
  lightNum             Int      @default(0) @map("light_num")
  status               Int      @default(0)
  stake                Float    @default(0)
  change24h            Float    @default(0) @map("change_24h")
  innerPrice           Float    @default(0) @map("inner_price")
  outerPrice           Float    @default(0) @map("outer_price")
  marketCap            Float?   @map("market_cap")
  process              Int      @default(0)
  assetProperty        Int      @default(0) @map("asset_property")
  userAvatar           String?  @map("user_avatar")
  nickName             String?  @map("nick_name")
  lighted              Boolean  @default(false)
  isLiked              Boolean  @default(false) @map("is_liked")
  stableCoinSymbol     String?  @map("stable_coin_symbol")
  isBlueV              Int      @default(0) @map("is_blue_v")
  postLikeCount        Int      @default(0) @map("post_like_count")
  postCommentCount     Int      @default(0) @map("post_comment_count")
  postShareCount       Int      @default(0) @map("post_share_count")
  postViewCount        Int      @default(0) @map("post_view_count")
  postHotScore         Float    @default(0) @map("post_hot_score")

  // 关联用户
  user            User             @relation(fields: [userId], references: [id])
  // 关联批量操作
  batchOperations BatchOperation[]
  // 关联点亮记录
  lightRecords    LightRecord[]
  // 关联帖子图片
  images          PostImage[]

  @@map("posts")
}

model PostImage {
  id           Int    @id @default(autoincrement())
  postId       Int    @map("post_id")
  postCoverUrl String @map("post_cover_url")

  // 关联帖子
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@map("post_images")
}

// 添加 square_tag_token 表模型
model SquareTagToken {
  id          Int      @id @default(autoincrement())
  assetId     BigInt   @map("asset_id")
  tokenName   String   @map("tokenName")
  tokenSymbol String   @map("tokenSymbol")
  lightNum    Int      @map("light_num")
  postId      Int?     @map("post_id")
  status      String?
  stake       BigInt?
  createdTime DateTime @map("created_time")

  @@index([tokenSymbol])
  @@index([assetId])
  @@index([lightNum])
  @@index([createdTime])
  @@map("square_tag_token")
}

// ========================================
// 内盘打新功能相关表
// ========================================

/**
 * 内盘打新监控配置表
 * 存储用户的打新监控配置信息
 */
model InnerDiskNewCoinConfig {
  id                 Int      @id @default(autoincrement())
  userId             Int      @map("user_id")
  isEnabled          Boolean  @default(false) @map("is_enabled")
  apiKey             String   @map("api_key")
  secret             String
  purchaseAmount     Float    @map("purchase_amount")
  purchaseType       String   @default("FIXED") @map("purchase_type") // FIXED: 固定数量, PERCENTAGE: 按余额百分比
  purchasePercentage Float?   @map("purchase_percentage") // 当purchaseType为PERCENTAGE时使用
  profitRate         Float    @default(0.1) @map("profit_rate") // 止盈率，默认10%
  stopLossRate       Float?   @map("stop_loss_rate") // 止损率，可选
  monitorInterval    Int      @default(5000) @map("monitor_interval") // 监控间隔(毫秒)
  maxPositions       Int      @default(5) @map("max_positions") // 最大持仓数量
  minMarketCap       Float?   @map("min_market_cap") // 最小市值过滤
  maxMarketCap       Float?   @map("max_market_cap") // 最大市值过滤
  excludeTokens      String?  @map("exclude_tokens") @db.Text // 排除的代币列表(JSON格式)

  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // 关联用户
  user      User                       @relation(fields: [userId], references: [id])
  // 关联监控会话
  sessions  InnerDiskNewCoinSession[]
  // 关联交易记录
  trades    InnerDiskNewCoinTrade[]
  // 关联持仓记录
  positions InnerDiskNewCoinPosition[]

  @@unique([userId])
  @@map("inner_disk_new_coin_configs")
}

/**
 * 内盘打新监控会话表
 * 记录每次启动的监控会话信息
 */
model InnerDiskNewCoinSession {
  id               String    @id @default(uuid())
  configId         Int       @map("config_id")
  status           String    @default("RUNNING") // RUNNING, STOPPED, ERROR
  startTime        DateTime  @default(now()) @map("start_time")
  endTime          DateTime? @map("end_time")
  totalDetected    Int       @default(0) @map("total_detected") // 检测到的新币数量
  totalTrades      Int       @default(0) @map("total_trades") // 执行的交易数量
  successfulTrades Int       @default(0) @map("successful_trades") // 成功的交易数量
  totalProfit      Float     @default(0) @map("total_profit") // 总盈亏
  errorMessage     String?   @map("error_message") @db.Text
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // 关联配置
  config     InnerDiskNewCoinConfig      @relation(fields: [configId], references: [id])
  // 关联新币检测记录
  detections InnerDiskNewCoinDetection[]
  // 关联交易记录
  trades     InnerDiskNewCoinTrade[]
  // 关联持仓记录
  positions  InnerDiskNewCoinPosition[]

  @@index([configId])
  @@index([status])
  @@index([startTime])
  @@map("inner_disk_new_coin_sessions")
}

/**
 * 内盘新币检测记录表
 * 记录检测到的新币信息
 */
model InnerDiskNewCoinDetection {
  id               Int      @id @default(autoincrement())
  sessionId        String   @map("session_id")
  poolId           Int      @map("pool_id")
  tokenSymbol      String   @map("token_symbol")
  tokenName        String?  @map("token_name")
  stableCoinSymbol String   @map("stable_coin_symbol")
  initialPrice     Float    @map("initial_price")
  marketCap        Float?   @map("market_cap")
  liquidity        Float?
  volume24h        Float?   @map("volume_24h")
  chain            String
  detectedAt       DateTime @default(now()) @map("detected_at")
  isTraded         Boolean  @default(false) @map("is_traded") // 是否已交易
  tradeReason      String?  @map("trade_reason") // 交易原因或跳过原因
  createdAt        DateTime @default(now()) @map("created_at")

  // 关联会话
  session InnerDiskNewCoinSession @relation(fields: [sessionId], references: [id])
  // 关联交易记录
  trades  InnerDiskNewCoinTrade[]

  @@index([sessionId])
  @@index([poolId])
  @@index([tokenSymbol])
  @@index([detectedAt])
  @@index([isTraded])
  @@map("inner_disk_new_coin_detections")
}

/**
 * 内盘打新交易记录表
 * 记录所有的买入和卖出交易
 */
model InnerDiskNewCoinTrade {
  id           String   @id @default(uuid())
  configId     Int      @map("config_id")
  sessionId    String   @map("session_id")
  detectionId  Int?     @map("detection_id")
  poolId       Int      @map("pool_id")
  tokenSymbol  String   @map("token_symbol")
  tradeType    String   @map("trade_type") // BUY, SELL
  amount       Float // 交易数量
  price        Float // 交易价格
  totalValue   Float    @map("total_value") // 交易总价值
  status       String   @default("PENDING") // PENDING, SUCCESS, FAILED
  txHash       String?  @map("tx_hash") // 交易哈希
  errorMessage String?  @map("error_message") @db.Text
  executedAt   DateTime @default(now()) @map("executed_at")

  // 买入相关字段
  buyPrice  Float?    @map("buy_price") // 买入价格(卖出时记录)
  buyAmount Float?    @map("buy_amount") // 买入数量(卖出时记录)
  buyTime   DateTime? @map("buy_time") // 买入时间(卖出时记录)

  // 盈亏相关字段
  profitLoss     Float? @map("profit_loss") // 盈亏金额
  profitLossRate Float? @map("profit_loss_rate") // 盈亏率

  // 止盈止损相关
  triggerType String? @map("trigger_type") // PROFIT_TARGET, STOP_LOSS, MANUAL
  targetPrice Float?  @map("target_price") // 目标价格

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联配置
  config    InnerDiskNewCoinConfig     @relation(fields: [configId], references: [id])
  // 关联会话
  session   InnerDiskNewCoinSession    @relation(fields: [sessionId], references: [id])
  // 关联检测记录
  detection InnerDiskNewCoinDetection? @relation(fields: [detectionId], references: [id])

  @@index([configId])
  @@index([sessionId])
  @@index([detectionId])
  @@index([poolId])
  @@index([tokenSymbol])
  @@index([tradeType])
  @@index([status])
  @@index([executedAt])
  @@map("inner_disk_new_coin_trades")
}

/**
 * 内盘打新持仓表
 * 记录当前持有的新币仓位
 */
model InnerDiskNewCoinPosition {
  id                Int       @id @default(autoincrement())
  configId          Int       @map("config_id")
  sessionId         String    @map("session_id")
  poolId            Int       @map("pool_id")
  tokenSymbol       String    @map("token_symbol")
  buyTradeId        String    @map("buy_trade_id") // 关联的买入交易ID
  amount            Float // 持仓数量
  buyPrice          Float     @map("buy_price") // 买入价格
  buyValue          Float     @map("buy_value") // 买入总价值
  currentPrice      Float?    @map("current_price") // 当前价格
  currentValue      Float?    @map("current_value") // 当前总价值
  unrealizedPnl     Float?    @map("unrealized_pnl") // 未实现盈亏
  unrealizedPnlRate Float?    @map("unrealized_pnl_rate") // 未实现盈亏率
  profitTarget      Float     @map("profit_target") // 止盈目标价
  stopLossTarget    Float?    @map("stop_loss_target") // 止损目标价
  status            String    @default("HOLDING") // HOLDING, SOLD, EXPIRED
  boughtAt          DateTime  @map("bought_at")
  soldAt            DateTime? @map("sold_at")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联配置
  config  InnerDiskNewCoinConfig  @relation(fields: [configId], references: [id])
  // 关联会话
  session InnerDiskNewCoinSession @relation(fields: [sessionId], references: [id])

  @@unique([configId, poolId]) // 每个配置每个池子只能有一个持仓
  @@index([configId])
  @@index([sessionId])
  @@index([poolId])
  @@index([tokenSymbol])
  @@index([status])
  @@index([boughtAt])
  @@map("inner_disk_new_coin_positions")
}

// ========================================
// 随机内盘交易功能相关表
// ========================================

/**
 * 随机内盘交易会话表
 * 记录每次启动的随机交易会话信息
 */
model RandomPoolTradeSession {
  id                String    @id @default(uuid())
  userId            Int       @map("user_id")
  status            String    @default("RUNNING") // RUNNING, STOPPED, ERROR
  startTime         DateTime  @default(now()) @map("start_time")
  endTime           DateTime? @map("end_time")

  // 配置参数
  minBuyAmount      Float     @map("min_buy_amount")
  maxBuyAmount      Float     @map("max_buy_amount")
  minBuyIntervalMs  Int       @map("min_buy_interval_ms")
  maxBuyIntervalMs  Int       @map("max_buy_interval_ms")
  minSellDelayMs    Int       @map("min_sell_delay_ms")
  maxSellDelayMs    Int       @map("max_sell_delay_ms")
  buyCount          Int       @map("buy_count")
  poolCooldownMs    Int       @default(300000) @map("pool_cooldown_ms")

  // 统计信息
  totalBuyOperations        Int   @default(0) @map("total_buy_operations")
  totalSellOperations       Int   @default(0) @map("total_sell_operations")
  successfulBuyOperations   Int   @default(0) @map("successful_buy_operations")
  successfulSellOperations  Int   @default(0) @map("successful_sell_operations")
  totalBuyVolume           Float @default(0) @map("total_buy_volume")
  totalSellVolume          Float @default(0) @map("total_sell_volume")
  activePoolsCount         Int   @default(0) @map("active_pools_count")
  completedPoolsCount      Int   @default(0) @map("completed_pools_count")

  errorMessage      String?   @map("error_message") @db.Text
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联用户
  user              User                        @relation(fields: [userId], references: [id])
  // 关联交易记录
  tradeRecords      RandomPoolTradeRecord[]
  // 关联池子状态
  poolStates        RandomPoolTradePoolState[]

  @@index([userId])
  @@index([status])
  @@index([startTime])
  @@map("random_pool_trade_sessions")
}

/**
 * 随机池交易记录表
 * 存储每次交易的详细记录
 */
model RandomPoolTradeRecord {
  id                String   @id @default(cuid())
  sessionId         String   @map("session_id")
  userId            Int      @map("user_id")
  poolId            String   @map("pool_id")
  symbol            String
  tradeType         String   @map("trade_type") // 'buy' | 'sell'
  amount            Float
  price             Float
  success           Boolean  @default(true)
  errorMessage      String?  @map("error_message") @db.Text
  timestamp         DateTime @default(now())
  createdAt         DateTime @default(now()) @map("created_at")

  // 关联用户
  user    User                     @relation(fields: [userId], references: [id])
  // 关联会话
  session RandomPoolTradeSession  @relation(fields: [sessionId], references: [id])

  @@index([sessionId])
  @@index([poolId])
  @@index([symbol])
  @@index([tradeType])
  @@index([success])
  @@index([timestamp])
  @@map("random_pool_trade_records")
}



/**
 * 随机内盘交易池子状态表
 * 记录每个池子的累积状态
 */
model RandomPoolTradePoolState {
  id                String   @id @default(uuid())
  sessionId         String   @map("session_id")
  poolId            Int      @map("pool_id")
  symbol            String
  accumulatedAmount Float    @default(0) @map("accumulated_amount")
  completedBuyCount Int      @default(0) @map("completed_buy_count")
  targetBuyCount    Int      @map("target_buy_count")
  status            String   @default("buying") // buying, selling, completed, error

  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // 关联会话
  session           RandomPoolTradeSession @relation(fields: [sessionId], references: [id])

  @@unique([sessionId, poolId]) // 每个会话每个池子只能有一个状态记录
  @@index([sessionId])
  @@index([poolId])
  @@index([symbol])
  @@index([status])
  @@map("random_pool_trade_pool_states")
}
