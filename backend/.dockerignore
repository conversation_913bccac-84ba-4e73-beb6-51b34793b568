# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
coverage/

# 开发文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 测试文件
test/
*.test.ts
*.spec.ts

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# OS文件
.DS_Store
.DS_Store?
._*
Thumbs.db

# Git文件
.git/
.gitignore

# 文档文件
README.md
docs/
*.md

# 临时文件
tmp/
temp/

# 数据文件（开发时的）
data/
