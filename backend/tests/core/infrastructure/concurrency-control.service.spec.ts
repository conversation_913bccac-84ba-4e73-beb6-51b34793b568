import { Test, TestingModule } from '@nestjs/testing';
import { ConcurrencyControlService } from '../../../src/core/infrastructure/concurrency-control.service';

describe('ConcurrencyControlService', () => {
  let service: ConcurrencyControlService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConcurrencyControlService],
    }).compile();

    service = module.get<ConcurrencyControlService>(ConcurrencyControlService);
  });

  afterEach(() => {
    // 清理所有限制器
    service.clearAllLimiters();
  });

  describe('executeWithLimit', () => {
    it('应该正常执行操作', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');

      const result = await service.executeWithLimit('test-key', mockOperation, {
        maxConcurrent: 2,
      });

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('应该限制并发数量', async () => {
      let activeCount = 0;
      let maxActiveCount = 0;

      const mockOperation = jest.fn().mockImplementation(async () => {
        activeCount++;
        maxActiveCount = Math.max(maxActiveCount, activeCount);
        await new Promise((resolve) => setTimeout(resolve, 50)); // 模拟异步操作
        activeCount--;
        return 'success';
      });

      // 并发执行 5 个操作，但限制为 2 个并发
      const promises = Array.from({ length: 5 }, (_, i) =>
        service.executeWithLimit(
          'concurrent-test',
          () => mockOperation(`operation-${i}`),
          { maxConcurrent: 2 },
        ),
      );

      const results = await Promise.all(promises);

      expect(results).toEqual([
        'success',
        'success',
        'success',
        'success',
        'success',
      ]);
      expect(mockOperation).toHaveBeenCalledTimes(5);
      expect(maxActiveCount).toBeLessThanOrEqual(2);
    });

    it('应该处理超时', async () => {
      const slowOperation = jest.fn().mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 200)); // 200ms 延迟
        return 'success';
      });

      await expect(
        service.executeWithLimit(
          'timeout-test',
          slowOperation,
          { maxConcurrent: 1, timeout: 100 }, // 100ms 超时
        ),
      ).rejects.toThrow('操作超时');
    });

    it('应该处理操作异常', async () => {
      const errorOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      await expect(
        service.executeWithLimit('error-test', errorOperation, {
          maxConcurrent: 1,
        }),
      ).rejects.toThrow('操作失败');
    });
  });

  describe('executeBatch', () => {
    it('应该批量执行操作', async () => {
      const items = [1, 2, 3, 4, 5];
      const operation = jest
        .fn()
        .mockImplementation((item: number) => Promise.resolve(item * 2));

      const results = await service.executeBatch(
        'batch-test',
        items,
        operation,
        { maxConcurrent: 2 },
      );

      expect(results).toEqual([2, 4, 6, 8, 10]);
      expect(operation).toHaveBeenCalledTimes(5);
    });

    it('应该限制批量操作的并发数', async () => {
      let activeCount = 0;
      let maxActiveCount = 0;

      const items = Array.from({ length: 10 }, (_, i) => i);
      const operation = jest.fn().mockImplementation(async (item: number) => {
        activeCount++;
        maxActiveCount = Math.max(maxActiveCount, activeCount);
        await new Promise((resolve) => setTimeout(resolve, 20));
        activeCount--;
        return item * 2;
      });

      const results = await service.executeBatch(
        'batch-concurrent-test',
        items,
        operation,
        { maxConcurrent: 3 },
      );

      expect(results).toEqual([0, 2, 4, 6, 8, 10, 12, 14, 16, 18]);
      expect(operation).toHaveBeenCalledTimes(10);
      expect(maxActiveCount).toBeLessThanOrEqual(3);
    });

    it('应该在批量操作中处理部分失败', async () => {
      const items = [1, 2, 3, 4, 5];
      const operation = jest.fn().mockImplementation((item: number) => {
        if (item === 3) {
          return Promise.reject(new Error(`操作失败: ${item}`));
        }
        return Promise.resolve(item * 2);
      });

      await expect(
        service.executeBatch('batch-partial-error-test', items, operation, {
          maxConcurrent: 2,
        }),
      ).rejects.toThrow('批量操作第2项失败');
    });
  });

  describe('executeBatchTolerant', () => {
    it('应该容错处理批量操作', async () => {
      const items = [1, 2, 3, 4, 5];
      const operation = jest.fn().mockImplementation((item: number) => {
        if (item === 3) {
          return Promise.reject(new Error(`操作失败: ${item}`));
        }
        return Promise.resolve(item * 2);
      });

      const results = await service.executeBatchTolerant(
        'batch-tolerant-test',
        items,
        operation,
        { maxConcurrent: 2 },
      );

      expect(results).toHaveLength(5);
      expect(results[0]).toEqual({ success: true, result: 2, item: 1 });
      expect(results[1]).toEqual({ success: true, result: 4, item: 2 });
      expect(results[2]).toEqual({
        success: false,
        error: '操作失败: 3',
        item: 3,
      });
      expect(results[3]).toEqual({ success: true, result: 8, item: 4 });
      expect(results[4]).toEqual({ success: true, result: 10, item: 5 });
    });

    it('应该处理所有操作都失败的情况', async () => {
      const items = [1, 2, 3];
      const operation = jest.fn().mockRejectedValue(new Error('全部失败'));

      const results = await service.executeBatchTolerant(
        'all-fail-test',
        items,
        operation,
        { maxConcurrent: 2 },
      );

      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.success).toBe(false);
        expect(result.error).toBe('全部失败');
        expect(result.item).toBe(items[index]);
      });
    });
  });

  describe('getStats', () => {
    it('应该返回并发限制器统计信息', async () => {
      // 启动一个长时间运行的操作
      const longOperation = jest.fn().mockImplementation(async () => {
        await new Promise((resolve) => setTimeout(resolve, 100));
        return 'success';
      });

      // 启动操作但不等待完成
      const promise = service.executeWithLimit('stats-test', longOperation, {
        maxConcurrent: 1,
      });

      // 短暂延迟让操作开始
      await new Promise((resolve) => setTimeout(resolve, 10));

      const stats = service.getStats();

      expect(stats['stats-test']).toBeDefined();
      expect(stats['stats-test'].active).toBeGreaterThan(0);

      // 等待操作完成
      await promise;
    });

    it('应该返回空统计信息当没有限制器时', () => {
      const stats = service.getStats();
      expect(stats).toEqual({});
    });
  });

  describe('clearLimiter', () => {
    it('应该清理指定的限制器', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');

      // 创建一个限制器
      await service.executeWithLimit('clear-test', mockOperation, {
        maxConcurrent: 1,
      });

      let stats = service.getStats();
      expect(stats['clear-test']).toBeDefined();

      // 清理限制器
      service.clearLimiter('clear-test');

      stats = service.getStats();
      expect(stats['clear-test']).toBeUndefined();
    });

    it('应该安全处理不存在的限制器', () => {
      expect(() => service.clearLimiter('nonexistent')).not.toThrow();
    });
  });

  describe('clearAllLimiters', () => {
    it('应该清理所有限制器', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');

      // 创建多个限制器
      await Promise.all([
        service.executeWithLimit('test1', mockOperation, { maxConcurrent: 1 }),
        service.executeWithLimit('test2', mockOperation, { maxConcurrent: 1 }),
      ]);

      let stats = service.getStats();
      expect(Object.keys(stats)).toHaveLength(2);

      // 清理所有限制器
      service.clearAllLimiters();

      stats = service.getStats();
      expect(stats).toEqual({});
    });
  });

  describe('边界条件', () => {
    it('应该处理空数组的批量操作', async () => {
      const operation = jest.fn();

      const results = await service.executeBatch('empty-batch', [], operation, {
        maxConcurrent: 2,
      });

      expect(results).toEqual([]);
      expect(operation).not.toHaveBeenCalled();
    });

    it('应该处理并发数为1的情况', async () => {
      let concurrentCount = 0;
      let maxConcurrentCount = 0;

      const operation = jest.fn().mockImplementation(async (item: number) => {
        concurrentCount++;
        maxConcurrentCount = Math.max(maxConcurrentCount, concurrentCount);
        await new Promise((resolve) => setTimeout(resolve, 20));
        concurrentCount--;
        return item;
      });

      await service.executeBatch(
        'single-concurrent',
        [1, 2, 3, 4, 5],
        operation,
        { maxConcurrent: 1 },
      );

      expect(maxConcurrentCount).toBe(1);
    });

    it('应该处理极大的并发数', async () => {
      const items = Array.from({ length: 5 }, (_, i) => i);
      const operation = jest
        .fn()
        .mockImplementation((item: number) => Promise.resolve(item));

      const results = await service.executeBatch(
        'large-concurrent',
        items,
        operation,
        { maxConcurrent: 1000 }, // 远大于实际需要的并发数
      );

      expect(results).toEqual([0, 1, 2, 3, 4]);
    });
  });
});
