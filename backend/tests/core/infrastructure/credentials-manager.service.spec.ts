import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { CredentialsManagerService } from '../../../src/core/infrastructure/credentials-manager.service';
import { AccountDataService } from '../../../src/common/services/account-data.service';

describe('CredentialsManagerService', () => {
  let service: CredentialsManagerService;
  let mockAccountDataService: jest.Mocked<AccountDataService>;

  // 测试数据
  const mockAccounts = [
    {
      apiKey: 'test-api-key-1',
      secret: 'test-secret-1',
      email: '<EMAIL>',
      password: 'test-password-1',
    },
    {
      apiKey: 'test-api-key-2',
      secret: 'test-secret-2',
      email: '<EMAIL>',
      password: 'test-password-2',
    },
  ];

  beforeEach(async () => {
    // 创建模拟的 AccountDataService
    mockAccountDataService = {
      getAccounts: jest.fn(),
      findAccountByApiKey: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CredentialsManagerService,
        {
          provide: AccountDataService,
          useValue: mockAccountDataService,
        },
      ],
    }).compile();

    service = module.get<CredentialsManagerService>(CredentialsManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    // 清理环境变量
    delete process.env.INNER_DISK_API_KEY;
    delete process.env.INNER_DISK_SECRET;
  });

  describe('getInnerDiskCredentials', () => {
    it('应该返回环境变量中的凭据（优先级最高）', () => {
      // 设置环境变量
      process.env.INNER_DISK_API_KEY = 'env-api-key';
      process.env.INNER_DISK_SECRET = 'env-secret';

      const credentials = service.getInnerDiskCredentials();

      expect(credentials).toEqual({
        apiKey: 'env-api-key',
        secret: 'env-secret',
      });
      expect(mockAccountDataService.getAccounts).not.toHaveBeenCalled();
    });

    it('应该从 AccountDataService 获取凭据（环境变量不存在时）', () => {
      // 不设置环境变量
      mockAccountDataService.getAccounts.mockReturnValue(mockAccounts);

      const credentials = service.getInnerDiskCredentials();

      expect(credentials).toEqual({
        apiKey: 'test-api-key-1',
        secret: 'test-secret-1',
      });
      expect(mockAccountDataService.getAccounts).toHaveBeenCalledTimes(1);
    });

    it('应该在没有凭据时抛出异常', () => {
      mockAccountDataService.getAccounts.mockReturnValue([]);

      expect(() => service.getInnerDiskCredentials()).toThrow(
        BadRequestException,
      );
      expect(() => service.getInnerDiskCredentials()).toThrow(
        /未找到有效的内盘凭据/,
      );
    });

    it('应该处理部分环境变量的情况', () => {
      // 只设置一个环境变量
      process.env.INNER_DISK_API_KEY = 'env-api-key';
      // INNER_DISK_SECRET 未设置

      mockAccountDataService.getAccounts.mockReturnValue(mockAccounts);

      const credentials = service.getInnerDiskCredentials();

      expect(credentials).toEqual({
        apiKey: 'test-api-key-1',
        secret: 'test-secret-1',
      });
      expect(mockAccountDataService.getAccounts).toHaveBeenCalledTimes(1);
    });
  });

  describe('getInnerDiskTradeCredentials', () => {
    it('应该返回正确的交易凭据', () => {
      process.env.INNER_DISK_API_KEY = 'trade-api-key';
      process.env.INNER_DISK_SECRET = 'trade-secret';

      const credentials = service.getInnerDiskTradeCredentials();

      expect(credentials).toEqual({
        apiKey: 'trade-api-key',
        secret: 'trade-secret',
      });
    });
  });

  describe('validateCredentials', () => {
    it('应该验证有效凭据', () => {
      const validCredentials = { apiKey: 'test-key', secret: 'test-secret' };

      expect(service.validateCredentials(validCredentials)).toBe(true);
    });

    it('应该拒绝无效凭据', () => {
      const testCases = [
        null,
        undefined,
        { apiKey: '', secret: 'test-secret' },
        { apiKey: 'test-key', secret: '' },
        { apiKey: null, secret: 'test-secret' },
        { apiKey: 'test-key', secret: null },
        {},
      ];

      testCases.forEach((credentials) => {
        expect(service.validateCredentials(credentials as any)).toBe(false);
      });
    });
  });

  describe('getCredentialsByApiKey', () => {
    it('应该根据 API Key 返回对应凭据', () => {
      const testApiKey = 'test-api-key-1';
      mockAccountDataService.getAccounts.mockReturnValue(mockAccounts);

      const credentials = service.getCredentialsByApiKey(testApiKey);

      expect(credentials).toEqual({
        apiKey: 'test-api-key-1',
        secret: 'test-secret-1',
      });
    });

    it('应该在找不到 API Key 时返回 null', () => {
      const unknownApiKey = 'unknown-api-key';
      mockAccountDataService.getAccounts.mockReturnValue(mockAccounts);

      const credentials = service.getCredentialsByApiKey(unknownApiKey);

      expect(credentials).toBeNull();
    });

    it('应该在账户列表为空时返回 null', () => {
      const testApiKey = 'test-api-key-1';
      mockAccountDataService.getAccounts.mockReturnValue([]);

      const credentials = service.getCredentialsByApiKey(testApiKey);

      expect(credentials).toBeNull();
    });
  });

  describe('边界条件测试', () => {
    it('应该处理极长的凭据字符串', () => {
      const longApiKey = 'a'.repeat(1000);
      const longSecret = 'b'.repeat(1000);

      process.env.INNER_DISK_API_KEY = longApiKey;
      process.env.INNER_DISK_SECRET = longSecret;

      const credentials = service.getInnerDiskCredentials();

      expect(credentials.apiKey).toBe(longApiKey);
      expect(credentials.secret).toBe(longSecret);
    });

    it('应该处理特殊字符', () => {
      const specialApiKey = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const specialSecret = '特殊字符测试-123';

      process.env.INNER_DISK_API_KEY = specialApiKey;
      process.env.INNER_DISK_SECRET = specialSecret;

      const credentials = service.getInnerDiskCredentials();

      expect(credentials.apiKey).toBe(specialApiKey);
      expect(credentials.secret).toBe(specialSecret);
    });
  });

  describe('性能测试', () => {
    it('应该在大量账户中快速查找', () => {
      // 创建大量测试账户
      const largeAccountList = Array.from({ length: 1000 }, (_, i) => ({
        apiKey: `api-key-${i}`,
        secret: `secret-${i}`,
        email: `user${i}@example.com`,
        password: `password-${i}`,
      }));

      mockAccountDataService.getAccounts.mockReturnValue(largeAccountList);

      const startTime = Date.now();
      const credentials = service.getCredentialsByApiKey('api-key-500');
      const endTime = Date.now();

      expect(credentials).toEqual({
        apiKey: 'api-key-500',
        secret: 'secret-500',
      });
      // 查找应该在合理时间内完成（< 10ms）
      expect(endTime - startTime).toBeLessThan(10);
    });

    it('应该快速处理多次凭据验证', () => {
      const testCredentials = { apiKey: 'test-key', secret: 'test-secret' };

      const startTime = Date.now();
      for (let i = 0; i < 1000; i++) {
        service.validateCredentials(testCredentials);
      }
      const endTime = Date.now();

      // 1000次验证应该在合理时间内完成（< 50ms）
      expect(endTime - startTime).toBeLessThan(50);
    });
  });

  describe('错误处理', () => {
    it('应该处理 AccountDataService 抛出的异常', () => {
      mockAccountDataService.getAccounts.mockImplementation(() => {
        throw new Error('数据库连接失败');
      });

      expect(() => service.getInnerDiskCredentials()).toThrow('数据库连接失败');
    });

    it('应该处理 undefined 返回值', () => {
      mockAccountDataService.getAccounts.mockReturnValue(undefined as any);

      expect(() => service.getInnerDiskCredentials()).toThrow();
    });
  });
});
