import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CoreModule } from './core/core.module';
import { AuthModule } from './modules/auth/auth.module';
import { BalanceModule } from './modules/balance/balance.module';
import { UsersModule } from './modules/users/users.module';
import { RandomLightModule } from './modules/random-light/random-light.module';
import { InnerDiskNewCoinModule } from './modules/inner-disk-new-coin/inner-disk-new-coin.module';
// 交易相关模块
import { InnerDiskModule } from './modules/trading/inner-disk/inner-disk.module';
import { RandomPoolTradeModule } from './modules/trading/random-pool/random-pool-trade.module';
import { BatchModule } from './modules/trading/batch-trading/batch.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '../.env'], // 先尝试当前目录，再尝试上级目录
    }),
    // 核心基础设施
    CoreModule,

    // 业务模块
    AuthModule,
    BalanceModule,
    UsersModule,
    RandomLightModule,
    InnerDiskNewCoinModule,

    // 交易模块
    InnerDiskModule,
    RandomPoolTradeModule,
    BatchModule,
  ],
})
export class AppModule {}
