import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);

    // 应用全局异常过滤器
    app.useGlobalFilters(new GlobalExceptionFilter());
    logger.log('Global exception filter applied');

    // 应用全局响应拦截器
    app.useGlobalInterceptors(new ResponseInterceptor());
    logger.log('Global response interceptor applied');

    // 启用全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );
    logger.log('Global validation pipe configured');

    // 启用CORS - 配置生产环境域名
    app.enableCors({
      origin: [
        'http://localhost:3000',
        'http://localhost:5173',
        'https://adminapi.binineex.com',
        'http://adminapi.binineex.com',
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    });
    logger.log('CORS configuration applied');

    // 设置全局前缀
    app.setGlobalPrefix('api', {
      exclude: ['/health', '/'],
    });
    logger.log('Global API prefix set to /api');

    const port = process.env.PORT ?? 3000;
    await app.listen(port);

    logger.log(`🚀 Application is running on: http://localhost:${port}`);
    logger.log(
      `📚 API documentation available at: http://localhost:${port}/api`,
    );
    logger.log(`🏥 Health check available at: http://localhost:${port}/health`);
  } catch (error) {
    logger.error('Failed to start application:', error);
    process.exit(1);
  }
}
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('Application failed to start', error);
  process.exit(1);
});
