import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * 标准化错误响应接口
 */
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
    method: string;
    requestId?: string;
  };
}

/**
 * 全局异常过滤器
 * 统一处理应用中的所有异常，提供标准化的错误响应格式
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const { status, errorCode, message, details } =
      this.parseException(exception);

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: errorCode,
        message,
        details,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        requestId: this.generateRequestId(),
      },
    };

    // 记录错误日志
    this.logError(exception, request, errorResponse);

    response.status(status).json(errorResponse);
  }

  /**
   * 解析异常信息
   */
  private parseException(exception: unknown): {
    status: number;
    errorCode: string;
    message: string;
    details?: unknown;
  } {
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const response = exception.getResponse();

      return {
        status,
        errorCode: this.getErrorCode(status),
        message:
          typeof response === 'string'
            ? response
            : ((response as Record<string, unknown>).message as string) ||
              exception.message,
        details: typeof response === 'object' ? response : undefined,
      };
    }

    // 处理数据库错误
    if (this.isDatabaseError(exception)) {
      return this.parseDatabaseError(exception as Error);
    }

    // 处理验证错误
    if (this.isValidationError(exception)) {
      return this.parseValidationError(exception as Error);
    }

    // 未知错误
    return {
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      errorCode: 'INTERNAL_SERVER_ERROR',
      message: 'Internal server error',
      details:
        process.env.NODE_ENV === 'development'
          ? (exception as Error).stack
          : undefined,
    };
  }

  /**
   * 根据HTTP状态码获取错误代码
   */
  private getErrorCode(status: number): string {
    const errorCodes: Record<number, string> = {
      400: 'BAD_REQUEST',
      401: 'UNAUTHORIZED',
      403: 'FORBIDDEN',
      404: 'NOT_FOUND',
      409: 'CONFLICT',
      422: 'UNPROCESSABLE_ENTITY',
      429: 'TOO_MANY_REQUESTS',
      500: 'INTERNAL_SERVER_ERROR',
      502: 'BAD_GATEWAY',
      503: 'SERVICE_UNAVAILABLE',
    };

    return errorCodes[status] || 'UNKNOWN_ERROR';
  }

  /**
   * 检查是否为数据库错误
   */
  private isDatabaseError(exception: unknown): boolean {
    return (
      exception instanceof Error &&
      (exception.name.includes('Prisma') ||
        exception.message.includes('database') ||
        exception.message.includes('connection'))
    );
  }

  /**
   * 解析数据库错误
   */
  private parseDatabaseError(exception: Error): {
    status: number;
    errorCode: string;
    message: string;
    details?: any;
  } {
    this.logger.error('Database error:', exception);

    return {
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      errorCode: 'DATABASE_ERROR',
      message: 'Database operation failed',
      details:
        process.env.NODE_ENV === 'development' ? exception.message : undefined,
    };
  }

  /**
   * 检查是否为验证错误
   */
  private isValidationError(exception: unknown): boolean {
    return (
      exception instanceof Error &&
      (exception.name.includes('Validation') ||
        exception.message.includes('validation') ||
        exception.message.includes('invalid'))
    );
  }

  /**
   * 解析验证错误
   */
  private parseValidationError(exception: Error): {
    status: number;
    errorCode: string;
    message: string;
    details?: any;
  } {
    return {
      status: HttpStatus.BAD_REQUEST,
      errorCode: 'VALIDATION_ERROR',
      message: 'Validation failed',
      details: exception.message,
    };
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    _request: Request,
    errorResponse: ErrorResponse,
  ): void {
    const { error } = errorResponse;
    const logMessage = `${error.method} ${error.path} - ${error.code}: ${error.message}`;

    if (
      error.code === 'INTERNAL_SERVER_ERROR' ||
      error.code === 'DATABASE_ERROR'
    ) {
      this.logger.error(
        logMessage,
        exception instanceof Error ? exception.stack : exception,
      );
    } else {
      this.logger.warn(logMessage);
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
