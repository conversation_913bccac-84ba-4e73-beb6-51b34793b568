import { Module, Global } from '@nestjs/common';
import { AccountDataService } from './services/account-data.service';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { ResponseInterceptor } from './interceptors/response.interceptor';

@Global()
@Module({
  providers: [AccountDataService, GlobalExceptionFilter, ResponseInterceptor],
  exports: [AccountDataService, GlobalExceptionFilter, ResponseInterceptor],
})
export class CommonModule {}
