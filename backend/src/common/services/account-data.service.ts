import { Injectable, Logger } from '@nestjs/common';
import { AccountData } from '../../core/shared/common.types';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class AccountDataService {
  private readonly logger = new Logger(AccountDataService.name);
  private accountsCache: AccountData[] | null = null;

  /**
   * 获取所有账户数据
   */
  getAccounts(): AccountData[] {
    if (this.accountsCache) {
      return this.accountsCache;
    }

    try {
      // 尝试多个可能的路径
      const possiblePaths = [
        path.join(process.cwd(), 'assets/data/accounts.json'),
        path.join(process.cwd(), '../assets/data/accounts.json'),
        path.join(__dirname, '../../../assets/data/accounts.json'),
        path.join(__dirname, '../../../../assets/data/accounts.json'),
      ];

      let accountsPath: string | null = null;
      for (const filePath of possiblePaths) {
        if (fs.existsSync(filePath)) {
          accountsPath = filePath;
          break;
        }
      }

      if (!accountsPath) {
        throw new Error(
          `无法找到 accounts.json 文件，尝试的路径: ${possiblePaths.join(', ')}`,
        );
      }

      const fileContent = fs.readFileSync(accountsPath, 'utf8');
      this.accountsCache = JSON.parse(fileContent) as AccountData[];
      this.logger.log(`成功加载 ${this.accountsCache.length} 个账户数据`);

      return this.accountsCache;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`读取账户数据失败: ${errorMessage}`);
      throw new Error(`无法读取账户数据: ${errorMessage}`);
    }
  }

  /**
   * 根据 API Key 查找账户
   */
  findAccountByApiKey(apiKey: string): AccountData | undefined {
    return this.getAccounts().find((acc) => acc.apiKey === apiKey);
  }

  /**
   * 获取有效的登录凭据映射
   */
  getValidCredentials(): Record<string, string> {
    const credentials: Record<string, string> = {};
    this.getAccounts().forEach((account) => {
      credentials[account.apiKey] = account.secret;
    });
    return credentials;
  }

  /**
   * 验证账户凭据
   */
  validateCredentials(apiKey: string, secret: string): boolean {
    const account = this.findAccountByApiKey(apiKey);
    return account ? account.secret === secret : false;
  }

  /**
   * 获取账户统计信息
   */
  getAccountStats() {
    const accounts = this.getAccounts();
    return {
      total: accounts.length,
      withEmail: accounts.filter((acc) => acc.email).length,
      withPassword: accounts.filter((acc) => acc.password).length,
    };
  }
}
