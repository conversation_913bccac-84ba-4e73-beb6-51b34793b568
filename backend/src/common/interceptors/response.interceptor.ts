import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * 标准化成功响应接口
 */
export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  meta: {
    timestamp: string;
    path: string;
    method: string;
    requestId: string;
    duration: number;
  };
}

/**
 * 兼容旧格式的响应接口
 */
export interface LegacyResponse {
  code: number;
  msg: string;
  data: any;
}

/**
 * 联合响应类型
 */
export type ApiResponseType<T> = SuccessResponse<T> | LegacyResponse;

/**
 * 全局响应拦截器
 * 统一处理所有成功响应，提供标准化的响应格式和请求追踪
 * 兼容现有的 { code, msg, data } 格式
 */
@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ApiResponseType<T>>
{
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiResponseType<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const startTime = Date.now();
    const requestId = this.generateRequestId();

    // 将请求ID添加到请求对象中，供其他地方使用
    (request as Request & { requestId: string }).requestId = requestId;

    // 记录请求开始
    this.logRequest(request, requestId);

    return next.handle().pipe(
      map((data: T) => {
        const duration = Date.now() - startTime;

        // 如果返回的数据已经是标准格式（包含 code 字段），直接返回
        if (data && typeof data === 'object' && 'code' in data) {
          return data as unknown as LegacyResponse;
        }

        // 构建标准化响应
        const successResponse: SuccessResponse<T> = {
          success: true,
          data,
          meta: {
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method,
            requestId,
            duration,
          },
        };

        return successResponse;
      }),
      tap((responseData) => {
        // 记录响应完成
        this.logResponse(request, response, responseData, requestId);
      }),
    );
  }

  /**
   * 记录请求信息
   */
  private logRequest(request: Request, requestId: string): void {
    const { method, url } = request;

    // 简化日志，只记录基本信息
    this.logger.log(`[${requestId}] ${method} ${url}`);

    // 仅在开发环境且有请求体时记录详细信息
    if (process.env.NODE_ENV === 'development' && request.body) {
      const sanitizedBody = this.sanitizeRequestBody(request.body);
      if (Object.keys(sanitizedBody).length > 0) {
        this.logger.debug(`[${requestId}] Request Body:`, sanitizedBody);
      }
    }
  }

  /**
   * 记录响应信息
   */
  private logResponse(
    request: Request,
    response: Response,
    responseData: ApiResponseType<any>,
    requestId: string,
  ): void {
    const { method, url } = request;
    const { statusCode } = response;

    // 获取持续时间，兼容不同的响应格式
    let duration = 0;
    if ('meta' in responseData && responseData.meta) {
      duration = responseData.meta.duration;
    }

    this.logger.log(
      `[${requestId}] ${method} ${url} - ${statusCode} - ${duration}ms`,
    );

    // 记录慢请求
    if (duration > 1000) {
      this.logger.warn(
        `[${requestId}] Slow request detected: ${method} ${url} took ${duration}ms`,
      );
    }
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeRequestBody(body: unknown): Record<string, unknown> {
    if (!body || typeof body !== 'object') {
      return {};
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'apiKey',
      'authorization',
      'cookie',
    ];

    const sanitized = { ...body };

    const sanitizeObject = (obj: unknown): unknown => {
      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      if (obj && typeof obj === 'object') {
        const result: Record<string, unknown> = {};
        for (const [key, value] of Object.entries(obj)) {
          const lowerKey = key.toLowerCase();
          if (sensitiveFields.some((field) => lowerKey.includes(field))) {
            result[key] = '[REDACTED]';
          } else {
            result[key] = sanitizeObject(value);
          }
        }
        return result;
      }

      return obj;
    };

    return sanitizeObject(sanitized) as Record<string, unknown>;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * 响应格式化工具类
 * 提供手动创建标准化响应的工具方法
 */
export class ResponseFormatter {
  /**
   * 创建成功响应
   */
  static success<T>(
    data: T,
    meta?: Partial<SuccessResponse<T>['meta']>,
  ): SuccessResponse<T> {
    return {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        path: '',
        method: '',
        requestId: '',
        duration: 0,
        ...meta,
      },
    };
  }

  /**
   * 创建分页响应
   */
  static paginated<T>(
    data: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    },
    meta?: Partial<SuccessResponse<any>['meta']>,
  ): SuccessResponse<{
    items: T[];
    pagination: typeof pagination;
  }> {
    return ResponseFormatter.success(
      {
        items: data,
        pagination,
      },
      meta,
    );
  }
}
