import { Module, Global } from '@nestjs/common';
import { ConfigurationService } from './config/configuration.service';
import { ConcurrencyControlService } from './infrastructure/concurrency-control.service';
import { CredentialsManagerService } from './infrastructure/credentials-manager.service';
import { BusinessLoggerService } from './infrastructure/business-logger.service';
import { DatabaseModule } from './database/database.module';

/**
 * 核心模块 - 提供全局基础设施服务
 */
@Global()
@Module({
  imports: [DatabaseModule],
  providers: [
    ConfigurationService,
    ConcurrencyControlService,
    CredentialsManagerService,
    BusinessLoggerService,
  ],
  exports: [
    ConfigurationService,
    ConcurrencyControlService,
    CredentialsManagerService,
    BusinessLoggerService,
    DatabaseModule,
  ],
})
export class CoreModule {}
