import { Logger } from '@nestjs/common';
import { InnerDiskTradeService } from '../../modules/trading/inner-disk/services/inner-disk-trade.service';
import { InnerDiskTradeType } from '../../modules/trading/inner-disk/types/inner-disk.types';

/**
 * 交易操作结果
 */
export interface TradeOperationResult {
  success: boolean;
  message: string;
  transactionId?: string;
}

/**
 * 交易参数
 */
export interface TradeParams {
  poolId: number;
  amount: number;
  tradeType: InnerDiskTradeType;
}

/**
 * 基础交易服务抽象类
 * 提供通用的交易执行逻辑
 */
export abstract class BaseTradeService {
  protected readonly logger: Logger;

  constructor(
    protected readonly innerDiskTradeService: InnerDiskTradeService,
    loggerName: string,
  ) {
    this.logger = new Logger(loggerName);
  }

  /**
   * 执行交易操作
   * @param params 交易参数
   * @returns 交易结果
   */
  protected async executeTradeOperation(
    params: TradeParams,
  ): Promise<TradeOperationResult> {
    const { poolId, amount, tradeType } = params;

    this.logger.debug(
      `开始执行${tradeType}操作: poolId=${poolId}, amount=${amount}`,
    );

    try {
      const tradeResult =
        await this.innerDiskTradeService.executeInnerDiskTradeWithDefaults([
          {
            poolId,
            amount: amount.toString(),
            tradeType,
          },
        ]);

      const success = tradeResult.code === 200;

      this.logger.log(
        `${tradeType}操作${success ? '成功' : '失败'}: poolId=${poolId}, amount=${amount}, message=${tradeResult.msg}`,
      );

      return {
        success,
        message: tradeResult.msg || (success ? '交易成功' : '交易失败'),
        transactionId: success ? JSON.stringify(tradeResult.data) : undefined,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      this.logger.error(
        `${tradeType}操作异常: poolId=${poolId}, amount=${amount}, error=${errorMessage}`,
      );

      return {
        success: false,
        message: String(errorMessage),
      };
    }
  }

  /**
   * 执行买入操作
   * @param poolId 池子ID
   * @param amount 买入金额
   * @returns 交易结果
   */
  protected async executeBuyTrade(
    poolId: number,
    amount: number,
  ): Promise<TradeOperationResult> {
    return this.executeTradeOperation({
      poolId,
      amount,
      tradeType: 'BUY',
    });
  }

  /**
   * 执行卖出操作
   * @param poolId 池子ID
   * @param amount 卖出金额
   * @returns 交易结果
   */
  protected async executeSellTrade(
    poolId: number,
    amount: number,
  ): Promise<TradeOperationResult> {
    return this.executeTradeOperation({
      poolId,
      amount,
      tradeType: 'SELL',
    });
  }

  /**
   * 批量执行交易操作
   * @param trades 交易参数数组
   * @returns 交易结果数组
   */
  protected async executeBatchTrades(
    trades: TradeParams[],
  ): Promise<TradeOperationResult[]> {
    const results: TradeOperationResult[] = [];

    for (const trade of trades) {
      const result = await this.executeTradeOperation(trade);
      results.push(result);

      // 如果交易失败，可以选择继续或停止
      if (!result.success) {
        this.logger.warn(
          `批量交易中的单个交易失败: poolId=${trade.poolId}, type=${trade.tradeType}`,
        );
      }
    }

    return results;
  }

  /**
   * 验证交易参数
   * @param params 交易参数
   * @throws 如果参数无效则抛出错误
   */
  protected validateTradeParams(params: TradeParams): void {
    if (params.poolId <= 0) {
      throw new Error('池子ID必须大于0');
    }
    if (params.amount <= 0) {
      throw new Error('交易金额必须大于0');
    }
    if (!['BUY', 'SELL'].includes(params.tradeType)) {
      throw new Error('交易类型必须是BUY或SELL');
    }
  }
}
