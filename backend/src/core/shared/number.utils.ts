/**
 * 数字处理工具类
 * 提供统一的数字格式化、精度处理等方法
 */
export class NumberUtils {
  /**
   * 将数字四舍五入到指定小数位数
   * @param num 要处理的数字
   * @param precision 小数位数，默认6位
   * @returns 四舍五入后的数字
   */
  static roundToDecimal(num: number, precision: number = 6): number {
    if (typeof num !== 'number' || isNaN(num)) {
      return 0;
    }
    return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision);
  }

  /**
   * 将数字格式化为余额显示
   * @param balance 余额数字
   * @param precision 小数位数，默认6位
   * @returns 格式化后的余额
   */
  static formatBalance(balance: number, precision: number = 6): number {
    return this.roundToDecimal(balance, precision);
  }

  /**
   * 将数字格式化为货币格式
   * @param num 要格式化的数字
   * @param precision 小数位数，默认2位
   * @returns 格式化后的货币字符串
   */
  static formatCurrency(num: number, precision: number = 2): string {
    return this.roundToDecimal(num, precision).toFixed(precision);
  }

  /**
   * 将数字格式化为百分比
   * @param num 要格式化的数字
   * @param precision 小数位数，默认1位
   * @returns 格式化后的百分比字符串
   */
  static formatPercentage(num: number, precision: number = 1): string {
    return `${this.roundToDecimal(num, precision).toFixed(precision)}%`;
  }

  /**
   * 验证数字是否有效
   * @param num 要验证的数字
   * @returns 是否为有效数字
   */
  static isValidNumber(num: any): num is number {
    return typeof num === 'number' && !isNaN(num) && isFinite(num);
  }

  /**
   * 安全地将字符串转换为数字
   * @param str 要转换的字符串
   * @param defaultValue 默认值，默认为0
   * @returns 转换后的数字
   */
  static safeParseNumber(
    str: string | number,
    defaultValue: number = 0,
  ): number {
    if (typeof str === 'number') {
      return this.isValidNumber(str) ? str : defaultValue;
    }
    const num = parseFloat(str);
    return this.isValidNumber(num) ? num : defaultValue;
  }

  /**
   * 计算两个数字之间的百分比差异
   * @param oldValue 旧值
   * @param newValue 新值
   * @param precision 小数位数，默认2位
   * @returns 百分比差异
   */
  static calculatePercentageChange(
    oldValue: number,
    newValue: number,
    precision: number = 2,
  ): number {
    if (oldValue === 0) {
      return newValue === 0 ? 0 : 100;
    }
    const change = ((newValue - oldValue) / oldValue) * 100;
    return this.roundToDecimal(change, precision);
  }

  /**
   * 将数字限制在指定范围内
   * @param num 要限制的数字
   * @param min 最小值
   * @param max 最大值
   * @returns 限制后的数字
   */
  static clamp(num: number, min: number, max: number): number {
    return Math.min(Math.max(num, min), max);
  }
}
