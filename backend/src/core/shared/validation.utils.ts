import { BadRequestException } from '@nestjs/common';

/**
 * 验证工具类
 * 提供各种通用的验证方法，统一验证逻辑和错误处理
 */
export class ValidationUtils {
  /**
   * 验证数字范围
   * @param value 要验证的值
   * @param min 最小值
   * @param max 最大值
   * @param fieldName 字段名称
   */
  static validateNumberRange(
    value: number,
    min: number,
    max: number,
    fieldName: string,
  ): void {
    if (typeof value !== 'number' || isNaN(value)) {
      throw new BadRequestException(`${fieldName}必须是有效数字`);
    }
    if (value < min || value > max) {
      throw new BadRequestException(
        `${fieldName}必须在 ${min} 到 ${max} 之间`,
      );
    }
  }

  /**
   * 验证正数
   * @param value 要验证的值
   * @param fieldName 字段名称
   * @param allowZero 是否允许为0，默认false
   */
  static validatePositiveNumber(
    value: number,
    fieldName: string,
    allowZero: boolean = false,
  ): void {
    if (typeof value !== 'number' || isNaN(value)) {
      throw new BadRequestException(`${fieldName}必须是有效数字`);
    }
    const minValue = allowZero ? 0 : 0.000001;
    if (value < minValue) {
      throw new BadRequestException(
        `${fieldName}必须${allowZero ? '大于等于0' : '大于0'}`,
      );
    }
  }

  /**
   * 验证最小最大值关系
   * @param min 最小值
   * @param max 最大值
   * @param fieldName 字段名称前缀
   */
  static validateMinMaxRelation(
    min: number,
    max: number,
    fieldName: string,
  ): void {
    this.validatePositiveNumber(min, `${fieldName}最小值`);
    this.validatePositiveNumber(max, `${fieldName}最大值`);
    
    if (min >= max) {
      throw new BadRequestException(`${fieldName}最小值必须小于最大值`);
    }
  }

  /**
   * 验证时间间隔（毫秒）
   * @param minMs 最小间隔
   * @param maxMs 最大间隔
   * @param fieldName 字段名称
   * @param minAllowed 允许的最小间隔，默认1000ms
   */
  static validateTimeInterval(
    minMs: number,
    maxMs: number,
    fieldName: string,
    minAllowed: number = 1000,
  ): void {
    this.validateMinMaxRelation(minMs, maxMs, fieldName);
    
    if (minMs < minAllowed) {
      throw new BadRequestException(
        `${fieldName}最小间隔不能小于${minAllowed / 1000}秒`,
      );
    }
  }

  /**
   * 验证买入金额范围
   * @param minAmount 最小金额
   * @param maxAmount 最大金额
   */
  static validateBuyAmountRange(minAmount: number, maxAmount: number): void {
    this.validateMinMaxRelation(minAmount, maxAmount, '买入金额');
  }

  /**
   * 验证买入次数
   * @param buyCount 买入次数
   */
  static validateBuyCount(buyCount: number): void {
    this.validatePositiveNumber(buyCount, '买入次数');
    
    if (!Number.isInteger(buyCount)) {
      throw new BadRequestException('买入次数必须是整数');
    }
    
    if (buyCount > 100) {
      throw new BadRequestException('买入次数不能超过100次');
    }
  }

  /**
   * 验证交易配置的基础字段
   * @param config 包含基础交易配置的对象
   */
  static validateBasicTradeConfig(config: {
    minBuyAmount: number;
    maxBuyAmount: number;
    buyCount: number;
    minBuyIntervalMs: number;
    maxBuyIntervalMs: number;
    minSellDelayMs: number;
    maxSellDelayMs: number;
  }): void {
    // 验证买入金额
    this.validateBuyAmountRange(config.minBuyAmount, config.maxBuyAmount);
    
    // 验证买入次数
    this.validateBuyCount(config.buyCount);
    
    // 验证买入间隔
    this.validateTimeInterval(
      config.minBuyIntervalMs,
      config.maxBuyIntervalMs,
      '买入间隔',
    );
    
    // 验证卖出延迟
    this.validateTimeInterval(
      config.minSellDelayMs,
      config.maxSellDelayMs,
      '卖出延迟',
    );
  }

  /**
   * 验证字符串不为空
   * @param value 要验证的值
   * @param fieldName 字段名称
   */
  static validateNonEmptyString(value: string, fieldName: string): void {
    if (typeof value !== 'string' || value.trim().length === 0) {
      throw new BadRequestException(`${fieldName}不能为空`);
    }
  }

  /**
   * 验证数组不为空
   * @param array 要验证的数组
   * @param fieldName 字段名称
   */
  static validateNonEmptyArray<T>(array: T[], fieldName: string): void {
    if (!Array.isArray(array) || array.length === 0) {
      throw new BadRequestException(`${fieldName}不能为空`);
    }
  }

  /**
   * 验证对象不为空
   * @param obj 要验证的对象
   * @param fieldName 字段名称
   */
  static validateNonEmptyObject(obj: any, fieldName: string): void {
    if (!obj || typeof obj !== 'object' || Object.keys(obj).length === 0) {
      throw new BadRequestException(`${fieldName}不能为空`);
    }
  }

  /**
   * 验证百分比值
   * @param value 要验证的值
   * @param fieldName 字段名称
   */
  static validatePercentage(value: number, fieldName: string): void {
    this.validateNumberRange(value, 0, 100, fieldName);
  }

  /**
   * 验证邮箱格式
   * @param email 要验证的邮箱
   * @param fieldName 字段名称
   */
  static validateEmail(email: string, fieldName: string = '邮箱'): void {
    this.validateNonEmptyString(email, fieldName);
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new BadRequestException(`${fieldName}格式不正确`);
    }
  }
}