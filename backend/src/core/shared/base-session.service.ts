import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * 基础会话接口
 */
export interface BaseSession {
  sessionId: string;
  isRunning: boolean;
  startTime: number;
  endTime?: number;
  config: unknown;
}

/**
 * 基础统计接口
 */
export interface BaseStatistics {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  successRate: number;
}

/**
 * 基础会话服务抽象类
 * 提供通用的会话管理、定时任务、事件发送等功能
 */
export abstract class BaseSessionService<
  TSession extends BaseSession,
  TConfig,
  TEvent extends {
    type: string;
    timestamp: number;
    sessionId: string;
    data: unknown;
  },
> {
  protected readonly logger: Logger;
  protected currentSession: TSession | null = null;
  protected mainTimer: NodeJS.Timeout | null = null;

  constructor(
    protected readonly eventEmitter: EventEmitter2,
    loggerName: string,
  ) {
    this.logger = new Logger(loggerName);
  }

  /**
   * 启动会话
   */
  protected startSession(config: TConfig): TSession {
    if (this.currentSession?.isRunning) {
      throw new Error('已有运行中的会话，请先停止当前会话');
    }

    this.currentSession = this.createSession(config);
    this.logger.log(`会话已启动，会话ID: ${this.currentSession.sessionId}`);

    this.emitSessionEvent('session_started', {});
    this.startMainLoop();

    return this.currentSession;
  }

  /**
   * 停止会话
   */
  protected async stopSession(): Promise<void> {
    if (!this.currentSession?.isRunning) {
      throw new Error('当前没有运行中的会话');
    }

    this.stopMainLoop();
    
    // 执行清理操作
    try {
      await this.cleanup();
    } catch (error) {
      this.logger.error('清理操作失败:', error);
    }

    this.currentSession.isRunning = false;
    this.currentSession.endTime = Date.now();

    this.logger.log(`会话已停止，会话ID: ${this.currentSession.sessionId}`);
    this.emitSessionEvent('session_stopped', {});
  }

  /**
   * 启动主循环
   */
  protected startMainLoop(): void {
    if (!this.currentSession) return;

    const executeNextOperation = async () => {
      if (!this.currentSession?.isRunning) return;

      try {
        await this.executeOperation();
      } catch (error) {
        this.logger.error('执行操作失败:', error);
        this.emitSessionEvent('error_occurred', {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // 设置下次执行
      if (this.currentSession?.isRunning) {
        this.mainTimer = setTimeout(() => {
          void executeNextOperation();
        }, this.getOperationInterval());
      }
    };

    // 立即执行第一次操作
    void executeNextOperation();
  }

  /**
   * 停止主循环
   */
  protected stopMainLoop(): void {
    if (this.mainTimer) {
      clearTimeout(this.mainTimer);
      this.mainTimer = null;
    }
  }

  /**
   * 发送会话事件
   */
  protected emitSessionEvent(type: string, data: unknown): void {
    if (!this.currentSession) {
      this.logger.debug(`⚠️ 尝试发送事件但会话为空: ${type}`);
      return;
    }

    const event = this.createEvent(type, data);
    const eventName = this.getEventName();
    
    this.logger.debug(`📡 发送事件: ${eventName} -> ${type}`);
    this.eventEmitter.emit(eventName, event);
  }

  /**
   * 获取运行时长
   */
  protected getRunningDuration(): number {
    if (!this.currentSession) return 0;

    const endTime = this.currentSession.endTime || Date.now();
    return endTime - this.currentSession.startTime;
  }

  /**
   * 计算成功率
   */
  protected calculateSuccessRate(successful: number, total: number): number {
    return total > 0 ? (successful / total) * 100 : 0;
  }

  /**
   * 获取会话状态
   */
  protected getSessionStatus() {
    return {
      isRunning: this.currentSession?.isRunning || false,
      sessionId: this.currentSession?.sessionId,
      startTime: this.currentSession?.startTime,
      endTime: this.currentSession?.endTime,
      runningDuration: this.getRunningDuration(),
    };
  }

  /**
   * 验证配置（基础验证，子类可扩展）
   */
  protected validateConfig(config: TConfig): void {
    if (!config) {
      throw new Error('配置不能为空');
    }
  }

  // ========================================
  // 抽象方法 - 子类必须实现
  // ========================================

  /**
   * 创建会话实例
   */
  protected abstract createSession(config: TConfig): TSession;

  /**
   * 执行具体操作
   */
  protected abstract executeOperation(): Promise<void>;

  /**
   * 获取操作间隔时间
   */
  protected abstract getOperationInterval(): number;

  /**
   * 创建事件实例
   */
  protected abstract createEvent(type: string, data: unknown): TEvent;

  /**
   * 获取事件名称
   */
  protected abstract getEventName(): string;

  /**
   * 清理资源（可选实现）
   */
  protected async cleanup(): Promise<void> {
    // 默认实现为空，子类可以重写
  }
}
