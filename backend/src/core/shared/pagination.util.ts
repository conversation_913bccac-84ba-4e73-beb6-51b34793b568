/**
 * 分页工具类
 * 提供统一的分页处理逻辑
 */

export interface PaginationOptions {
  currentPage?: number;
  pageSize?: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface PageFetcher<T> {
  (
    page: number,
    pageSize: number,
  ): Promise<{
    data: T[];
    totalRecords: number;
  }>;
}

export class PaginationUtil {
  /**
   * 标准化分页参数
   * @param options 分页选项
   * @returns 标准化后的分页参数
   */
  static normalizePaginationOptions(options: PaginationOptions = {}): {
    currentPage: number;
    pageSize: number;
    offset: number;
  } {
    const currentPage = Math.max(1, options.currentPage || 1);
    const pageSize = Math.max(1, Math.min(1000, options.pageSize || 20)); // 限制最大1000
    const offset = (currentPage - 1) * pageSize;

    return {
      currentPage,
      pageSize,
      offset,
    };
  }

  /**
   * 创建分页结果
   * @param data 数据数组
   * @param totalRecords 总记录数
   * @param currentPage 当前页码
   * @param pageSize 页大小
   * @returns 分页结果
   */
  static createPaginationResult<T>(
    data: T[],
    totalRecords: number,
    currentPage: number,
    pageSize: number,
  ): PaginationResult<T> {
    const totalPages = Math.ceil(totalRecords / pageSize);

    return {
      data,
      pagination: {
        currentPage,
        pageSize,
        totalPages,
        totalRecords,
        hasNextPage: currentPage < totalPages,
        hasPrevPage: currentPage > 1,
      },
    };
  }

  /**
   * 获取所有分页数据（用于需要完整数据集的场景）
   * @param fetcher 页面获取函数
   * @param pageSize 每页大小，默认100
   * @returns 所有数据的数组
   */
  static async getAllPages<T>(
    fetcher: PageFetcher<T>,
    pageSize: number = 100,
  ): Promise<T[]> {
    const allData: T[] = [];
    let currentPage = 1;
    let hasMore = true;

    while (hasMore) {
      const result = await fetcher(currentPage, pageSize);
      allData.push(...result.data);

      const totalPages = Math.ceil(result.totalRecords / pageSize);
      hasMore = currentPage < totalPages;
      currentPage++;

      // 防止无限循环的安全措施
      if (currentPage > 1000) {
        throw new Error('分页查询超过最大页数限制');
      }
    }

    return allData;
  }

  /**
   * 内存中分页（对于已经在内存中的数据）
   * @param data 完整数据数组
   * @param options 分页选项
   * @returns 分页结果
   */
  static paginateInMemory<T>(
    data: T[],
    options: PaginationOptions = {},
  ): PaginationResult<T> {
    const { currentPage, pageSize, offset } =
      this.normalizePaginationOptions(options);
    const totalRecords = data.length;
    const paginatedData = data.slice(offset, offset + pageSize);

    return this.createPaginationResult(
      paginatedData,
      totalRecords,
      currentPage,
      pageSize,
    );
  }

  /**
   * 计算分页的SQL LIMIT和OFFSET
   * @param currentPage 当前页码
   * @param pageSize 页大小
   * @returns LIMIT和OFFSET值
   */
  static calculateSqlLimitOffset(
    currentPage: number,
    pageSize: number,
  ): {
    limit: number;
    offset: number;
  } {
    const normalizedOptions = this.normalizePaginationOptions({
      currentPage,
      pageSize,
    });
    return {
      limit: normalizedOptions.pageSize,
      offset: normalizedOptions.offset,
    };
  }
}
