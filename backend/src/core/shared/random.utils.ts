import { NumberUtils } from './number.utils';

/**
 * 随机数生成工具类
 * 提供各种随机数生成的通用方法
 */
export class RandomUtils {
  /**
   * 生成指定范围内的随机整数
   * @param min 最小值（包含）
   * @param max 最大值（包含）
   * @returns 随机整数
   */
  static randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 生成指定范围内的随机浮点数
   * @param min 最小值（包含）
   * @param max 最大值（不包含）
   * @param decimals 小数位数，默认2位
   * @returns 随机浮点数
   */
  static randomFloat(min: number, max: number, decimals: number = 2): number {
    const random = Math.random() * (max - min) + min;
    return NumberUtils.roundToDecimal(random, decimals);
  }

  /**
   * 生成随机买入金额
   * @param minAmount 最小金额
   * @param maxAmount 最大金额
   * @returns 随机金额（保留2位小数）
   */
  static randomBuyAmount(minAmount: number, maxAmount: number): number {
    return this.randomFloat(minAmount, maxAmount, 2);
  }

  /**
   * 生成随机时间间隔（毫秒）
   * @param minMs 最小间隔（毫秒）
   * @param maxMs 最大间隔（毫秒）
   * @returns 随机间隔时间
   */
  static randomInterval(minMs: number, maxMs: number): number {
    return this.randomInt(minMs, maxMs);
  }

  /**
   * 生成随机延迟时间（毫秒）
   * @param minDelayMs 最小延迟（毫秒）
   * @param maxDelayMs 最大延迟（毫秒）
   * @returns 随机延迟时间
   */
  static randomDelay(minDelayMs: number, maxDelayMs: number): number {
    return this.randomInt(minDelayMs, maxDelayMs);
  }

  /**
   * 从数组中随机选择一个元素
   * @param array 数组
   * @returns 随机选择的元素
   */
  static randomChoice<T>(array: T[]): T {
    if (array.length === 0) {
      throw new Error('Cannot choose from empty array');
    }
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * 生成随机布尔值
   * @param probability 为true的概率（0-1），默认0.5
   * @returns 随机布尔值
   */
  static randomBoolean(probability: number = 0.5): boolean {
    return Math.random() < probability;
  }

  /**
   * 验证区间配置的有效性
   * @param min 最小值
   * @param max 最大值
   * @param fieldName 字段名称（用于错误信息）
   * @throws 如果配置无效则抛出错误
   */
  static validateRange(min: number, max: number, fieldName: string): void {
    if (min >= max) {
      throw new Error(`${fieldName}的最小值必须小于最大值`);
    }
    if (min < 0) {
      throw new Error(`${fieldName}的最小值不能小于0`);
    }
  }
}
