import { Logger } from '@nestjs/common';
import { BusinessLoggerService } from '../infrastructure/business-logger.service';

export interface OperationContext {
  service: string;
  operation: string;
  startTime?: number;
}

/**
 * 基础业务服务抽象类
 * 提供统一的错误处理、日志记录和操作包装
 */
export abstract class BaseBusinessService {
  protected readonly logger: Logger;
  protected readonly businessLogger?: BusinessLoggerService;

  constructor(serviceName: string, businessLogger?: BusinessLoggerService) {
    this.logger = new Logger(serviceName);
    this.businessLogger = businessLogger;
  }

  /**
   * 安全执行操作并记录日志
   * @param operation 要执行的操作
   * @param context 操作上下文
   * @param defaultValue 失败时的默认返回值
   * @returns 操作结果
   */
  protected async safeExecuteWithLogging<T>(
    operation: () => Promise<T>,
    context: OperationContext,
    defaultValue: T,
  ): Promise<T> {
    const startTime = Date.now();

    try {
      this.logger.debug(`开始执行: ${context.service}.${context.operation}`);

      const result = await operation();
      const duration = Date.now() - startTime;

      this.logger.debug(
        `执行成功: ${context.service}.${context.operation} (${duration}ms)`,
      );

      // 记录API调用成功
      this.businessLogger?.logApiCall({
        service: context.service,
        method: context.operation,
        duration,
        success: true,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      this.logger.error(
        `执行失败: ${context.service}.${context.operation} (${duration}ms) - ${errorMessage}`,
        error,
      );

      // 记录API调用失败
      this.businessLogger?.logApiCall({
        service: context.service,
        method: context.operation,
        duration,
        success: false,
        error: errorMessage,
      });

      return defaultValue;
    }
  }

  /**
   * 安全执行同步操作
   * @param operation 要执行的操作
   * @param context 操作上下文
   * @param defaultValue 失败时的默认返回值
   * @returns 操作结果
   */
  protected safeExecuteSync<T>(
    operation: () => T,
    context: OperationContext,
    defaultValue: T,
  ): T {
    const startTime = Date.now();

    try {
      this.logger.debug(`开始执行: ${context.service}.${context.operation}`);

      const result = operation();
      const duration = Date.now() - startTime;

      this.logger.debug(
        `执行成功: ${context.service}.${context.operation} (${duration}ms)`,
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      this.logger.error(
        `执行失败: ${context.service}.${context.operation} (${duration}ms) - ${errorMessage}`,
        error,
      );

      return defaultValue;
    }
  }

  /**
   * 包装数据库操作
   * @param operation 数据库操作
   * @param operationName 操作名称
   * @param defaultValue 默认返回值
   */
  protected async safeDbOperation<T>(
    operation: () => Promise<T> | T,
    operationName: string,
    defaultValue: T,
  ): Promise<T> {
    return this.safeExecuteWithLogging(
      async () => {
        const result = await operation();
        return result;
      },
      {
        service: this.constructor.name,
        operation: `DB_${operationName}`,
      },
      defaultValue,
    );
  }

  /**
   * 记录业务事件
   * @param eventType 事件类型
   * @param data 事件数据
   */
  protected logBusinessEvent(
    eventType: string,
    data: Record<string, unknown>,
  ): void {
    this.businessLogger?.logBusinessEvent({
      type: eventType,
      service: this.constructor.name,
      data,
    });
  }

  /**
   * 记录服务启动
   * @param config 启动配置
   */
  protected logServiceStart(config?: Record<string, unknown>): void {
    this.businessLogger?.logServiceStart(this.constructor.name, config);
  }

  /**
   * 记录服务停止
   * @param reason 停止原因
   */
  protected logServiceStop(reason?: string): void {
    this.businessLogger?.logServiceStop(this.constructor.name, reason);
  }

  /**
   * 构建操作上下文
   * @param operation 操作名称
   */
  protected createContext(operation: string): OperationContext {
    return {
      service: this.constructor.name,
      operation,
      startTime: Date.now(),
    };
  }

  /**
   * 格式化错误消息
   * @param error 错误对象
   */
  protected formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'Unknown error';
  }

  /**
   * 检查操作是否成功
   * @param result 操作结果
   */
  protected isOperationSuccessful(result: unknown): boolean {
    if (result === null || result === undefined) return false;
    if (typeof result === 'boolean') return result;
    if (typeof result === 'object' && 'success' in result) {
      return Boolean((result as { success: boolean }).success);
    }
    return true;
  }
}
