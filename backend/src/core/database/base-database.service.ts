import { Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';

/**
 * 数据库连接配置接口
 */
export interface DatabaseConfig {
  connectionString?: string;
  options?: Record<string, any>;
}

/**
 * 健康检查结果接口
 */
export interface HealthCheckResult {
  isHealthy: boolean;
  message?: string;
  details?: Record<string, any>;
}

/**
 * 数据库统计信息接口
 */
export interface DatabaseStatistics {
  connectionCount?: number;
  totalQueries?: number;
  activeTransactions?: number;
  uptime?: number;
  [key: string]: any;
}

/**
 * 基础数据库服务抽象类
 * 提供数据库连接、健康检查、统计等通用功能
 */
export abstract class BaseDatabaseService implements OnModuleInit, OnModuleDestroy {
  protected readonly logger: Logger;
  protected isConnected: boolean = false;
  protected connectionStartTime?: number;

  constructor(loggerName: string) {
    this.logger = new Logger(loggerName);
  }

  /**
   * 模块初始化时连接数据库
   */
  async onModuleInit(): Promise<void> {
    try {
      await this.connect();
      this.isConnected = true;
      this.connectionStartTime = Date.now();
      this.logger.log('数据库连接成功');
    } catch (error) {
      this.logger.error('数据库连接失败:', error);
      // 根据具体需求决定是否抛出错误
      // throw error;
    }
  }

  /**
   * 模块销毁时断开数据库连接
   */
  async onModuleDestroy(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.disconnect();
        this.isConnected = false;
        this.logger.log('数据库连接已断开');
      }
    } catch (error) {
      this.logger.error('断开数据库连接失败:', error);
    }
  }

  /**
   * 获取连接状态
   */
  isConnectionHealthy(): boolean {
    return this.isConnected;
  }

  /**
   * 获取连接时长（毫秒）
   */
  getConnectionUptime(): number {
    return this.connectionStartTime ? Date.now() - this.connectionStartTime : 0;
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    try {
      const isHealthy = await this.checkHealth();
      const uptime = this.getConnectionUptime();
      
      return {
        isHealthy,
        message: isHealthy ? '数据库连接正常' : '数据库连接异常',
        details: {
          connected: this.isConnected,
          uptime,
          uptimeFormatted: this.formatUptime(uptime),
        },
      };
    } catch (error) {
      return {
        isHealthy: false,
        message: '健康检查失败',
        details: {
          error: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * 格式化连接时长
   */
  private formatUptime(uptime: number): string {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 安全执行数据库操作
   */
  protected async safeExecute<T>(
    operation: () => Promise<T>,
    operationName: string,
    defaultValue?: T,
  ): Promise<T> {
    try {
      if (!this.isConnected) {
        throw new Error('数据库未连接');
      }
      return await operation();
    } catch (error) {
      this.logger.error(`${operationName}失败:`, error);
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw error;
    }
  }

  /**
   * 记录查询日志
   */
  protected logQuery(query: string, params?: any[], duration?: number): void {
    if (process.env.NODE_ENV === 'development') {
      const message = [`执行查询: ${query}`];
      if (params && params.length > 0) {
        message.push(`参数: ${JSON.stringify(params)}`);
      }
      if (duration !== undefined) {
        message.push(`耗时: ${duration}ms`);
      }
      this.logger.debug(message.join(' | '));
    }
  }

  // ========================================
  // 抽象方法 - 子类必须实现
  // ========================================

  /**
   * 建立数据库连接
   */
  protected abstract connect(): Promise<void>;

  /**
   * 断开数据库连接
   */
  protected abstract disconnect(): Promise<void>;

  /**
   * 检查数据库健康状态
   */
  protected abstract checkHealth(): Promise<boolean>;

  /**
   * 获取数据库统计信息
   */
  abstract getStatistics(): Promise<DatabaseStatistics>;

  // ========================================
  // 可选重写的方法
  // ========================================

  /**
   * 重新连接数据库
   */
  async reconnect(): Promise<void> {
    this.logger.log('尝试重连数据库...');
    try {
      if (this.isConnected) {
        await this.disconnect();
      }
      await this.connect();
      this.isConnected = true;
      this.connectionStartTime = Date.now();
      this.logger.log('数据库重连成功');
    } catch (error) {
      this.logger.error('数据库重连失败:', error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 默认实现为空，子类可以重写
    this.logger.debug('执行数据库清理操作');
  }
}