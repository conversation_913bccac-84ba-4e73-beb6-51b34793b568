import { Module } from '@nestjs/common';
import { DatabaseService } from './database.service';
import { NineLightDatabaseService } from './nine-light-database.service';
import { PersistenceService } from './persistence.service';

/**
 * 数据库模块 - 统一数据访问层
 */
@Module({
  providers: [DatabaseService, NineLightDatabaseService, PersistenceService],
  exports: [DatabaseService, NineLightDatabaseService, PersistenceService],
})
export class DatabaseModule {}
