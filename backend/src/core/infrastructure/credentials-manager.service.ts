import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { ENV_KEYS } from '../config/app.constants';
import { AccountDataService } from '../../common/services/account-data.service';

export interface Credentials {
  apiKey: string;
  secret: string;
}

/**
 * 统一凭据管理服务
 * 负责管理各种服务的API凭据获取逻辑
 */
@Injectable()
export class CredentialsManagerService {
  private readonly logger = new Logger(CredentialsManagerService.name);

  constructor(private readonly accountDataService: AccountDataService) {}

  /**
   * 获取内盘服务凭据
   */
  getInnerDiskCredentials(): Credentials {
    return this.getCredentials('INNER_DISK', {
      envApiKey: ENV_KEYS.INNER_DISK_API_KEY,
      envSecret: ENV_KEYS.INNER_DISK_SECRET,
      serviceName: '内盘',
    });
  }

  /**
   * 获取内盘交易服务凭据
   */
  getInnerDiskTradeCredentials(): Credentials {
    return this.getCredentials('INNER_DISK_TRADE', {
      envApiKey: ENV_KEYS.INNER_DISK_API_KEY,
      envSecret: ENV_KEYS.INNER_DISK_SECRET,
      serviceName: '内盘交易',
    });
  }

  /**
   * 通用凭据获取方法
   */
  private getCredentials(
    _serviceType: string,
    config: {
      envApiKey: string;
      envSecret: string;
      serviceName: string;
    },
  ): Credentials {
    // 优先使用环境变量中的凭据
    const envApiKey = process.env[config.envApiKey];
    const envSecret = process.env[config.envSecret];

    if (envApiKey && envSecret) {
      this.logger.debug(`使用环境变量中的${config.serviceName} API 凭据`);
      return {
        apiKey: envApiKey,
        secret: envSecret,
      };
    }

    // 回退到 AccountDataService 中的账户
    this.logger.debug(
      `环境变量中未找到${config.serviceName} API 凭据，使用 AccountDataService`,
    );
    const accounts = this.accountDataService.getAccounts();
    if (accounts.length === 0) {
      throw new BadRequestException(
        `未找到有效的${config.serviceName}凭据。请在环境变量中设置 ${config.envApiKey} 和 ${config.envSecret}，或确保 AccountDataService 中有可用账户`,
      );
    }

    // 使用第一个可用账户
    const account = accounts[0];
    return {
      apiKey: account.apiKey,
      secret: account.secret,
    };
  }

  /**
   * 验证凭据有效性
   */
  validateCredentials(credentials: any): boolean {
    if (!credentials || typeof credentials !== 'object') {
      return false;
    }

    return (
      typeof credentials.apiKey === 'string' &&
      typeof credentials.secret === 'string' &&
      credentials.apiKey.length > 0 &&
      credentials.secret.length > 0
    );
  }

  /**
   * 根据 API Key 获取对应账户的凭据
   */
  getCredentialsByApiKey(apiKey: string): Credentials | null {
    const accounts = this.accountDataService.getAccounts();
    const account = accounts.find((acc) => acc.apiKey === apiKey);

    if (!account) {
      this.logger.warn(
        `未找到 API Key ${apiKey.substring(0, 8)}... 对应的账户`,
      );
      return null;
    }

    return {
      apiKey: account.apiKey,
      secret: account.secret,
    };
  }
}
