import { Injectable, Logger } from '@nestjs/common';

// 使用 default 导入
import pLimit from 'p-limit';

export interface ConcurrencyConfig {
  maxConcurrent: number;
  timeout?: number;
}

/**
 * 并发控制服务
 * 管理应用程序中的并发操作，防止过载
 */
@Injectable()
export class ConcurrencyControlService {
  private readonly logger = new Logger(ConcurrencyControlService.name);
  private readonly limiters: Map<string, ReturnType<typeof pLimit>> = new Map();

  /**
   * 获取或创建并发限制器
   */
  private getLimiter(
    key: string,
    maxConcurrent: number,
  ): ReturnType<typeof pLimit> {
    if (!this.limiters.has(key)) {
      const limiter = pLimit(maxConcurrent);
      this.limiters.set(key, limiter);
      this.logger.log(`创建并发限制器: ${key}, 最大并发数: ${maxConcurrent}`);
    }
    return this.limiters.get(key)!;
  }

  /**
   * 执行带并发控制的操作
   */
  executeWithLimit<T>(
    key: string,
    operation: () => Promise<T>,
    config: ConcurrencyConfig,
  ): Promise<T> {
    const limiter = this.getLimiter(key, config.maxConcurrent);

    return limiter(async () => {
      const startTime = Date.now();

      try {
        // 如果设置了超时，添加超时控制
        if (config.timeout) {
          return await Promise.race([
            operation(),
            this.createTimeoutPromise<T>(config.timeout),
          ]);
        }

        const result = await operation();
        const duration = Date.now() - startTime;

        this.logger.debug(`并发操作完成: ${key}, 耗时: ${duration}ms`);
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        this.logger.error(`并发操作失败: ${key}, 耗时: ${duration}ms`, error);
        throw error;
      }
    });
  }

  /**
   * 批量执行带并发控制的操作
   */
  async executeBatch<T, R>(
    key: string,
    items: T[],
    operation: (item: T) => Promise<R>,
    config: ConcurrencyConfig,
  ): Promise<R[]> {
    const limiter = this.getLimiter(key, config.maxConcurrent);

    this.logger.log(
      `开始批量执行: ${key}, 项目数: ${items.length}, 并发限制: ${config.maxConcurrent}`,
    );

    const promises = items.map((item) =>
      limiter(async () => {
        try {
          if (config.timeout) {
            return await Promise.race([
              operation(item),
              this.createTimeoutPromise<R>(config.timeout),
            ]);
          }
          return await operation(item);
        } catch (error) {
          this.logger.error(`批量操作项目失败:`, error);
          throw error;
        }
      }),
    );

    const results = await Promise.allSettled(promises);
    const successCount = results.filter((r) => r.status === 'fulfilled').length;
    const failureCount = results.length - successCount;

    this.logger.log(
      `批量执行完成: ${key}, 成功: ${successCount}, 失败: ${failureCount}`,
    );

    // 返回成功的结果，失败的抛出异常
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        throw new Error(`批量操作第${index}项失败: ${result.reason.message}`);
      }
    });
  }

  /**
   * 执行带并发控制的批量操作（容错版本）
   * 失败的操作不会影响其他操作，返回结果包含成功和失败的信息
   */
  async executeBatchTolerant<T, R>(
    key: string,
    items: T[],
    operation: (item: T) => Promise<R>,
    config: ConcurrencyConfig,
  ): Promise<Array<{ success: boolean; result?: R; error?: string; item: T }>> {
    const limiter = this.getLimiter(key, config.maxConcurrent);

    this.logger.log(`开始容错批量执行: ${key}, 项目数: ${items.length}`);

    const promises = items.map((item) =>
      limiter(async () => {
        try {
          let result: R;
          if (config.timeout) {
            result = await Promise.race([
              operation(item),
              this.createTimeoutPromise<R>(config.timeout),
            ]);
          } else {
            result = await operation(item);
          }

          return { success: true, result, item };
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage,
            item,
          };
        }
      }),
    );

    const results = await Promise.all(promises);
    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.length - successCount;

    this.logger.log(
      `容错批量执行完成: ${key}, 成功: ${successCount}, 失败: ${failureCount}`,
    );

    return results;
  }

  /**
   * 获取并发限制器的统计信息
   */
  getStats(): Record<string, { pending: number; active: number }> {
    const stats: Record<string, { pending: number; active: number }> = {};

    for (const [key, limiter] of this.limiters) {
      stats[key] = {
        pending: limiter.pendingCount,
        active: limiter.activeCount,
      };
    }

    return stats;
  }

  /**
   * 清理指定的并发限制器
   */
  clearLimiter(key: string): void {
    if (this.limiters.has(key)) {
      this.limiters.delete(key);
      this.logger.log(`清理并发限制器: ${key}`);
    }
  }

  /**
   * 清理所有并发限制器
   */
  clearAllLimiters(): void {
    this.limiters.clear();
    this.logger.log('清理所有并发限制器');
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`操作超时: ${timeout}ms`));
      }, timeout);
    });
  }
}
