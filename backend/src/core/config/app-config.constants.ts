/**
 * 应用程序配置常量
 * 统一管理所有硬编码的数值和配置项
 */

// 超时配置
export const TIMEOUT_CONFIG = {
  DEFAULT_API_TIMEOUT: 30000, // 30秒
  TRADE_TIMEOUT: 60000, // 1分钟
  DATABASE_TIMEOUT: 10000, // 10秒
  SHORT_TIMEOUT: 5000, // 5秒
} as const;

// 重试配置
export const RETRY_CONFIG = {
  DEFAULT_RETRY_DELAY: 1000, // 1秒
  MAX_RETRY_ATTEMPTS: 3,
  EXPONENTIAL_BACKOFF_BASE: 2,
} as const;

// 批处理配置
export const BATCH_CONFIG = {
  DEFAULT_BATCH_SIZE: 100,
  LARGE_BATCH_SIZE: 500,
  SMALL_BATCH_SIZE: 50,
  MAX_BATCH_SIZE: 1000,
} as const;

// 并发控制配置
export const CONCURRENCY_CONFIG = {
  TRADE_OPERATIONS: 5, // 交易操作最大并发数
  API_CALLS: 10, // API调用最大并发数
  DATABASE_OPERATIONS: 20, // 数据库操作最大并发数
  FILE_OPERATIONS: 3, // 文件操作最大并发数
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 60000, // 1分钟
  SHORT_TTL: 30000, // 30秒
  LONG_TTL: 300000, // 5分钟
  POOL_CACHE_TTL: 60000, // 池子缓存TTL
  MAX_CACHE_SIZE: 1000, // 最大缓存条目数
} as const;

// 数据库配置
export const DATABASE_CONFIG = {
  CONNECTION_TIMEOUT: 10000,
  QUERY_TIMEOUT: 30000,
  AUTO_SAVE_INTERVAL: 30000, // 自动保存间隔
  MAX_QUERY_RETRIES: 3,
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1,
} as const;

// 日志配置
export const LOGGING_CONFIG = {
  MAX_LOG_LENGTH: 1000, // 最大日志长度
  SENSITIVE_FIELDS: [
    'password',
    'secret',
    'apiKey',
    'token',
    'key',
    'credential',
  ],
  EMAIL_MASK_LENGTH: 2, // 邮箱脱敏显示长度
} as const;

// 业务规则配置
export const BUSINESS_RULES = {
  MIN_TRADE_AMOUNT: 0.01, // 最小交易金额
  MAX_TRADE_AMOUNT: 100000, // 最大交易金额
  MIN_BALANCE_THRESHOLD: 1, // 最小余额阈值
  MIN_BUY_INTERVAL_MS: 1000, // 最小买入间隔
  MIN_SELL_DELAY_MS: 1000, // 最小卖出延迟
} as const;

// 系统限制配置
export const SYSTEM_LIMITS = {
  MAX_ACTIVE_SESSIONS: 100, // 最大活跃会话数
  MAX_POOL_COUNT: 1000, // 最大池子数量
  MAX_ACCOUNT_COUNT: 500, // 最大账户数量
  MAX_TRADE_HISTORY: 10000, // 最大交易历史记录数
} as const;

// ERROR_CODES 已移至 app.constants.ts 避免重复定义

// 环境配置
export const ENVIRONMENT_CONFIG = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

/**
 * 获取环境变量配置，支持默认值
 */
export const getEnvConfig = <T>(key: string, defaultValue: T): T => {
  const value = process.env[key];

  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }

  // 尝试解析不同类型
  if (typeof defaultValue === 'number') {
    const numValue = Number(value);
    return isNaN(numValue) ? defaultValue : (numValue as unknown as T);
  }

  if (typeof defaultValue === 'boolean') {
    const boolValue = value.toLowerCase() === 'true';
    return boolValue as unknown as T;
  }

  return value as unknown as T;
};

/**
 * 动态配置，可通过环境变量覆盖
 */
export const getDynamicConfig = () => ({
  timeout: {
    api: getEnvConfig('API_TIMEOUT', TIMEOUT_CONFIG.DEFAULT_API_TIMEOUT),
    trade: getEnvConfig('TRADE_TIMEOUT', TIMEOUT_CONFIG.TRADE_TIMEOUT),
    database: getEnvConfig('DATABASE_TIMEOUT', TIMEOUT_CONFIG.DATABASE_TIMEOUT),
  },
  concurrency: {
    trade: getEnvConfig(
      'TRADE_CONCURRENCY',
      CONCURRENCY_CONFIG.TRADE_OPERATIONS,
    ),
    api: getEnvConfig('API_CONCURRENCY', CONCURRENCY_CONFIG.API_CALLS),
    database: getEnvConfig(
      'DB_CONCURRENCY',
      CONCURRENCY_CONFIG.DATABASE_OPERATIONS,
    ),
  },
  batch: {
    defaultSize: getEnvConfig(
      'DEFAULT_BATCH_SIZE',
      BATCH_CONFIG.DEFAULT_BATCH_SIZE,
    ),
    maxSize: getEnvConfig('MAX_BATCH_SIZE', BATCH_CONFIG.MAX_BATCH_SIZE),
  },
  cache: {
    ttl: getEnvConfig('CACHE_TTL', CACHE_CONFIG.DEFAULT_TTL),
    maxSize: getEnvConfig('CACHE_MAX_SIZE', CACHE_CONFIG.MAX_CACHE_SIZE),
  },
});

// 定义配置接口
interface ConfigToValidate {
  timeout?: { api?: number };
  concurrency?: { trade?: number };
  batch?: { defaultSize?: number; maxSize?: number };
  cache?: { ttl?: number };
}

/**
 * 配置验证器
 */
export const validateConfig = (config: ConfigToValidate): void => {
  if (config.timeout?.api && config.timeout.api <= 0) {
    throw new Error('API timeout must be positive');
  }

  if (config.concurrency?.trade && config.concurrency.trade <= 0) {
    throw new Error('Trade concurrency must be positive');
  }

  if (config.batch?.defaultSize && config.batch?.maxSize) {
    if (
      config.batch.defaultSize <= 0 ||
      config.batch.defaultSize > config.batch.maxSize
    ) {
      throw new Error('Batch size configuration is invalid');
    }
  }

  if (config.cache?.ttl && config.cache.ttl <= 0) {
    throw new Error('Cache TTL must be positive');
  }
};
