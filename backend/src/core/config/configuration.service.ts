import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  TIMEOUT_CONFIG as _TIMEOUT_CONFIG,
  RETRY_CONFIG,
  BATCH_CONFIG as _BATCH_CONFIG,
  CONCURRENCY_CONFIG as _CONCURRENCY_CONFIG,
  CACHE_CONFIG as _CACHE_CONFIG,
  DATABASE_CONFIG,
  BUSINESS_RULES,
  getDynamicConfig,
  validateConfig,
} from './app-config.constants';

export interface ServiceConfig {
  timeout: {
    api: number;
    trade: number;
    database: number;
  };
  retry: {
    delay: number;
    maxAttempts: number;
    backoffBase: number;
  };
  batch: {
    defaultSize: number;
    maxSize: number;
  };
  concurrency: {
    trade: number;
    api: number;
    database: number;
  };
  cache: {
    ttl: number;
    maxSize: number;
  };
}

/**
 * 统一配置管理服务
 * 提供类型安全的配置访问和环境变量覆盖支持
 */
@Injectable()
export class ConfigurationService implements OnModuleInit {
  private readonly logger = new Logger(ConfigurationService.name);
  private config: ServiceConfig;

  constructor() {
    this.loadConfiguration();
  }

  onModuleInit() {
    try {
      validateConfig(this.config);
      this.logger.log('配置验证通过');
      this.logConfigSummary();
    } catch (error) {
      this.logger.error('配置验证失败:', error);
      throw error;
    }
  }

  /**
   * 获取服务配置
   */
  getServiceConfig(serviceName?: string): ServiceConfig {
    if (serviceName) {
      this.logger.debug(`获取服务配置: ${serviceName}`);
    }
    return { ...this.config };
  }

  /**
   * 获取超时配置
   */
  getTimeoutConfig(type: 'api' | 'trade' | 'database' = 'api'): number {
    return this.config.timeout[type];
  }

  /**
   * 获取重试配置
   */
  getRetryConfig(): ServiceConfig['retry'] {
    return { ...this.config.retry };
  }

  /**
   * 获取批处理配置
   */
  getBatchConfig(): ServiceConfig['batch'] {
    return { ...this.config.batch };
  }

  /**
   * 获取并发配置
   */
  getConcurrencyConfig(type: 'trade' | 'api' | 'database' = 'api'): number {
    return this.config.concurrency[type];
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig(): ServiceConfig['cache'] {
    return { ...this.config.cache };
  }

  /**
   * 获取数据库配置
   */
  getDatabaseConfig(): typeof DATABASE_CONFIG {
    return { ...DATABASE_CONFIG };
  }

  /**
   * 获取业务规则配置
   */
  getBusinessRules(): typeof BUSINESS_RULES {
    return { ...BUSINESS_RULES };
  }

  /**
   * 动态更新配置（运行时）
   */
  updateConfig(updates: Partial<ServiceConfig>): void {
    const oldConfig = { ...this.config };

    this.config = {
      ...this.config,
      ...updates,
    };

    try {
      validateConfig(this.config);
      this.logger.log('配置更新成功');
    } catch (error) {
      // 恢复旧配置
      this.config = oldConfig;
      this.logger.error('配置更新失败，已恢复:', error);
      throw error;
    }
  }

  /**
   * 重新加载配置
   */
  reloadConfiguration(): void {
    this.loadConfiguration();
    validateConfig(this.config);
    this.logger.log('配置重新加载成功');
    this.logConfigSummary();
  }

  /**
   * 获取特定服务的推荐配置
   */
  getRecommendedConfig(
    serviceType: 'trade' | 'api' | 'database' | 'batch',
  ): Partial<ServiceConfig> {
    const baseConfig = this.getServiceConfig();

    switch (serviceType) {
      case 'trade':
        return {
          timeout: { ...baseConfig.timeout, api: 60000 }, // 交易操作需要更长超时
          concurrency: { ...baseConfig.concurrency, trade: 3 }, // 交易操作并发数较低
          retry: { ...baseConfig.retry, maxAttempts: 5 }, // 交易操作重试次数更多
        };

      case 'api':
        return {
          timeout: { ...baseConfig.timeout, api: 30000 },
          concurrency: { ...baseConfig.concurrency, api: 10 },
          retry: { ...baseConfig.retry, maxAttempts: 3 },
        };

      case 'database':
        return {
          timeout: { ...baseConfig.timeout, database: 10000 },
          concurrency: { ...baseConfig.concurrency, database: 20 },
          retry: { ...baseConfig.retry, maxAttempts: 2 },
        };

      case 'batch':
        return {
          batch: { defaultSize: 100, maxSize: 500 },
          concurrency: { ...baseConfig.concurrency, api: 5 },
        };

      default:
        return baseConfig;
    }
  }

  /**
   * 配置健康检查
   */
  healthCheck(): { status: 'healthy' | 'warning' | 'error'; issues: string[] } {
    const issues: string[] = [];

    // 检查超时配置
    if (this.config.timeout.api > 60000) {
      issues.push('API超时时间过长，可能影响用户体验');
    }

    // 检查并发配置
    if (this.config.concurrency.trade > 10) {
      issues.push('交易并发数过高，可能导致API限流');
    }

    // 检查批处理配置
    if (this.config.batch.defaultSize > this.config.batch.maxSize) {
      issues.push('默认批处理大小超过最大值');
    }

    const status =
      issues.length === 0 ? 'healthy' : issues.length > 3 ? 'error' : 'warning';

    return { status, issues };
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): void {
    const dynamicConfig = getDynamicConfig();

    this.config = {
      timeout: {
        api: dynamicConfig.timeout.api,
        trade: dynamicConfig.timeout.trade,
        database: dynamicConfig.timeout.database,
      },
      retry: {
        delay: RETRY_CONFIG.DEFAULT_RETRY_DELAY,
        maxAttempts: RETRY_CONFIG.MAX_RETRY_ATTEMPTS,
        backoffBase: RETRY_CONFIG.EXPONENTIAL_BACKOFF_BASE,
      },
      batch: {
        defaultSize: dynamicConfig.batch.defaultSize,
        maxSize: dynamicConfig.batch.maxSize,
      },
      concurrency: {
        trade: dynamicConfig.concurrency.trade,
        api: dynamicConfig.concurrency.api,
        database: dynamicConfig.concurrency.database,
      },
      cache: {
        ttl: dynamicConfig.cache.ttl,
        maxSize: dynamicConfig.cache.maxSize,
      },
    };
  }

  /**
   * 记录配置摘要
   */
  private logConfigSummary(): void {
    this.logger.log(`配置摘要: 
      - API超时: ${this.config.timeout.api}ms
      - 交易并发: ${this.config.concurrency.trade}
      - 批处理大小: ${this.config.batch.defaultSize}
      - 缓存TTL: ${this.config.cache.ttl}ms`);
  }
}
