import {
  Controller,
  Post,
  Body,
  UnauthorizedException,
  Get,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { LoginDto } from '../dto/login.dto';
import { Public } from '../decorators/public.decorator';
import { CurrentUser } from '../decorators/current-user.decorator';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../strategies/jwt.strategy';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 健康检查端点
   */
  @Public()
  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'Nine Light Backend API',
    };
  }

  @Public()
  @Post('login')
  login(@Body() loginDto: LoginDto) {
    const user = this.authService.validateUser(
      loginDto.username,
      loginDto.password,
    );
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return this.authService.login(user);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@CurrentUser() user: User) {
    return {
      success: true,
      user: {
        username: user.username,
        sub: user.sub,
      },
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('verify')
  verifyToken(@CurrentUser() user: User) {
    return {
      success: true,
      message: 'Token is valid',
      user: {
        username: user.username,
        sub: user.sub,
      },
    };
  }
}
