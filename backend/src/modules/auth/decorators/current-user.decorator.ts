import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User } from '../strategies/jwt.strategy';

interface RequestWithUser {
  user: User;
}

/**
 * 从请求中提取当前认证用户信息
 *
 * @example
 * ```typescript
 * @Get('profile')
 * @UseGuards(JwtAuthGuard)
 * getProfile(@CurrentUser() user: User) {
 *   return { user };
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): User => {
    const request = ctx.switchToHttp().getRequest<RequestWithUser>();
    return request.user;
  },
);
