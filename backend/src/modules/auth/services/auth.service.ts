import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { BaseBusinessService } from '../../../core/shared/base-business.service';

@Injectable()
export class AuthService extends BaseBusinessService {
  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {
    super(AuthService.name);
  }

  validateUser(username: string, pass: string): { username: string } | null {
    try {
      if (!username || !pass) {
        this.logger.warn('登录验证失败：用户名或密码为空');
        return null;
      }

      const adminUsername = this.configService.get<string>('ADMIN_USERNAME');
      const adminPassword = this.configService.get<string>('ADMIN_PASSWORD');

      if (!adminUsername || !adminPassword) {
        this.logger.error('系统配置错误：管理员凭据未正确配置');
        return null;
      }

      const isPasswordMatching = pass === adminPassword;
      if (username === adminUsername && isPasswordMatching) {
        this.logger.log(`管理员登录成功：${username}`);
        return { username: adminUsername };
      }

      this.logger.warn(`登录验证失败：用户名或密码错误 - ${username}`);
      return null;
    } catch (error) {
      this.logger.error('验证用户时发生错误', error);
      return null;
    }
  }

  login(user: { username: string }): { access_token: string } {
    try {
      if (!user || !user.username) {
        this.logger.error('登录失败：用户信息无效');
        throw new Error('用户信息无效');
      }

      const payload = { username: user.username, sub: 'admin' };
      const token = this.jwtService.sign(payload);

      this.logger.log(`JWT令牌生成成功：${user.username}`);
      return { access_token: token };
    } catch (error) {
      this.logger.error('生成JWT令牌时发生错误', error);
      throw error;
    }
  }
}
