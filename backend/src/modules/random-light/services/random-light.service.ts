import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

import { RandomUtils } from '../../../core/shared/random.utils';
import { BaseBusinessService } from '../../../core/shared/base-business.service';
import {
  NineBalanceService,
  NineBalanceInfo,
} from '../../balance/services/nine-balance.service';
import {
  LightQueryOptimizerService,
  LightValidationResult,
} from '../../trading/batch-trading/services/light-query-optimizer.service';
import { EnhancedBatchLightCoreService } from '../../trading/batch-trading/services/enhanced-batch-light-core.service';
import { AccountDataService } from '../../../common/services/account-data.service';
import { UserBean } from '../../../core/shared/common.types';
import {
  RandomLightSession,
  RandomLightSessionStatus,
  RandomLightSessionConfig,
  RandomLightExecutionResult,
  RandomLightExecutionStatus,
  RandomLightEvent,
  RandomLightEventType,
  RandomLightEventData,
  CandidateAccount,
  CandidateAsset,
  RandomSelectionResult,
} from '../types/random-light.types';
import {
  RandomLightStatusDto,
  RandomLightStatisticsDto,
  RandomLightExecutionDto,
} from '../dto/random-light.dto';

/**
 * 随机点亮服务
 * 实现自动随机选择账户和资产进行点亮操作
 */
@Injectable()
export class RandomLightService extends BaseBusinessService {
  private currentSession: RandomLightSession | null = null;
  private executionHistory: RandomLightExecutionResult[] = [];
  private readonly maxHistorySize = 1000; // 最大历史记录数

  // 最近使用的账户和资产记录，用于避免重复选择
  private recentlyUsedAccounts = new Map<string, Date>();
  private recentlyUsedAssets = new Map<number, Date>();

  // 缓存数据，避免每次执行都查询数据库
  private cachedAccountsWithBalance: NineBalanceInfo[] = [];
  private cachedLightableTokens: LightValidationResult[] = [];
  private lastCacheUpdate: Date | null = null;
  private readonly CACHE_DURATION_MS = 10 * 60 * 1000; // 10分钟缓存

  constructor(
    private readonly nineBalanceService: NineBalanceService,
    private readonly lightQueryOptimizer: LightQueryOptimizerService,
    private readonly batchLightCore: EnhancedBatchLightCoreService,
    private readonly eventEmitter: EventEmitter2,
    private readonly accountDataService: AccountDataService,
  ) {
    super(RandomLightService.name);
    this.logger.log('RandomLightService 初始化完成');
  }

  /**
   * 获取账户统计信息（用于健康检查）
   */
  getAccountStats() {
    try {
      const accounts = this.accountDataService.getAccounts();
      return {
        totalAccounts: accounts.length,
        accountsLoaded: accounts.length > 0,
        sampleApiKey:
          accounts.length > 0
            ? accounts[0].apiKey.substring(0, 8) + '...'
            : null,
      };
    } catch (error) {
      this.logger.error('获取账户统计信息失败:', error);
      return {
        totalAccounts: 0,
        accountsLoaded: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 缓存管理器 - 统一处理缓存逻辑
   */
  private async ensureCacheValid(): Promise<{
    accounts: NineBalanceInfo[];
    tokens: LightValidationResult[];
  }> {
    const needsRefresh =
      !this.lastCacheUpdate ||
      Date.now() - this.lastCacheUpdate.getTime() > this.CACHE_DURATION_MS;

    if (needsRefresh) {
      try {
        this.logger.debug('刷新缓存数据...');
        const [accounts, tokens] = await Promise.all([
          this.nineBalanceService.getAccountsWithNineBalance(0),
          this.lightQueryOptimizer.getLightableTokens(200),
        ]);

        this.cachedAccountsWithBalance = accounts;
        this.cachedLightableTokens = tokens;
        this.lastCacheUpdate = new Date();

        this.logger.log(
          `缓存刷新完成: ${accounts.length} 个账户, ${tokens.length} 个资产`,
        );
      } catch (error) {
        this.logger.error('缓存刷新失败:', error);
        // 继续使用旧缓存
      }
    }

    return {
      accounts: this.cachedAccountsWithBalance,
      tokens: this.cachedLightableTokens,
    };
  }

  /**
   * 启动随机点亮会话
   */
  async startRandomLight(
    config: Partial<RandomLightSessionConfig>,
  ): Promise<RandomLightSession> {
    if (this.currentSession?.status === RandomLightSessionStatus.RUNNING) {
      throw new Error('随机点亮会话已在运行中');
    }

    // 清理已停止的会话
    if (this.currentSession?.status === RandomLightSessionStatus.STOPPED) {
      this.logger.log(`清理已停止的会话: ${this.currentSession.id}`);
      this.currentSession = null;
    }

    // 启动时预加载数据到缓存
    await this.ensureCacheValid();

    // 创建新会话
    const sessionConfig: RandomLightSessionConfig = {
      intervalMs: config.intervalMs || 3000,
      amount: config.amount || 1,
      minBalance: config.minBalance || 0,
      maxLightNum: config.maxLightNum || 999,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
    };

    this.currentSession = {
      id: uuidv4(),
      status: RandomLightSessionStatus.RUNNING,
      config: sessionConfig,
      startTime: new Date(),
      totalExecutions: 0,
      successCount: 0,
      failedCount: 0,
    };

    this.logger.log(
      `启动随机点亮会话: ${this.currentSession.id}, 间隔: ${sessionConfig.intervalMs}ms`,
    );

    // 发送启动事件
    this.emitEvent(RandomLightEventType.SESSION_STARTED, {
      sessionId: this.currentSession.id,
      config: sessionConfig,
    });

    // 启动定时器
    this.scheduleNextExecution();

    return this.currentSession;
  }

  /**
   * 停止随机点亮会话
   */
  stopRandomLight(): void {
    if (!this.currentSession) {
      this.logger.warn('尝试停止随机点亮，但当前没有活动会话');
      return;
    }

    const sessionId = this.currentSession.id;
    this.logger.log(`手动停止随机点亮会话: ${sessionId}`);

    // 清除定时器
    if (this.currentSession.timer) {
      clearTimeout(this.currentSession.timer);
      this.logger.debug(`已清除定时器: ${sessionId}`);
    }

    // 更新会话状态
    this.currentSession.status = RandomLightSessionStatus.STOPPED;
    this.currentSession.endTime = new Date();
    this.logger.log(`会话状态已更新为STOPPED: ${sessionId}`);

    // 发送停止事件
    this.emitEvent(RandomLightEventType.SESSION_STOPPED, {
      sessionId,
      duration:
        this.currentSession.endTime.getTime() -
        this.currentSession.startTime.getTime(),
      totalExecutions: this.currentSession.totalExecutions,
      successCount: this.currentSession.successCount,
      failedCount: this.currentSession.failedCount,
    });

    // 不要立即将 currentSession 设为 null，保留会话信息以供状态查询
    // this.currentSession = null;
  }

  /**
   * 获取当前状态
   */
  getStatus(): RandomLightStatusDto {
    if (!this.currentSession) {
      this.logger.debug('获取状态: 当前没有活动会话，返回停止状态');
      return {
        isRunning: false,
        startTime: null,
        intervalMs: 0,
        amount: 0,
        totalExecutions: 0,
        successCount: 0,
        failedCount: 0,
        lastExecution: null,
        currentAccount: null,
        currentAssetId: null,
        currentAssetSymbol: null,
        uptime: 0,
      };
    }

    const isRunning =
      this.currentSession.status === RandomLightSessionStatus.RUNNING;
    this.logger.debug(
      `获取状态: 会话${this.currentSession.id}, 状态=${this.currentSession.status}, isRunning=${isRunning}`,
    );

    // 计算运行时长：如果会话已停止，使用 endTime；否则使用当前时间
    const endTime = this.currentSession.endTime || new Date();
    const uptime = endTime.getTime() - this.currentSession.startTime.getTime();

    return {
      isRunning,
      startTime: this.currentSession.startTime.toISOString(),
      intervalMs: this.currentSession.config.intervalMs,
      amount: this.currentSession.config.amount,
      totalExecutions: this.currentSession.totalExecutions,
      successCount: this.currentSession.successCount,
      failedCount: this.currentSession.failedCount,
      lastExecution: this.currentSession.lastExecution?.toISOString() || null,
      currentAccount: this.currentSession.currentAccount?.email || null,
      currentAssetId: this.currentSession.currentAsset?.assetId || null,
      currentAssetSymbol: this.currentSession.currentAsset?.symbol || null,
      uptime,
    };
  }

  /**
   * 获取统计信息
   */
  getStatistics(): RandomLightStatisticsDto {
    const totalExecutions = this.executionHistory.length;
    const totalSuccessCount = this.executionHistory.filter(
      (h) => h.status === RandomLightExecutionStatus.SUCCESS,
    ).length;
    const totalFailedCount = totalExecutions - totalSuccessCount;
    const successRate =
      totalExecutions > 0 ? (totalSuccessCount / totalExecutions) * 100 : 0;

    // 简化平均间隔计算
    const averageInterval = this.currentSession?.config.intervalMs || 0;

    // 当前会话信息
    const currentSession = this.currentSession
      ? {
          isActive:
            this.currentSession.status === RandomLightSessionStatus.RUNNING,
          duration: Date.now() - this.currentSession.startTime.getTime(),
          executions: this.currentSession.totalExecutions,
          successCount: this.currentSession.successCount,
          failedCount: this.currentSession.failedCount,
        }
      : {
          isActive: false,
          duration: 0,
          executions: 0,
          successCount: 0,
          failedCount: 0,
        };

    return {
      totalSessions: this.currentSession ? 1 : 0,
      totalExecutions,
      totalSuccessCount,
      totalFailedCount,
      successRate,
      averageInterval,
      longestSession: currentSession.duration,
      currentSession,
    };
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(limit: number = 100): RandomLightExecutionDto[] {
    return this.executionHistory
      .slice(-limit)
      .reverse()
      .map((result) => ({
        id: result.id,
        timestamp: result.timestamp.toISOString(),
        accountApiKey: result.account.apiKey.substring(0, 8) + '...',
        accountEmail: result.account.email,
        assetId: result.asset.assetId,
        assetSymbol: result.asset.symbol,
        assetName: result.asset.tokenName,
        amount: result.amount,
        status: result.status as 'success' | 'failed' | 'error',
        message: result.message,
        duration: result.duration,
        lightNumBefore: result.asset.lightNumBefore,
        lightNumAfter: result.asset.lightNumAfter,
      }));
  }

  /**
   * 调度下一次执行
   */
  private scheduleNextExecution(): void {
    if (!this.currentSession) {
      this.logger.warn('调度下一次执行失败: 当前没有活动会话');
      return;
    }

    if (this.currentSession.status !== RandomLightSessionStatus.RUNNING) {
      this.logger.warn(
        `调度下一次执行失败: 会话状态不是RUNNING，当前状态: ${this.currentSession.status}`,
      );
      return;
    }

    this.logger.debug(
      `调度下一次执行: 会话${this.currentSession.id}, 间隔${this.currentSession.config.intervalMs}ms`,
    );

    // 清除之前的定时器（如果存在）
    if (this.currentSession.timer) {
      clearTimeout(this.currentSession.timer);
      this.logger.debug(`清除之前的定时器: 会话${this.currentSession.id}`);
    }

    const timer = setTimeout(() => {
      void (async () => {
        // 再次检查会话状态，确保在定时器执行时会话仍然有效
        if (
          !this.currentSession ||
          this.currentSession.status !== RandomLightSessionStatus.RUNNING
        ) {
          this.logger.warn('定时器执行时会话已无效或已停止，跳过执行');
          return;
        }

        try {
          await this.executeRandomLight();
        } catch (error) {
          this.logger.error('随机点亮执行失败:', error);
          this.emitEvent(RandomLightEventType.ERROR_OCCURRED, {
            sessionId: this.currentSession?.id,
            error: error instanceof Error ? error.message : String(error),
          });
        }

        // 调度下一次执行
        this.scheduleNextExecution();
      })();
    }, this.currentSession.config.intervalMs);

    this.currentSession.timer = timer;
    this.logger.debug(`新定时器已设置: 会话${this.currentSession.id}`);
  }

  /**
   * 执行一次随机点亮
   */
  private async executeRandomLight(): Promise<void> {
    if (!this.currentSession) {
      return;
    }

    const startTime = Date.now();
    const executionId = uuidv4();

    this.logger.debug(`开始执行随机点亮: ${executionId}`);

    try {
      // 1. 随机选择账户和资产
      const selection = await this.performRandomSelection();

      if (!selection.account || !selection.asset) {
        const message = `没有找到合适的账户或资产进行点亮 - 可用账户: ${selection.availableAccountsCount}, 可用资产: ${selection.availableAssetsCount}`;
        this.logger.warn(message);
        this.recordExecution(
          executionId,
          null,
          null,
          RandomLightExecutionStatus.SKIPPED,
          message,
          startTime,
        );

        // 发送跳过事件
        this.emitEvent(RandomLightEventType.EXECUTION_FAILED, {
          sessionId: this.currentSession?.id,
          error: message,
        });

        return;
      }

      // 2. 更新当前选择
      this.currentSession.currentAccount = {
        apiKey: selection.account.apiKey,
        email: selection.account.email,
        userId: selection.account.userId,
      };
      this.currentSession.currentAsset = {
        assetId: selection.asset.assetId,
        symbol: selection.asset.symbol,
        tokenName: selection.asset.tokenName,
        lightNum: selection.asset.lightNum,
      };

      // 3. 发送执行开始事件
      this.emitEvent(RandomLightEventType.EXECUTION_STARTED, {
        sessionId: this.currentSession.id,
        executionId,
        account: selection.account,
        asset: selection.asset,
      });

      // 4. 执行点亮
      // 获取用户完整信息（包括secret）
      const fullUserBean = this.getUserWithSecret(selection.account.apiKey);
      if (!fullUserBean) {
        const message = `无法获取用户密钥信息: ${selection.account.apiKey.substring(0, 8)}...`;
        this.logger.error(message);
        this.recordExecution(
          executionId,
          selection.account,
          selection.asset,
          RandomLightExecutionStatus.FAILED,
          message,
          startTime,
        );

        // 发送失败事件
        this.emitEvent(RandomLightEventType.EXECUTION_FAILED, {
          sessionId: this.currentSession?.id,
          error: message,
        });

        return;
      }

      this.logger.debug(
        `准备执行点亮: 账户=${selection.account.email}, 资产=${selection.asset.symbol}, 数量=${this.currentSession.config.amount}`,
      );

      const amountMap = new Map<string, number>();
      amountMap.set(fullUserBean.apiKey, this.currentSession.config.amount);

      const result = await this.batchLightCore.executeEnhancedBatchLight(
        [fullUserBean],
        selection.asset.assetId,
        amountMap,
      );

      this.logger.debug(
        `API调用完成: 代码=${result.code}, 总数=${result.data.total}, 成功=${result.data.success}, 失败=${result.data.failed}`,
      );

      // 5. 处理结果
      if (!result.data.results || result.data.results.length === 0) {
        const message = '点亮API返回空结果';
        this.logger.error(message);
        this.recordExecution(
          executionId,
          selection.account,
          selection.asset,
          RandomLightExecutionStatus.ERROR,
          message,
          startTime,
        );
        return;
      }

      const lightResult = result.data.results[0];
      const status = lightResult.success
        ? RandomLightExecutionStatus.SUCCESS
        : RandomLightExecutionStatus.FAILED;

      this.logger.debug(
        `点亮结果: 状态=${lightResult.status}, 成功=${lightResult.success}, 消息=${lightResult.message}`,
      );

      this.recordExecution(
        executionId,
        selection.account,
        selection.asset,
        status,
        lightResult.message,
        startTime,
      );

      // 6. 更新统计
      this.currentSession.totalExecutions++;
      this.currentSession.lastExecution = new Date();

      if (lightResult.success) {
        this.currentSession.successCount++;
      } else {
        this.currentSession.failedCount++;
      }

      // 7. 更新最近使用记录
      this.updateRecentlyUsedRecords(
        selection.account.apiKey,
        selection.asset.assetId,
      );

      this.logger.debug(`随机点亮执行完成: ${executionId}, 状态: ${status}`);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error(`随机点亮执行异常: ${executionId}`, error);
      this.recordExecution(
        executionId,
        null,
        null,
        RandomLightExecutionStatus.ERROR,
        message,
        startTime,
      );

      if (this.currentSession) {
        this.currentSession.totalExecutions++;
        this.currentSession.failedCount++;
      }
    }
  }

  /**
   * 执行随机选择
   */
  private async performRandomSelection(): Promise<RandomSelectionResult> {
    const startTime = Date.now();

    try {
      // 1. 从缓存获取有余额的账户
      const { accounts } = await this.ensureCacheValid();

      // 根据当前会话的最小余额要求过滤
      const filteredAccounts = accounts.filter(
        (account) =>
          account.totalBalance >= (this.currentSession?.config.minBalance || 0),
      );

      this.logger.debug(
        `从缓存获取到 ${accounts.length} 个账户, 过滤后 ${filteredAccounts.length} 个符合余额要求`,
      );

      // 2. 筛选候选账户（排除最近使用的）
      const candidateAccounts = this.filterCandidateAccounts(filteredAccounts);

      this.logger.debug(`筛选后剩余 ${candidateAccounts.length} 个候选账户`);

      // 3. 从缓存获取可点亮的资产
      const { tokens } = await this.ensureCacheValid();

      this.logger.debug(`从缓存获取到 ${tokens.length} 个可点亮的资产`);

      // 4. 筛选候选资产（排除最近使用的）
      const candidateAssets = this.filterCandidateAssets(tokens);

      this.logger.debug(`筛选后剩余 ${candidateAssets.length} 个候选资产`);

      // 5. 随机选择
      const selectedAccount = this.randomSelectAccount(candidateAccounts);
      const selectedAsset = this.randomSelectAsset(candidateAssets);

      this.logger.debug(
        `随机选择结果: 账户=${selectedAccount?.email || 'null'}, 资产=${selectedAsset?.symbol || 'null'}`,
      );

      const result: RandomSelectionResult = {
        account: selectedAccount,
        asset: selectedAsset,
        availableAccountsCount: candidateAccounts.length,
        availableAssetsCount: candidateAssets.length,
        selectionTime: Date.now() - startTime,
      };

      // 发送选择完成事件
      this.emitEvent(RandomLightEventType.SELECTION_COMPLETED, {
        sessionId: this.currentSession?.id,
        selection: result,
      });

      return result;
    } catch (error) {
      this.logger.error('随机选择失败:', error);
      return {
        account: null,
        asset: null,
        availableAccountsCount: 0,
        availableAssetsCount: 0,
        selectionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 筛选候选账户
   */
  private filterCandidateAccounts(
    accountsWithBalance: NineBalanceInfo[],
  ): CandidateAccount[] {
    const now = Date.now();
    const recentWindowMs = 300000; // 5分钟内不重复使用

    return accountsWithBalance
      .filter((account) => {
        const lastUsed = this.recentlyUsedAccounts.get(account.apiKey);
        return !lastUsed || now - lastUsed.getTime() > recentWindowMs;
      })
      .map((account) => ({
        userId: account.userId,
        apiKey: account.apiKey,
        email: account.email,
        totalBalance: account.totalBalance,
        lastUsed: this.recentlyUsedAccounts.get(account.apiKey),
      }));
  }

  /**
   * 筛选候选资产
   */
  private filterCandidateAssets(
    lightableTokens: LightValidationResult[],
  ): CandidateAsset[] {
    const now = Date.now();
    const recentWindowMs = 300000; // 5分钟内不重复使用

    return lightableTokens
      .filter((token) => {
        const lastUsed = this.recentlyUsedAssets.get(token.assetId);
        return (
          token.canLight &&
          (!lastUsed || now - lastUsed.getTime() > recentWindowMs)
        );
      })
      .map((token) => ({
        assetId: token.assetId,
        symbol: token.symbol,
        tokenName: token.tokenName,
        lightNum: token.lightNum,
        postId: token.postId,
        status: token.status,
        lastUsed: this.recentlyUsedAssets.get(token.assetId),
      }));
  }

  /**
   * 随机选择账户
   */
  private randomSelectAccount(
    candidates: CandidateAccount[],
  ): CandidateAccount | null {
    if (candidates.length === 0) {
      return null;
    }

    return RandomUtils.randomChoice(candidates);
  }

  /**
   * 随机选择资产
   */
  private randomSelectAsset(
    candidates: CandidateAsset[],
  ): CandidateAsset | null {
    if (candidates.length === 0) {
      return null;
    }

    return RandomUtils.randomChoice(candidates);
  }

  /**
   * 获取用户完整信息（包括secret）
   * 使用 AccountDataService 统一获取账户数据
   */
  private getUserWithSecret(apiKey: string): UserBean | null {
    try {
      // 使用 AccountDataService 获取账户数据
      const accountsData = this.accountDataService.getAccounts();

      // 查找匹配的账户
      const matchedAccount = accountsData.find(
        (account) => account.apiKey === apiKey,
      );

      if (!matchedAccount) {
        this.logger.warn(`未找到API密钥: ${apiKey}`);
        return null;
      }

      return {
        apiKey: matchedAccount.apiKey,
        secret: matchedAccount.secret,
        email: matchedAccount.email,
      };
    } catch (error) {
      this.logger.error(`获取用户信息失败: ${apiKey}`, error);
      return null;
    }
  }

  /**
   * 记录执行结果
   */
  private recordExecution(
    executionId: string,
    account: CandidateAccount | null,
    asset: CandidateAsset | null,
    status: RandomLightExecutionStatus,
    message: string,
    startTime: number,
  ): void {
    const execution: RandomLightExecutionResult = {
      id: executionId,
      sessionId: this.currentSession?.id || '',
      timestamp: new Date(),
      account: account
        ? {
            apiKey: account.apiKey,
            email: account.email,
            userId: account.userId,
            totalBalance: account.totalBalance,
          }
        : {
            apiKey: '',
            email: '',
            userId: 0,
            totalBalance: 0,
          },
      asset: asset
        ? {
            assetId: asset.assetId,
            symbol: asset.symbol,
            tokenName: asset.tokenName,
            lightNumBefore: asset.lightNum,
            lightNumAfter:
              status === RandomLightExecutionStatus.SUCCESS
                ? asset.lightNum + 1
                : undefined,
            postId: asset.postId,
          }
        : {
            assetId: 0,
            symbol: '',
            tokenName: '',
            lightNumBefore: 0,
            postId: 0,
          },
      amount: this.currentSession?.config.amount || 0,
      status,
      message,
      duration: Date.now() - startTime,
    };

    // 添加到历史记录
    this.executionHistory.push(execution);

    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory.shift();
    }

    // 发送执行完成事件
    const eventType =
      status === RandomLightExecutionStatus.SUCCESS
        ? RandomLightEventType.EXECUTION_COMPLETED
        : RandomLightEventType.EXECUTION_FAILED;

    this.emitEvent(eventType, {
      sessionId: this.currentSession?.id,
      execution,
    });
  }

  /**
   * 更新最近使用记录
   */
  private updateRecentlyUsedRecords(apiKey: string, assetId: number): void {
    const now = new Date();
    this.recentlyUsedAccounts.set(apiKey, now);
    this.recentlyUsedAssets.set(assetId, now);

    // 清理过期记录
    const expireTime = now.getTime() - 600000; // 10分钟过期

    for (const [key, time] of this.recentlyUsedAccounts.entries()) {
      if (time.getTime() < expireTime) {
        this.recentlyUsedAccounts.delete(key);
      }
    }

    for (const [key, time] of this.recentlyUsedAssets.entries()) {
      if (time.getTime() < expireTime) {
        this.recentlyUsedAssets.delete(key);
      }
    }
  }

  /**
   * 发送事件
   */
  private emitEvent(
    type: RandomLightEventType,
    data: RandomLightEventData,
  ): void {
    const event: RandomLightEvent = {
      type,
      timestamp: new Date(),
      sessionId: this.currentSession?.id || '',
      data,
    };

    this.eventEmitter.emit('random-light.event', event);
  }
}
