import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { DatabaseModule } from '../../core/database/database.module';
import { AuthModule } from '../auth/auth.module';
import { BalanceModule } from '../balance/balance.module';
import { BatchModule } from '../trading/batch-trading/batch.module';
import { CommonModule } from '../../common/common.module';
import { RandomLightController } from './controllers/random-light.controller';
import { RandomLightService } from './services/random-light.service';

@Module({
  imports: [
    DatabaseModule,
    AuthModule,
    BalanceModule,
    BatchModule,
    CommonModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [RandomLightController],
  providers: [RandomLightService],
  exports: [RandomLightService],
})
export class RandomLightModule {}
