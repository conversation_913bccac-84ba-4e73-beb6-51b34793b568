import { IsN<PERSON><PERSON>, <PERSON>Option<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * 启动随机点亮请求DTO
 */
export class StartRandomLightDto {
  @IsOptional()
  @IsNumber({}, { message: '间隔时间必须是数字' })
  @Min(1000, { message: '间隔时间不能少于1000毫秒' })
  @Max(300000, { message: '间隔时间不能超过300000毫秒(5分钟)' })
  @Transform(({ value }) => parseInt(value as string))
  intervalMs?: number = 3000; // 默认3秒

  @IsOptional()
  @IsNumber({}, { message: '点亮数量必须是数字' })
  @Min(1, { message: '点亮数量不能少于1' })
  @Max(10, { message: '点亮数量不能超过10' })
  @Transform(({ value }) => parseInt(value as string))
  amount?: number = 1; // 默认点亮1个
}

/**
 * 随机点亮状态响应DTO
 */
export interface RandomLightStatusDto {
  isRunning: boolean;
  startTime: string | null;
  intervalMs: number;
  amount: number;
  totalExecutions: number;
  successCount: number;
  failedCount: number;
  lastExecution: string | null;
  currentAccount: string | null;
  currentAssetId: number | null;
  currentAssetSymbol: string | null;
  uptime: number; // 运行时长(毫秒)
}

/**
 * 随机点亮统计响应DTO
 */
export interface RandomLightStatisticsDto {
  totalSessions: number;
  totalExecutions: number;
  totalSuccessCount: number;
  totalFailedCount: number;
  successRate: number;
  averageInterval: number;
  longestSession: number;
  currentSession: {
    isActive: boolean;
    duration: number;
    executions: number;
    successCount: number;
    failedCount: number;
  };
}

/**
 * 随机点亮执行记录DTO
 */
export interface RandomLightExecutionDto {
  id: string;
  timestamp: string;
  accountApiKey: string;
  accountEmail: string;
  assetId: number;
  assetSymbol: string;
  assetName: string;
  amount: number;
  status: 'success' | 'failed' | 'error';
  message: string;
  duration: number; // 执行耗时(毫秒)
  lightNumBefore: number;
  lightNumAfter?: number;
}

/**
 * 随机点亮事件DTO (WebSocket推送)
 */
export interface RandomLightEventDto {
  type: 'status' | 'execution' | 'error' | 'stopped' | 'connected';
  timestamp: string;
  data: any;
}

/**
 * 随机点亮配置DTO
 */
export interface RandomLightConfigDto {
  minBalance: number; // 最小NINE余额要求
  maxLightNum: number; // 最大light_num限制
  retryAttempts: number; // 重试次数
  retryDelay: number; // 重试延迟(毫秒)
  maxConcurrentExecutions: number; // 最大并发执行数
}
