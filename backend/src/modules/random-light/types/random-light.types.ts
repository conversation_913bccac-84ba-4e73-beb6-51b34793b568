/**
 * 随机点亮相关类型定义
 */

/**
 * 随机点亮会话状态
 */
export enum RandomLightSessionStatus {
  STOPPED = 'stopped',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error',
}

/**
 * 随机点亮执行状态
 */
export enum RandomLightExecutionStatus {
  PENDING = 'pending',
  EXECUTING = 'executing',
  SUCCESS = 'success',
  FAILED = 'failed',
  ERROR = 'error',
  SKIPPED = 'skipped',
}

/**
 * 随机点亮会话配置
 */
export interface RandomLightSessionConfig {
  intervalMs: number;
  amount: number;
  minBalance: number;
  maxLightNum: number;
  retryAttempts: number;
  retryDelay: number;
}

/**
 * 随机点亮会话信息
 */
export interface RandomLightSession {
  id: string;
  status: RandomLightSessionStatus;
  config: RandomLightSessionConfig;
  startTime: Date;
  endTime?: Date;
  totalExecutions: number;
  successCount: number;
  failedCount: number;
  lastExecution?: Date;
  currentAccount?: {
    apiKey: string;
    email: string;
    userId: number;
  };
  currentAsset?: {
    assetId: number;
    symbol: string;
    tokenName: string;
    lightNum: number;
  };
  timer?: NodeJS.Timeout;
}

/**
 * 随机点亮执行结果
 */
export interface RandomLightExecutionResult {
  id: string;
  sessionId: string;
  timestamp: Date;
  account: {
    apiKey: string;
    email: string;
    userId: number;
    totalBalance: number;
  };
  asset: {
    assetId: number;
    symbol: string;
    tokenName: string;
    lightNumBefore: number;
    lightNumAfter?: number;
    postId: number;
  };
  amount: number;
  status: RandomLightExecutionStatus;
  message: string;
  duration: number;
  error?: string;
}

/**
 * 随机选择器配置
 */
export interface RandomSelectionConfig {
  accountMinBalance: number;
  assetMaxLightNum: number;
  excludeRecentlyUsed: boolean;
  recentlyUsedWindowMs: number;
}

/**
 * 候选账户信息
 */
export interface CandidateAccount {
  userId: number;
  apiKey: string;
  email: string;
  totalBalance: number;
  lastUsed?: Date;
}

/**
 * 候选资产信息
 */
export interface CandidateAsset {
  assetId: number;
  symbol: string;
  tokenName: string;
  lightNum: number;
  postId: number;
  status: string;
  lastUsed?: Date;
}

/**
 * 随机选择结果
 */
export interface RandomSelectionResult {
  account: CandidateAccount | null;
  asset: CandidateAsset | null;
  availableAccountsCount: number;
  availableAssetsCount: number;
  selectionTime: number;
}

/**
 * 随机点亮事件类型
 */
export enum RandomLightEventType {
  SESSION_STARTED = 'session_started',
  SESSION_STOPPED = 'session_stopped',
  SESSION_PAUSED = 'session_paused',
  SESSION_RESUMED = 'session_resumed',
  EXECUTION_STARTED = 'execution_started',
  EXECUTION_COMPLETED = 'execution_completed',
  EXECUTION_FAILED = 'execution_failed',
  SELECTION_COMPLETED = 'selection_completed',
  ERROR_OCCURRED = 'error_occurred',
}

/**
 * 随机点亮事件数据类型
 */
export type RandomLightEventData =
  | { sessionId: string; config: RandomLightSessionConfig }
  | {
      sessionId: string;
      duration: number;
      totalExecutions: number;
      successCount: number;
      failedCount: number;
    }
  | {
      sessionId: string;
      executionId: string;
      account: CandidateAccount;
      asset: CandidateAsset;
    }
  | { sessionId: string; execution: RandomLightExecutionResult }
  | { sessionId: string; selection: RandomSelectionResult }
  | { sessionId: string; error: string }
  | Record<string, unknown>;

/**
 * 随机点亮事件
 */
export interface RandomLightEvent {
  type: RandomLightEventType;
  timestamp: Date;
  sessionId: string;
  data: RandomLightEventData;
}
