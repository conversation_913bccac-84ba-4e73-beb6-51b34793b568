import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  HttpStatus,
  Lo<PERSON>,
  Sse,
  MessageEvent,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { Public } from '../../../modules/auth/decorators/public.decorator';
import { RandomLightService } from '../services/random-light.service';
import { StartRandomLightDto } from '../dto/random-light.dto';
import {
  RandomLightEvent,
  RandomLightSession,
} from '../types/random-light.types';

/**
 * 随机点亮控制器
 * 提供随机点亮功能的HTTP接口和实时事件流
 */
@Controller('batch/random-light')
@UseGuards(JwtAuthGuard)
export class RandomLightController {
  private readonly logger = new Logger(RandomLightController.name);

  constructor(
    private readonly randomLightService: RandomLightService,
    private readonly eventEmitter: EventEmitter2,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 启动随机点亮
   */
  @Post('start')
  async startRandomLight(@Body() dto: StartRandomLightDto) {
    this.logger.log(
      `启动随机点亮请求: 间隔${dto.intervalMs}ms, 数量${dto.amount}`,
    );

    try {
      const session: RandomLightSession =
        await this.randomLightService.startRandomLight({
          intervalMs: dto.intervalMs,
          amount: dto.amount,
        });

      return {
        code: HttpStatus.OK,
        msg: '随机点亮已启动',
        data: {
          sessionId: session.id,
          status: session.status,
          config: session.config,
          startTime: session.startTime,
        },
      };
    } catch (error) {
      this.logger.error('启动失败:', error);
      return {
        code: HttpStatus.BAD_REQUEST,
        msg: error instanceof Error ? error.message : '启动失败',
        data: null,
      };
    }
  }

  /**
   * 停止随机点亮
   */
  @Post('stop')
  stopRandomLight() {
    this.logger.log('停止随机点亮请求');

    try {
      this.randomLightService.stopRandomLight();
      return {
        code: HttpStatus.OK,
        msg: '随机点亮已停止',
        data: { stopped: true },
      };
    } catch (error) {
      this.logger.error('停止失败:', error);
      return {
        code: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: error instanceof Error ? error.message : '停止失败',
        data: { stopped: false },
      };
    }
  }

  /**
   * 获取随机点亮状态
   */
  @Get('status')
  getRandomLightStatus() {
    try {
      const status = this.randomLightService.getStatus();
      return {
        code: HttpStatus.OK,
        msg: '获取状态成功',
        data: status,
      };
    } catch (error) {
      this.logger.error('获取状态失败:', error);
      return {
        code: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: error instanceof Error ? error.message : '获取状态失败',
        data: null,
      };
    }
  }

  /**
   * 获取统计信息
   */
  @Get('statistics')
  getRandomLightStatistics() {
    try {
      const statistics = this.randomLightService.getStatistics();
      return {
        code: HttpStatus.OK,
        msg: '获取统计信息成功',
        data: statistics,
      };
    } catch (error) {
      this.logger.error('获取统计信息失败:', error);
      return {
        code: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: error instanceof Error ? error.message : '获取统计信息失败',
        data: null,
      };
    }
  }

  /**
   * 获取执行历史
   */
  @Get('history')
  getExecutionHistory(@Query('limit') limit?: number) {
    try {
      const history = this.randomLightService.getExecutionHistory(limit);
      return {
        code: HttpStatus.OK,
        msg: '获取执行历史成功',
        data: history,
      };
    } catch (error) {
      this.logger.error('获取执行历史失败:', error);
      return {
        code: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: error instanceof Error ? error.message : '获取执行历史失败',
        data: [],
      };
    }
  }

  /**
   * 健康检查
   */
  @Get('health')
  getHealthStatus() {
    try {
      const accountStats = this.randomLightService.getAccountStats();
      return {
        code: HttpStatus.OK,
        msg: '健康检查完成',
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          accountStats,
        },
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        code: HttpStatus.INTERNAL_SERVER_ERROR,
        msg: error instanceof Error ? error.message : '健康检查失败',
        data: {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : String(error),
        },
      };
    }
  }

  /**
   * 随机点亮事件流 (Server-Sent Events)
   */
  @Public()
  @Sse('events')
  events(@Query('token') token?: string): Observable<MessageEvent> {
    // 验证token
    if (!token) {
      throw new UnauthorizedException('缺少认证token');
    }

    try {
      this.jwtService.verify(token);
    } catch (error) {
      this.logger.warn('SSE认证失败:', error);
      throw new UnauthorizedException('无效的认证token');
    }

    this.logger.log('客户端连接到随机点亮事件流');

    return new Observable((observer) => {
      // 发送连接确认
      observer.next({
        type: 'connected',
        data: JSON.stringify({
          type: 'connected',
          timestamp: new Date().toISOString(),
          message: '已连接到随机点亮事件流',
        }),
      } as MessageEvent);

      // 定期发送状态更新
      const statusInterval = setInterval(() => {
        try {
          const status = this.randomLightService.getStatus();
          observer.next({
            type: 'status',
            data: JSON.stringify(status),
          } as MessageEvent);
        } catch (error) {
          this.logger.error('发送状态更新失败:', error);
        }
      }, 5000);

      // 监听随机点亮事件
      const eventListener = (event: RandomLightEvent) => {
        try {
          // 从event.data中排除sessionId以避免重复
          const { sessionId: _, ...eventDataWithoutSessionId } =
            event.data as Record<string, unknown>;

          observer.next({
            type: 'execution',
            data: JSON.stringify({
              eventType: event.type,
              sessionId: event.sessionId,
              ...eventDataWithoutSessionId,
            }),
          } as MessageEvent);
        } catch (error) {
          this.logger.error('发送事件失败:', error);
        }
      };

      this.eventEmitter.on('random-light.event', eventListener);

      // 清理函数
      return () => {
        this.logger.log('客户端断开随机点亮事件流连接');
        clearInterval(statusInterval);
        this.eventEmitter.off('random-light.event', eventListener);
      };
    });
  }
}
