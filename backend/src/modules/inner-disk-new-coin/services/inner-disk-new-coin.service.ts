import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InnerDiskService } from '../../trading/inner-disk/services/inner-disk.service';
import { AccountDataService } from '../../../common/services/account-data.service';
import { InnerDiskNewCoinPersistenceService } from './inner-disk-new-coin-persistence.service';
import {
  InnerDiskNewCoinConfig,
  MonitoringStatus,
  NewCoinEvent,
  NewCoinEventType,
  CreateNewCoinConfigRequest,
} from '../types';

/**
 * 内盘打新服务 - 持久化版本
 * 使用简单的持久化存储，确保重启后状态恢复
 */
@Injectable()
export class InnerDiskNewCoinService {
  private readonly logger = new Logger(InnerDiskNewCoinService.name);

  // 简化的内存缓存（启动时从持久化加载）
  private readonly configs = new Map<number, InnerDiskNewCoinConfig>();
  private readonly activeSessions = new Map<number, string>(); // userId -> sessionId
  private readonly sessionTimers = new Map<string, NodeJS.Timeout>();
  private readonly lastKnownPools = new Map<number, Set<number>>(); // userId -> Set<poolId>

  constructor(
    private readonly innerDiskService: InnerDiskService,
    private readonly accountDataService: AccountDataService,
    private readonly eventEmitter: EventEmitter2,
    private readonly persistenceService: InnerDiskNewCoinPersistenceService,
  ) {
    this.logger.log('内盘打新服务初始化完成');
    // 启动时加载持久化数据
    this.loadAllPersistedData();
  }

  /**
   * 启动时加载所有持久化数据
   */
  private loadAllPersistedData(): void {
    try {
      // 这里可以加载所有用户的配置，暂时简化
      this.logger.log('持久化数据加载完成');
    } catch (error) {
      this.logger.error('加载持久化数据失败:', error);
    }
  }

  /**
   * 简单的持久化保存
   */
  private async saveUserState(userId: number): Promise<void> {
    try {
      const config = this.configs.get(userId);
      const sessionId = this.activeSessions.get(userId);
      const lastKnownPools = this.lastKnownPools.get(userId) || new Set();

      if (config) {
        const state = {
          config,
          sessionId: sessionId || '',
          isRunning: !!sessionId,
          startTime: Date.now(),
          lastKnownPools,
          detectionHistory: [],
          monitoringStats: {
            totalDetected: 0,
            totalTrades: 0,
            successfulTrades: 0,
            currentPositions: 0,
            totalProfit: 0,
            errorCount: 0,
          },
          lastSaved: Date.now(),
        };

        await this.persistenceService.saveMonitoringState(userId, state);
        this.logger.debug(`用户${userId}状态已保存`);
      }
    } catch (error) {
      this.logger.error(`保存用户${userId}状态失败:`, error);
    }
  }

  /**
   * 简单的持久化加载
   */
  private async loadUserState(userId: number): Promise<void> {
    try {
      const state = await this.persistenceService.loadMonitoringState(userId);
      if (state && state.config) {
        this.configs.set(userId, state.config);
        this.lastKnownPools.set(userId, state.lastKnownPools || new Set());
        this.logger.debug(`用户${userId}状态已恢复`);
      }
    } catch (error) {
      this.logger.error(`加载用户${userId}状态失败:`, error);
    }
  }

  // ========================================
  // 持久化方法
  // ========================================

  // ========================================
  // 配置管理
  // ========================================

  /**
   * 获取用户配置
   */
  getUserConfig(userId: number): InnerDiskNewCoinConfig | null {
    const config = this.configs.get(userId);
    return config ? { ...config } : null;
  }

  /**
   * 创建或更新用户配置
   */
  createOrUpdateConfig(
    userId: number,
    configData: CreateNewCoinConfigRequest,
  ): InnerDiskNewCoinConfig {
    const credentials = this.getCredentials(
      configData.apiKey,
      configData.secret,
    );

    const config: InnerDiskNewCoinConfig = {
      id: Date.now(),
      userId,
      isEnabled: configData.isEnabled ?? false,
      apiKey: credentials.apiKey,
      secret: credentials.secret,
      purchaseAmount: configData.purchaseAmount,
      purchaseType: configData.purchaseType,
      purchasePercentage: configData.purchasePercentage,
      profitRate: configData.profitRate,
      stopLossRate: configData.stopLossRate,
      monitorInterval: configData.monitorInterval ?? 5000,
      maxPositions: configData.maxPositions ?? 5,
      minMarketCap: configData.minMarketCap,
      maxMarketCap: configData.maxMarketCap,
      excludeTokens: configData.excludeTokens || [],

      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.configs.set(userId, config);
    this.logger.log(`用户 ${userId} 的配置已保存`);

    // 异步保存到持久化存储
    void this.saveUserState(userId);

    return config;
  }

  // ========================================
  // 监控管理
  // ========================================

  /**
   * 启动监控
   */
  async startMonitoring(userId: number): Promise<string> {
    // 尝试恢复之前的状态
    await this.loadUserState(userId);

    // 检查是否已在运行
    if (this.activeSessions.has(userId)) {
      throw new Error('监控已在运行中');
    }

    // 获取配置
    const config = this.getUserConfig(userId);
    if (!config) {
      throw new Error('请先配置监控参数');
    }

    // 移除了临时凭据更新逻辑
    // 现在API凭据只能通过配置设置

    // 创建监控会话
    const sessionId = this.generateSessionId();
    this.activeSessions.set(userId, sessionId);

    // 初始化已知池子列表
    await this.initializeKnownPools(userId, config);

    // 启动监控定时器
    this.startMonitoringTimer(userId, sessionId, config);

    // 发送状态更新事件
    this.emitStatusEvent(userId);

    // 保存监控状态
    void this.saveUserState(userId);

    this.logger.log(`用户 ${userId} 开始监控，会话ID: ${sessionId}`);
    return sessionId;
  }

  /**
   * 停止监控
   */
  stopMonitoring(userId: number, reason?: string): void {
    const sessionId = this.activeSessions.get(userId);
    if (!sessionId) {
      throw new Error('监控未在运行');
    }

    // 停止定时器
    const timer = this.sessionTimers.get(sessionId);
    if (timer) {
      clearInterval(timer);
      this.sessionTimers.delete(sessionId);
    }

    // 保存最终状态
    void this.saveUserState(userId);

    // 清理状态
    this.activeSessions.delete(userId);
    this.lastKnownPools.delete(userId);

    // 发送停止事件
    this.emitEvent(userId, 'stopped', { sessionId, reason });

    this.logger.log(`用户 ${userId} 停止监控，会话ID: ${sessionId}`);
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus(userId: number): MonitoringStatus {
    const sessionId = this.activeSessions.get(userId);
    const isRunning = Boolean(sessionId);

    const status: MonitoringStatus = {
      isRunning,
      sessionId,
      startTime: isRunning ? new Date() : undefined,
      uptime: isRunning ? Date.now() - Date.now() : 0, // 简化实现
      totalDetected: 0,
      totalTrades: 0,
      successfulTrades: 0,
      currentPositions: 0,
      totalProfit: 0,
      lastDetection: undefined,
      lastTrade: undefined,
      errorCount: 0,
      lastError: undefined,
    };

    return status;
  }

  // ========================================
  // 私有辅助方法
  // ========================================

  /**
   * 获取API凭据
   */
  private getCredentials(
    apiKey?: string,
    secret?: string,
  ): { apiKey: string; secret: string } {
    if (apiKey && secret) {
      return { apiKey, secret };
    }

    // 使用默认凭据
    const validCredentials = this.accountDataService.getValidCredentials();
    return {
      apiKey: validCredentials.apiKey || '',
      secret: validCredentials.secret || '',
    };
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 发送状态事件
   */
  private emitStatusEvent(userId: number): void {
    const status = this.getMonitoringStatus(userId);
    this.emitEvent(userId, 'status', status);
  }

  /**
   * 发送事件
   */
  private emitEvent(
    userId: number,
    type: NewCoinEventType,
    data: unknown,
  ): void {
    const event: NewCoinEvent = {
      type,
      timestamp: new Date(),
      data,
    };

    this.eventEmitter.emit(`inner-disk-new-coin.${userId}`, event);
  }

  /**
   * 初始化已知池子列表
   */
  private async initializeKnownPools(
    userId: number,
    config: InnerDiskNewCoinConfig,
  ): Promise<void> {
    try {
      const allPools = await this.innerDiskService.getAllInnerDiskData({
        apiKey: config.apiKey,
        secret: config.secret,
      });

      const poolIds = new Set(allPools.map((pool) => pool.id));
      this.lastKnownPools.set(userId, poolIds);

      this.logger.log(`用户 ${userId} 初始化已知池子数量: ${poolIds.size}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`初始化已知池子失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 启动监控定时器
   */
  private startMonitoringTimer(
    userId: number,
    sessionId: string,
    config: InnerDiskNewCoinConfig,
  ): void {
    const timer = setInterval(() => {
      void (async () => {
        try {
          await this.performMonitoringCycle(userId, sessionId, config);
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '未知错误';
          this.logger.error(`监控周期执行失败: ${errorMessage}`);
          this.emitEvent(userId, 'error', { message: errorMessage });
        }
      })();
    }, config.monitorInterval);

    this.sessionTimers.set(sessionId, timer);
  }

  /**
   * 执行监控周期 - 简化版本
   */
  private async performMonitoringCycle(
    userId: number,
    sessionId: string,
    config: InnerDiskNewCoinConfig,
  ): Promise<void> {
    this.logger.debug(`执行监控周期: 用户 ${userId}, 会话 ${sessionId}`);

    try {
      // 获取当前所有内盘数据
      const currentPools = await this.innerDiskService.getAllInnerDiskData({
        apiKey: config.apiKey,
        secret: config.secret,
      });

      const currentPoolIds = new Set(currentPools.map((pool) => pool.id));
      const lastKnownPoolIds = this.lastKnownPools.get(userId) || new Set();

      // 找出新增的池子
      const newPoolIds = Array.from(currentPoolIds).filter(
        (id) => !lastKnownPoolIds.has(id),
      );

      if (newPoolIds.length > 0) {
        const newPools = currentPools.filter((pool) =>
          newPoolIds.includes(pool.id),
        );
        this.logger.log(
          `用户 ${userId} 检测到 ${newPools.length} 个新币: ${newPools.map((p) => p.tokenSymbol).join(', ')}`,
        );

        // 发送检测事件
        this.emitEvent(userId, 'detection', {
          newCoins: newPools.map((pool) => ({
            poolId: pool.id,
            tokenSymbol: pool.tokenSymbol,
            tokenName: pool.ticker,
            initialPrice: pool.price,
            marketCap: pool.marketCap,
            detectedAt: new Date(),
          })),
        });
      }

      // 更新已知池子列表
      this.lastKnownPools.set(userId, currentPoolIds);

      // 发送状态更新
      this.emitStatusEvent(userId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`监控周期执行失败: ${errorMessage}`);
      throw error;
    }
  }
}
