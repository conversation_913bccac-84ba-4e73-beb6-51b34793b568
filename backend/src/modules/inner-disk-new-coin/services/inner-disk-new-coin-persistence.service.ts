import { Injectable, Logger } from '@nestjs/common';
import { PersistenceService } from '../../../core/database/persistence.service';
import {
  InnerDiskNewCoinConfig,
  MonitoringStatus,
  NewCoinDetection,
} from '../types';

/**
 * 内盘打新持久化数据结构
 */
export interface InnerDiskNewCoinPersistentData {
  config: InnerDiskNewCoinConfig;
  sessionId: string;
  isRunning: boolean;
  startTime: number;
  lastKnownPools: Set<number>;
  detectionHistory: NewCoinDetection[];
  monitoringStats: {
    totalDetected: number;
    totalTrades: number;
    successfulTrades: number;
    currentPositions: number;
    totalProfit: number;
    errorCount: number;
    lastDetection?: number;
    lastTrade?: number;
    lastError?: string;
  };
  lastSaved: number;
}

/**
 * 序列化后的持久化数据结构（用于JSON存储）
 */
export interface SerializedInnerDiskNewCoinPersistentData {
  config: InnerDiskNewCoinConfig;
  sessionId: string;
  isRunning: boolean;
  startTime: number;
  lastKnownPools: number[]; // Set序列化为数组
  detectionHistory: NewCoinDetection[];
  monitoringStats: {
    totalDetected: number;
    totalTrades: number;
    successfulTrades: number;
    currentPositions: number;
    totalProfit: number;
    errorCount: number;
    lastDetection?: number;
    lastTrade?: number;
    lastError?: string;
  };
  lastSaved: number;
}

/**
 * 内盘打新持久化服务
 *
 * 负责将监控状态和交易记录持久化到JSON文件
 */
@Injectable()
export class InnerDiskNewCoinPersistenceService {
  private readonly logger = new Logger(InnerDiskNewCoinPersistenceService.name);

  constructor(private readonly persistenceService: PersistenceService) {}

  /**
   * 保存监控状态
   */
  async saveMonitoringState(
    userId: number,
    data: InnerDiskNewCoinPersistentData,
  ): Promise<void> {
    try {
      const filename = this.persistenceService.getUserFilename(
        userId,
        'inner_disk_new_coin',
      );

      // 序列化Set对象为数组
      const serializedData: SerializedInnerDiskNewCoinPersistentData = {
        ...data,
        lastKnownPools: Array.from(data.lastKnownPools),
        lastSaved: Date.now(),
      };

      await this.persistenceService.atomicSave(filename, serializedData);
      this.logger.debug(
        `内盘打新状态已保存: 用户${userId}, 会话${data.sessionId}`,
      );
    } catch (error) {
      this.logger.error(
        `保存监控状态失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * 加载监控状态
   */
  async loadMonitoringState(
    userId: number,
  ): Promise<InnerDiskNewCoinPersistentData | null> {
    try {
      const filename = this.persistenceService.getUserFilename(
        userId,
        'inner_disk_new_coin',
      );

      const defaultData: SerializedInnerDiskNewCoinPersistentData = {
        config:
          null as unknown as SerializedInnerDiskNewCoinPersistentData['config'],
        sessionId: '',
        isRunning: false,
        startTime: 0,
        lastKnownPools: [],
        detectionHistory: [],
        monitoringStats: {
          totalDetected: 0,
          totalTrades: 0,
          successfulTrades: 0,
          currentPositions: 0,
          totalProfit: 0,
          errorCount: 0,
        },
        lastSaved: 0,
      };

      const serializedData = await this.persistenceService.safeLoad(
        filename,
        defaultData,
      );

      if (!serializedData.config) {
        return null;
      }

      // 反序列化数组为Set对象
      const data: InnerDiskNewCoinPersistentData = {
        ...serializedData,
        isRunning: false, // 重启后默认为停止状态
        lastKnownPools: new Set(serializedData.lastKnownPools || []),
      };

      this.logger.log(
        `内盘打新状态已加载: 用户${userId}, 会话${data.sessionId}`,
      );
      return data;
    } catch (error) {
      this.logger.error(
        `加载监控状态失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * 删除监控状态
   */
  async deleteMonitoringState(userId: number): Promise<void> {
    try {
      const filename = this.persistenceService.getUserFilename(
        userId,
        'inner_disk_new_coin',
      );
      await this.persistenceService.deleteData(filename);
      this.logger.log(`内盘打新状态已删除: 用户${userId}`);
    } catch (error) {
      this.logger.error(
        `删除监控状态失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 检查是否有保存的监控状态
   */
  async hasMonitoringState(userId: number): Promise<boolean> {
    const filename = this.persistenceService.getUserFilename(
      userId,
      'inner_disk_new_coin',
    );
    return await this.persistenceService.dataExists(filename);
  }

  /**
   * 保存新币检测记录（追加模式）
   */
  async appendDetectionRecord(
    userId: number,
    detection: NewCoinDetection,
  ): Promise<void> {
    try {
      // 加载现有状态
      const existingData = await this.loadMonitoringState(userId);
      if (!existingData) {
        this.logger.warn(`无法追加检测记录，监控状态不存在: 用户${userId}`);
        return;
      }

      // 添加新记录
      existingData.detectionHistory.push(detection);
      existingData.monitoringStats.totalDetected++;
      existingData.monitoringStats.lastDetection = Date.now();

      // 限制历史记录大小（保留最近500条）
      if (existingData.detectionHistory.length > 500) {
        existingData.detectionHistory =
          existingData.detectionHistory.slice(-500);
      }

      // 保存更新后的状态
      await this.saveMonitoringState(userId, existingData);

      this.logger.debug(
        `检测记录已追加: 用户${userId}, 池子${detection.poolId}`,
      );
    } catch (error) {
      this.logger.error(
        `追加检测记录失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 更新监控统计信息
   */
  async updateMonitoringStats(
    userId: number,
    updates: Partial<InnerDiskNewCoinPersistentData['monitoringStats']>,
  ): Promise<void> {
    try {
      const existingData = await this.loadMonitoringState(userId);
      if (!existingData) {
        this.logger.warn(`无法更新统计信息，监控状态不存在: 用户${userId}`);
        return;
      }

      // 更新统计信息
      Object.assign(existingData.monitoringStats, updates);

      // 保存更新后的状态
      await this.saveMonitoringState(userId, existingData);

      this.logger.debug(`监控统计已更新: 用户${userId}`);
    } catch (error) {
      this.logger.error(
        `更新监控统计失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 获取检测历史记录
   */
  async getDetectionHistory(
    userId: number,
    limit?: number,
    offset?: number,
  ): Promise<NewCoinDetection[]> {
    try {
      const data = await this.loadMonitoringState(userId);
      if (!data) {
        return [];
      }

      let history = data.detectionHistory;

      // 按时间倒序排列
      history.sort((a, b) => b.detectedAt.getTime() - a.detectedAt.getTime());

      // 应用分页
      if (offset) {
        history = history.slice(offset);
      }
      if (limit) {
        history = history.slice(0, limit);
      }

      return history;
    } catch (error) {
      this.logger.error(
        `获取检测历史失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      return [];
    }
  }

  /**
   * 获取监控状态
   */
  async getMonitoringStatus(userId: number): Promise<MonitoringStatus> {
    try {
      const data = await this.loadMonitoringState(userId);

      if (!data) {
        return {
          isRunning: false,
          sessionId: undefined,
          startTime: undefined,
          uptime: 0,
          totalDetected: 0,
          totalTrades: 0,
          successfulTrades: 0,
          currentPositions: 0,
          totalProfit: 0,
          lastDetection: undefined,
          lastTrade: undefined,
          errorCount: 0,
          lastError: undefined,
        };
      }

      const uptime = data.isRunning ? Date.now() - data.startTime : 0;

      return {
        isRunning: data.isRunning,
        sessionId: data.sessionId,
        startTime: data.isRunning ? new Date(data.startTime) : undefined,
        uptime,
        totalDetected: data.monitoringStats.totalDetected,
        totalTrades: data.monitoringStats.totalTrades,
        successfulTrades: data.monitoringStats.successfulTrades,
        currentPositions: data.monitoringStats.currentPositions,
        totalProfit: data.monitoringStats.totalProfit,
        lastDetection: data.monitoringStats.lastDetection
          ? new Date(data.monitoringStats.lastDetection)
          : undefined,
        lastTrade: data.monitoringStats.lastTrade
          ? new Date(data.monitoringStats.lastTrade)
          : undefined,
        errorCount: data.monitoringStats.errorCount,
        lastError: data.monitoringStats.lastError,
      };
    } catch (error) {
      this.logger.error(
        `获取监控状态失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {
        isRunning: false,
        sessionId: undefined,
        startTime: undefined,
        uptime: 0,
        totalDetected: 0,
        totalTrades: 0,
        successfulTrades: 0,
        currentPositions: 0,
        totalProfit: 0,
        lastDetection: undefined,
        lastTrade: undefined,
        errorCount: 0,
        lastError: undefined,
      };
    }
  }

  /**
   * 清理过期的监控状态（超过30天的停止监控）
   */
  async cleanupExpiredMonitoring(): Promise<void> {
    try {
      const files = await this.persistenceService.listDataFiles();
      const innerDiskFiles = files.filter(
        (file) =>
          file.includes('inner_disk_new_coin') && file.startsWith('user_'),
      );

      const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;

      for (const file of innerDiskFiles) {
        try {
          const data =
            await this.persistenceService.loadData<SerializedInnerDiskNewCoinPersistentData>(
              file,
              null as unknown as SerializedInnerDiskNewCoinPersistentData,
            );

          if (data && !data.isRunning && data.lastSaved < thirtyDaysAgo) {
            await this.persistenceService.deleteData(file);
            this.logger.log(`已清理过期监控状态: ${file}`);
          }
        } catch (error) {
          this.logger.warn(
            `清理文件失败: ${file}, ${error instanceof Error ? error.message : String(error)}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `清理过期监控失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}
