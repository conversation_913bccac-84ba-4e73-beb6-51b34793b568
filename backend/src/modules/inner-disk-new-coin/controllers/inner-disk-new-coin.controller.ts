import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Logger,
  Sse,
  MessageEvent,
} from '@nestjs/common';
import { Observable, fromEvent, map } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { InnerDiskNewCoinService } from '../services/inner-disk-new-coin.service';
import {
  CreateNewCoinConfigDto,
  StopMonitoringDto,
  BaseResponseDto,
  ConfigResponseDto,
  MonitoringStatusResponseDto,
  EventResponseDto,
} from '../dto';
import {
  NewCoinEvent,
  InnerDiskNewCoinConfig,
  MonitoringStatus,
} from '../types';

// 辅助函数：安全地转换配置对象
function convertConfigToResponse(
  config: InnerDiskNewCoinConfig,
): ConfigResponseDto {
  return {
    id: config.id,
    userId: config.userId,
    isEnabled: config.isEnabled,
    purchaseAmount: config.purchaseAmount,
    purchaseType: config.purchaseType,
    purchasePercentage: config.purchasePercentage,
    profitRate: config.profitRate,
    stopLossRate: config.stopLossRate,
    monitorInterval: config.monitorInterval,
    maxPositions: config.maxPositions,
    minMarketCap: config.minMarketCap,
    maxMarketCap: config.maxMarketCap,
    excludeTokens: config.excludeTokens,

    createdAt: config.createdAt?.toISOString(),
    updatedAt: config.updatedAt?.toISOString(),
  };
}

// 辅助函数：安全地转换状态对象
function convertStatusToResponse(
  status: MonitoringStatus,
): MonitoringStatusResponseDto {
  return {
    isRunning: status.isRunning,
    sessionId: status.sessionId,
    startTime: status.startTime?.toISOString(),
    uptime: status.uptime,
    totalDetected: status.totalDetected,
    totalTrades: status.totalTrades,
    successfulTrades: status.successfulTrades,
    currentPositions: status.currentPositions,
    totalProfit: status.totalProfit,
    lastDetection: status.lastDetection?.toISOString(),
    lastTrade: status.lastTrade?.toISOString(),
    errorCount: status.errorCount,
    lastError: status.lastError,
  };
}

/**
 * 内盘打新控制器 - 简化版本
 */
@Controller('inner-disk-new-coin')
@UseGuards(JwtAuthGuard)
export class InnerDiskNewCoinController {
  private readonly logger = new Logger(InnerDiskNewCoinController.name);

  constructor(
    private readonly innerDiskNewCoinService: InnerDiskNewCoinService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.logger.log('内盘打新控制器初始化完成');
  }

  /**
   * 获取用户配置
   */
  @Get('config')
  getConfig(): BaseResponseDto<ConfigResponseDto | null> {
    try {
      // 使用固定的用户ID，因为当前是单用户系统
      const userId = 1;

      const config = this.innerDiskNewCoinService.getUserConfig(userId);

      if (config === null) {
        return new BaseResponseDto(true, 200, '配置不存在', null);
      }

      const responseData = convertConfigToResponse(config);

      return new BaseResponseDto(true, 200, '获取配置成功', responseData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`获取配置失败: ${errorMessage}`);
      return new BaseResponseDto(false, 500, `获取配置失败: ${errorMessage}`);
    }
  }

  /**
   * 创建或更新配置
   */
  @Post('config')
  createOrUpdateConfig(
    @Body() configDto: CreateNewCoinConfigDto,
  ): BaseResponseDto<ConfigResponseDto> {
    try {
      // 使用固定的用户ID，因为当前是单用户系统
      const userId = 1;

      const config = this.innerDiskNewCoinService.createOrUpdateConfig(
        userId,
        configDto,
      );

      const responseData = convertConfigToResponse(config);

      return new BaseResponseDto(true, 200, '配置保存成功', responseData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`保存配置失败: ${errorMessage}`);
      return new BaseResponseDto(false, 500, `保存配置失败: ${errorMessage}`);
    }
  }

  /**
   * 启动监控
   */
  @Post('start')
  async startMonitoring(): Promise<BaseResponseDto<{ sessionId: string }>> {
    try {
      // 使用固定的用户ID，因为当前是单用户系统
      const userId = 1;

      const sessionId =
        await this.innerDiskNewCoinService.startMonitoring(userId);

      this.logger.log(`用户 ${userId} 启动内盘打新监控，会话ID: ${sessionId}`);

      return new BaseResponseDto(true, 200, '监控启动成功', { sessionId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`启动监控失败: ${errorMessage}`);
      return new BaseResponseDto(false, 500, `启动监控失败: ${errorMessage}`);
    }
  }

  /**
   * 停止监控
   */
  @Post('stop')
  stopMonitoring(@Body() stopDto: StopMonitoringDto): BaseResponseDto<void> {
    try {
      // 使用固定的用户ID，因为当前是单用户系统
      const userId = 1;

      void this.innerDiskNewCoinService.stopMonitoring(userId, stopDto.reason);

      this.logger.log(`用户 ${userId} 停止内盘打新监控`);

      return new BaseResponseDto(true, 200, '监控已停止');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`停止监控失败: ${errorMessage}`);
      return new BaseResponseDto(false, 500, `停止监控失败: ${errorMessage}`);
    }
  }

  /**
   * 获取监控状态
   */
  @Get('status')
  getMonitoringStatus(): BaseResponseDto<MonitoringStatusResponseDto> {
    try {
      // 使用固定的用户ID，因为当前是单用户系统
      const userId = 1;

      const status = this.innerDiskNewCoinService.getMonitoringStatus(userId);

      const responseData = convertStatusToResponse(status);

      return new BaseResponseDto(true, 200, '获取状态成功', responseData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`获取监控状态失败: ${errorMessage}`);
      return new BaseResponseDto(
        false,
        500,
        `获取监控状态失败: ${errorMessage}`,
      );
    }
  }

  /**
   * 实时事件流
   */
  @Sse('events')
  getEventStream(): Observable<MessageEvent> {
    // 使用固定的用户ID，因为当前是单用户系统
    const userId = 1;

    this.logger.log(`用户 ${userId} 连接内盘打新事件流`);

    return fromEvent(this.eventEmitter, `inner-disk-new-coin.${userId}`).pipe(
      map((event: NewCoinEvent) => {
        const eventData: EventResponseDto = {
          type: event.type,
          timestamp: event.timestamp.toISOString(),
          data: event.data,
        };

        return {
          data: JSON.stringify(eventData),
          type: event.type,
        } as MessageEvent;
      }),
    );
  }

  /**
   * 健康检查
   */
  @Get('health')
  getHealth(): BaseResponseDto<{
    status: string;
    service: string;
    timestamp: string;
  }> {
    return new BaseResponseDto(true, 200, '服务正常', {
      status: 'healthy',
      service: 'inner-disk-new-coin',
      timestamp: new Date().toISOString(),
    });
  }
}
