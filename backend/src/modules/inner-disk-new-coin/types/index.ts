/**
 * 内盘打新功能类型定义 - 简化版本
 */

// ========================================
// 基础枚举
// ========================================

export enum PurchaseType {
  FIXED = 'FIXED',
  PERCENTAGE = 'PERCENTAGE',
}

export enum NewCoinTradeType {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum TriggerType {
  PROFIT_TARGET = 'PROFIT_TARGET',
  STOP_LOSS = 'STOP_LOSS',
  MANUAL = 'MANUAL',
}

// ========================================
// 基础类型
// ========================================

export type SessionStatus = 'RUNNING' | 'STOPPED' | 'ERROR';
export type TradeStatus = 'PENDING' | 'SUCCESS' | 'FAILED';
export type PositionStatus = 'HOLDING' | 'SOLD' | 'EXPIRED';
export type NewCoinEventType =
  | 'status'
  | 'detection'
  | 'trade'
  | 'error'
  | 'stopped'
  | 'connected';

// ========================================
// 核心接口
// ========================================

/**
 * 内盘打新配置
 */
export interface InnerDiskNewCoinConfig {
  id?: number;
  userId: number;
  isEnabled: boolean;
  apiKey: string;
  secret: string;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval: number;
  maxPositions: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens?: string[];

  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * 配置创建/更新请求
 */
export interface CreateNewCoinConfigRequest {
  isEnabled?: boolean;
  apiKey?: string;
  secret?: string;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval?: number;
  maxPositions?: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens?: string[];
}

/**
 * 监控状态
 */
export interface MonitoringStatus {
  isRunning: boolean;
  sessionId?: string;
  startTime?: Date;
  uptime: number;
  totalDetected: number;
  totalTrades: number;
  successfulTrades: number;
  currentPositions: number;
  totalProfit: number;
  lastDetection?: Date;
  lastTrade?: Date;
  errorCount: number;
  lastError?: string;
}

/**
 * 实时事件
 */
export interface NewCoinEvent {
  type: NewCoinEventType;
  timestamp: Date;
  data: unknown;
}

/**
 * 新币检测结果
 */
export interface NewCoinDetectionResult {
  poolId: number;
  tokenSymbol: string;
  tokenName?: string;
  initialPrice: number;
  marketCap?: number;
  detectedAt: Date;
}

/**
 * 新币检测记录（用于持久化）
 */
export interface NewCoinDetection {
  poolId: number;
  tokenSymbol: string;
  tokenName?: string;
  stableCoinSymbol: string;
  initialPrice: number;
  marketCap?: number;
  liquidity?: number;
  volume24h?: number;
  chain: string;
  detectedAt: Date;
  isTraded: boolean;
  tradeReason?: string;
}

/**
 * 统计信息
 */
export interface Statistics {
  totalSessions: number;
  activeSessions: number;
  totalDetected: number;
  totalTrades: number;
  successfulTrades: number;
  totalProfit: number;
  successRate: number;
  averageProfit: number;
}
