import {
  IsBoolean,
  Is<PERSON><PERSON>ber,
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  Min,
  Max,
  IsPositive,
} from 'class-validator';
import { PurchaseType } from '../types';

/**
 * 创建/更新配置DTO
 */
export class CreateNewCoinConfigDto {
  @IsOptional()
  @IsBoolean()
  isEnabled?: boolean = false;

  @IsOptional()
  @IsString()
  apiKey?: string;

  @IsOptional()
  @IsString()
  secret?: string;

  @IsNumber()
  @IsPositive()
  purchaseAmount: number;

  @IsEnum(PurchaseType)
  purchaseType: PurchaseType = PurchaseType.FIXED;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(1)
  purchasePercentage?: number;

  @IsNumber()
  @Min(0.01)
  @Max(10)
  profitRate: number = 0.1;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(1)
  stopLossRate?: number;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  monitorInterval?: number = 5000;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  maxPositions?: number = 5;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  minMarketCap?: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  maxMarketCap?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeTokens?: string[];
}

// StartMonitoringDto 已移除
// 启动监控不再需要额外参数，API凭据通过配置管理

/**
 * 停止监控DTO
 */
export class StopMonitoringDto {
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 响应DTO基类
 */
export class BaseResponseDto<T = unknown> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: string;

  constructor(success: boolean, code: number, message: string, data?: T) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * 配置响应DTO
 */
export interface ConfigResponseDto {
  id?: number;
  userId: number;
  isEnabled: boolean;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval: number;
  maxPositions: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens?: string[];

  createdAt?: string;
  updatedAt?: string;
}

/**
 * 监控状态响应DTO
 */
export interface MonitoringStatusResponseDto {
  isRunning: boolean;
  sessionId?: string;
  startTime?: string;
  uptime: number;
  totalDetected: number;
  totalTrades: number;
  successfulTrades: number;
  currentPositions: number;
  totalProfit: number;
  lastDetection?: string;
  lastTrade?: string;
  errorCount: number;
  lastError?: string;
  config?: ConfigResponseDto;
}

/**
 * 事件响应DTO
 */
export interface EventResponseDto {
  type: string;
  timestamp: string;
  data: unknown;
}
