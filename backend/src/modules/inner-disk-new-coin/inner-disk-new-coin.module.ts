import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { AuthModule } from '../auth/auth.module';
import { InnerDiskModule } from '../trading/inner-disk/inner-disk.module';
import { CommonModule } from '../../common/common.module';

import { InnerDiskNewCoinController } from './controllers';
import { InnerDiskNewCoinService } from './services/inner-disk-new-coin.service';
import { InnerDiskNewCoinPersistenceService } from './services/inner-disk-new-coin-persistence.service';

/**
 * 内盘打新模块
 *
 * 功能特性：
 * - 新币监控和检测
 * - 自动交易执行
 * - 止盈止损管理
 * - 持仓管理
 * - 实时状态推送
 * - 统计分析
 *
 * 依赖模块：
 * - DatabaseModule: 数据库操作
 * - AuthModule: 用户认证
 * - InnerDiskModule: 内盘API服务
 * - CommonModule: 通用服务
 * - EventEmitterModule: 事件系统
 * - ScheduleModule: 定时任务
 */
@Module({
  imports: [
    // 认证模块
    AuthModule,

    // 内盘模块（复用现有的内盘API服务）
    InnerDiskModule,

    // 通用模块（账户数据服务等）
    CommonModule,

    // 事件系统（用于实时推送）
    EventEmitterModule.forRoot(),
  ],

  controllers: [InnerDiskNewCoinController],

  providers: [InnerDiskNewCoinService, InnerDiskNewCoinPersistenceService],

  exports: [InnerDiskNewCoinService, InnerDiskNewCoinPersistenceService],
})
export class InnerDiskNewCoinModule {}
