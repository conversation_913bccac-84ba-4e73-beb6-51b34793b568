import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../core/database/database.service';
import {
  NineBalanceService,
  type NineBalanceInfo,
} from '../balance/services/nine-balance.service';
import { AccountData, UserWithBalance } from '../../core/shared/common.types';
import { AccountDataService } from '../../common/services/account-data.service';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly nineBalanceService: NineBalanceService,
    private readonly accountDataService: AccountDataService,
  ) {}

  importAccountsFromFile(): void {
    // accounts.json 是数据源，不需要导入到数据库
    // 这个方法保留是为了兼容性，但实际上不执行任何操作
    const accountsData = this.accountDataService.getAccounts();
    this.logger.log(`账户数据已加载，共 ${accountsData.length} 个账户`);
    // 不再将数据导入数据库，因为我们直接从文件读取
  }

  getAllUsers() {
    // 从 accounts.json 文件获取所有用户，而不是从数据库
    const accounts = this.accountDataService.getAccounts();
    // 转换为符合预期格式的对象
    return accounts.map((account, index) => ({
      id: index + 1, // 使用索引作为ID
      email: account.email,
      apiKey: account.apiKey,
      secret: account.secret,
      createdAt: new Date(), // 使用当前时间作为创建时间
    }));
  }

  getUsersByIds(ids: number[]) {
    // 从 accounts.json 文件获取用户，而不是从数据库
    const accounts = this.accountDataService.getAccounts();
    return accounts
      .map((account, index) => ({
        id: index + 1,
        email: account.email,
        apiKey: account.apiKey,
        secret: account.secret,
      }))
      .filter((user) => ids.includes(user.id));
  }

  getUsersCount(): number {
    // 从 accounts.json 文件获取用户数量，而不是从数据库
    const accounts = this.accountDataService.getAccounts();
    return accounts.length;
  }

  /**
   * 使用 AccountDataService 读取账户数据
   * 不依赖数据库，直接返回文件中的账户信息
   */
  getAccountsFromFile(): AccountData[] {
    const accountsData = this.accountDataService.getAccounts();
    this.logger.log(`从账户文件读取到 ${accountsData.length} 个账户`);
    return accountsData;
  }

  /**
   * 获取带余额信息的账户列表
   * 支持不同类型的代币余额查询
   */
  async getAccountsWithBalance(
    tokenType: 'NINE' | 'USDT' = 'NINE',
  ): Promise<UserWithBalance[]> {
    try {
      this.logger.log(`开始获取带${tokenType}余额信息的账户列表`);

      // 1. 从文件读取账户数据
      const accountsData = this.getAccountsFromFile();

      // 2. 根据代币类型获取余额信息
      let balanceDetails: NineBalanceInfo[];
      if (tokenType === 'NINE') {
        const balanceResult =
          await this.nineBalanceService.queryAllNineBalances();
        balanceDetails = balanceResult.balanceDetails;
      } else {
        balanceDetails = await this.nineBalanceService.getUsdtBalances();
      }

      // 3. 创建余额映射表，以 apiKey 为键
      const balanceMap = new Map<string, NineBalanceInfo>();
      balanceDetails.forEach((balance) => {
        balanceMap.set(balance.apiKey, balance);
      });

      // 4. 合并账户数据和余额信息
      const usersWithBalance: UserWithBalance[] = accountsData.map(
        (account) => {
          const balanceInfo = balanceMap.get(account.apiKey);

          return {
            ...account,
            balance: balanceInfo?.totalBalance || 0,
            hasBalance: balanceInfo?.hasBalance || false,
          };
        },
      );

      this.logger.log(
        `成功获取 ${usersWithBalance.length} 个账户的${tokenType}余额信息`,
      );
      return usersWithBalance;
    } catch (error) {
      this.logger.error(`获取带${tokenType}余额信息的账户列表失败:`, error);
      throw new Error(
        `获取账户${tokenType}余额信息失败: ${(error as Error).message}`,
      );
    }
  }

  /**
   * 获取账户统计信息
   * 包括总账户数、有余额账户数、总余额等统计数据
   */
  async getAccountsStatistics() {
    try {
      const accountsData = this.getAccountsFromFile();
      const balanceResult =
        await this.nineBalanceService.queryAllNineBalances();

      return {
        totalAccounts: accountsData.length,
        accountsWithBalance: balanceResult.accountsWithBalance,
        accountsWithoutBalance: balanceResult.accountsWithoutBalance,
        totalNineBalance: balanceResult.totalNineBalance,
        averageBalance: balanceResult.averageBalance,
        executionTimeMs: balanceResult.executionTimeMs,
      };
    } catch (error) {
      this.logger.error('获取账户统计信息失败:', error);
      throw new Error(`获取账户统计信息失败: ${(error as Error).message}`);
    }
  }

  /**
   * 根据余额条件筛选账户
   * @param minBalance 最小余额阈值
   */
  async getFilteredAccounts(
    minBalance: number = 0,
  ): Promise<UserWithBalance[]> {
    return this.getFilteredAccountsByTokenType(minBalance, 'NINE');
  }

  /**
   * 获取带USDT余额信息的账户列表
   * 专门用于内盘交易的余额查询
   */
  async getAccountsWithUsdtBalance(): Promise<UserWithBalance[]> {
    return this.getAccountsWithBalance('USDT');
  }

  /**
   * 根据余额条件筛选账户
   * @param minBalance 最小余额阈值
   * @param tokenType 代币类型
   */
  async getFilteredAccountsByTokenType(
    minBalance: number = 0,
    tokenType: 'NINE' | 'USDT' = 'NINE',
  ): Promise<UserWithBalance[]> {
    const accountsWithBalance = await this.getAccountsWithBalance(tokenType);

    return accountsWithBalance.filter((account: UserWithBalance) => {
      return (account.balance || 0) >= minBalance;
    });
  }
}
