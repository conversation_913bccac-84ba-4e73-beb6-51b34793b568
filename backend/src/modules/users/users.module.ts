import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { DatabaseModule } from '../../core/database/database.module';
// Temporarily disabled due to auth module path issues\n// import { AuthModule } from '../../auth/auth.module';
import { BalanceModule } from '../balance/balance.module';
import { UserAccountSelectionService } from './services/user-account-selection.service';
import { CommonModule } from '../../common/common.module';

@Module({
  imports: [DatabaseModule, /* AuthModule, */ BalanceModule, CommonModule],
  controllers: [UsersController],
  providers: [UsersService, UserAccountSelectionService],
  exports: [UsersService, UserAccountSelectionService],
})
export class UsersModule {}
