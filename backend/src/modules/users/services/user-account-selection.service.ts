import { Injectable } from '@nestjs/common';
import { UsersService } from '../users.service';
import { AccountDataService } from '../../../common/services/account-data.service';
import { RandomUtils } from '../../../core/shared/random.utils';
import { BaseBusinessService } from '../../../core/shared/base-business.service';
import { UserWithBalance } from '../../../core/shared/common.types';

/**
 * 账户选择选项
 */
export interface AccountSelectionOptions {
  /** 代币类型 */
  tokenType: 'NINE' | 'USDT';
  /** 最小余额要求 */
  minBalance?: number;
  /** 最大账户数量限制 */
  maxAccounts?: number;
  /** 是否排除最近使用的账户 */
  excludeRecent?: boolean;
  /** 选择模式 */
  selectionMode: 'random' | 'smart_filter' | 'all';
  /** 避重时间窗口（毫秒） */
  recentWindowMs?: number;
  /** 最近使用的账户记录 */
  recentlyUsedAccounts?: Map<string, Date>;
}

/**
 * 账户选择结果
 */
export interface AccountSelectionResult {
  /** 选中的账户列表 */
  selectedAccounts: UserWithBalance[];
  /** 总可用账户数 */
  totalAvailable: number;
  /** 过滤后的账户数 */
  filteredCount: number;
  /** 选择耗时（毫秒） */
  selectionTime: number;
  /** 选择统计信息 */
  statistics: {
    totalAccountsInFile: number;
    accountsWithBalance: number;
    accountsAfterMinBalance: number;
    accountsAfterRecentFilter: number;
    finalSelectedCount: number;
  };
}

/**
 * 用户模块的账户选择服务
 *
 * 职责：
 * - 提供统一的账户选择逻辑
 * - 支持不同代币类型的余额过滤
 * - 支持多种选择模式（随机、智能筛选、全部）
 * - 实现避重机制
 */
@Injectable()
export class UserAccountSelectionService extends BaseBusinessService {
  constructor(
    private readonly usersService: UsersService,
    private readonly accountDataService: AccountDataService,
  ) {
    super(UserAccountSelectionService.name);
  }

  /**
   * 选择账户
   *
   * @param options 选择选项
   * @returns 选择结果
   */
  async selectAccounts(
    options: AccountSelectionOptions,
  ): Promise<AccountSelectionResult> {
    const startTime = Date.now();

    this.logger.log(
      `开始账户选择: tokenType=${options.tokenType}, mode=${options.selectionMode}, minBalance=${options.minBalance}`,
    );

    try {
      // 1. 获取所有账户数据
      const allAccounts = this.accountDataService.getAccounts();

      // 2. 获取带余额信息的账户
      const accountsWithBalance =
        await this.usersService.getAccountsWithBalance(options.tokenType);

      // 3. 应用最小余额过滤
      const minBalance = options.minBalance || 0;
      const accountsAfterMinBalance = accountsWithBalance.filter(
        (account) => (account.balance || 0) >= minBalance,
      );

      // 4. 应用避重过滤
      const accountsAfterRecentFilter = this.applyRecentFilter(
        accountsAfterMinBalance,
        options,
      );

      // 5. 根据选择模式进行最终选择
      const selectedAccounts = this.applySelectionMode(
        accountsAfterRecentFilter,
        options,
      );

      const selectionTime = Date.now() - startTime;

      const result: AccountSelectionResult = {
        selectedAccounts,
        totalAvailable: allAccounts.length,
        filteredCount: accountsAfterRecentFilter.length,
        selectionTime,
        statistics: {
          totalAccountsInFile: allAccounts.length,
          accountsWithBalance: accountsWithBalance.length,
          accountsAfterMinBalance: accountsAfterMinBalance.length,
          accountsAfterRecentFilter: accountsAfterRecentFilter.length,
          finalSelectedCount: selectedAccounts.length,
        },
      };

      this.logger.log(
        `账户选择完成: 总账户${result.statistics.totalAccountsInFile}, ` +
          `有余额${result.statistics.accountsWithBalance}, ` +
          `最终选择${result.statistics.finalSelectedCount}, ` +
          `耗时${selectionTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error('账户选择失败:', error);
      throw error;
    }
  }

  /**
   * 获取账户余额统计信息
   *
   * @param tokenType 代币类型
   * @param minBalance 最小余额要求
   * @returns 统计信息
   */
  async getAccountBalanceStatistics(
    tokenType: 'NINE' | 'USDT',
    minBalance: number = 0,
  ): Promise<{
    totalAccounts: number;
    accountsWithBalance: number;
    accountsAboveMinBalance: number;
    averageBalance: number;
    totalBalance: number;
  }> {
    const accountsWithBalance =
      await this.usersService.getAccountsWithBalance(tokenType);

    const accountsAboveMinBalance = accountsWithBalance.filter(
      (account) => (account.balance || 0) >= minBalance,
    );

    const totalBalance = accountsWithBalance.reduce(
      (sum, account) => sum + (account.balance || 0),
      0,
    );

    const averageBalance =
      accountsWithBalance.length > 0
        ? totalBalance / accountsWithBalance.length
        : 0;

    return {
      totalAccounts: this.accountDataService.getAccounts().length,
      accountsWithBalance: accountsWithBalance.length,
      accountsAboveMinBalance: accountsAboveMinBalance.length,
      averageBalance,
      totalBalance,
    };
  }

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 应用避重过滤
   */
  private applyRecentFilter(
    accounts: UserWithBalance[],
    options: AccountSelectionOptions,
  ): UserWithBalance[] {
    if (!options.excludeRecent || !options.recentlyUsedAccounts) {
      return accounts;
    }

    const now = Date.now();
    const windowMs = options.recentWindowMs || 300000; // 默认5分钟

    return accounts.filter((account) => {
      const lastUsed = options.recentlyUsedAccounts!.get(account.apiKey);
      return !lastUsed || now - lastUsed.getTime() > windowMs;
    });
  }

  /**
   * 应用选择模式
   */
  private applySelectionMode(
    accounts: UserWithBalance[],
    options: AccountSelectionOptions,
  ): UserWithBalance[] {
    switch (options.selectionMode) {
      case 'random':
        return this.randomSelect(accounts, options.maxAccounts);

      case 'smart_filter':
        return this.smartFilter(accounts, options.maxAccounts);

      case 'all':
        return options.maxAccounts
          ? accounts.slice(0, options.maxAccounts)
          : accounts;

      default:
        throw new Error(`不支持的选择模式: ${options.selectionMode as string}`);
    }
  }

  /**
   * 随机选择
   */
  private randomSelect(
    accounts: UserWithBalance[],
    maxAccounts?: number,
  ): UserWithBalance[] {
    if (accounts.length === 0) {
      return [];
    }

    const count = maxAccounts ? Math.min(maxAccounts, accounts.length) : 1;

    // 使用Fisher-Yates洗牌算法进行更好的随机选择
    const shuffled = [...accounts];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = RandomUtils.randomInt(0, i);
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    return shuffled.slice(0, count);
  }

  /**
   * 智能筛选（按余额排序，选择前N个）
   */
  private smartFilter(
    accounts: UserWithBalance[],
    maxAccounts?: number,
  ): UserWithBalance[] {
    const sorted = [...accounts].sort(
      (a, b) => (b.balance || 0) - (a.balance || 0),
    );
    return maxAccounts ? sorted.slice(0, maxAccounts) : sorted;
  }
}
