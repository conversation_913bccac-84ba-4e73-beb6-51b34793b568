import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../auth/strategies/jwt.strategy';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * 初始化账户数据
   * 从 accounts.json 文件导入用户数据
   */
  @Post('init-accounts')
  initAccounts(@CurrentUser() _user?: User) {
    try {
      this.usersService.importAccountsFromFile();
      const count = this.usersService.getUsersCount();
      return {
        success: true,
        message: `Successfully imported accounts`,
        count,
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 获取用户总数
   */
  @Get('count')
  getUsersCount(@CurrentUser() _user?: User) {
    const count = this.usersService.getUsersCount();
    return {
      success: true,
      count,
    };
  }

  /**
   * 获取所有用户列表
   */
  @Get()
  getAllUsers(@CurrentUser() _user?: User) {
    try {
      const users = this.usersService.getAllUsers();
      return {
        success: true,
        data: users,
        count: users.length,
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 从账户文件获取账户数据
   * 直接读取文件，不依赖数据库
   */
  @Get('accounts-from-file')
  getAccountsFromFile(@CurrentUser() _user?: User) {
    try {
      const accounts = this.usersService.getAccountsFromFile();
      return {
        success: true,
        data: accounts,
        count: accounts.length,
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 通用获取带代币余额信息的账户列表处理器
   */
  private async getAccountsWithTokenBalanceHandler(
    tokenType: 'NINE' | 'USDT',
    _user?: User,
  ) {
    try {
      const accountsWithBalance =
        await this.usersService.getAccountsWithBalance(tokenType);
      return {
        success: true,
        data: accountsWithBalance,
        count: accountsWithBalance.length,
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 获取带余额信息的账户列表
   * 结合 accounts.json 数据和 NINE 代币余额信息
   */
  @Get('accounts-with-balance')
  async getAccountsWithBalance(@CurrentUser() _user?: User) {
    return this.getAccountsWithTokenBalanceHandler('NINE', _user);
  }

  /**
   * 获取带USDT余额信息的账户列表
   * 专门用于内盘交易的余额查询
   */
  @Get('accounts-with-usdt-balance')
  async getAccountsWithUsdtBalance(@CurrentUser() _user?: User) {
    return this.getAccountsWithTokenBalanceHandler('USDT', _user);
  }

  /**
   * 获取账户统计信息
   * 包括总账户数、有余额账户数、总余额等统计数据
   */
  @Get('statistics')
  async getAccountsStatistics(@CurrentUser() _user?: User) {
    try {
      const statistics = await this.usersService.getAccountsStatistics();
      return {
        success: true,
        data: statistics,
      };
    } catch (error: unknown) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
