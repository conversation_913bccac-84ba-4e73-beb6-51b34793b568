import { Injectable, BadRequestException } from '@nestjs/common';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { CredentialsManagerService } from '../../../../core/infrastructure/credentials-manager.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { InnerDiskTradeApiService } from './inner-disk-trade-api.service';
import {
  InnerDiskTradeRequest,
  InnerDiskTradeResponse,
  InnerDiskTradeUserRequest,
  InnerTradeBean,
} from '../types/inner-disk.types';

/**
 * 内盘交易业务服务
 *
 * 职责：
 * - 处理内盘交易的业务逻辑
 * - 管理用户凭据和交易参数
 * - 提供统一的交易接口
 *
 * 设计模式：
 * - 参考 inner-disk.service.ts 的设计模式
 * - 使用依赖注入获取其他服务
 * - 保持业务逻辑简洁
 */
@Injectable()
export class InnerDiskTradeService extends BaseBusinessService {
  constructor(
    private readonly accountDataService: AccountDataService,
    private readonly credentialsManager: CredentialsManagerService,
    private readonly innerDiskTradeApiService: InnerDiskTradeApiService,
  ) {
    super(InnerDiskTradeService.name);
    this.logger.log('内盘交易服务初始化完成');
  }

  /**
   * 执行内盘交易
   * @param tradeRequests 交易请求列表
   * @returns 交易结果
   */
  async executeInnerDiskTrade(
    tradeRequests: InnerDiskTradeUserRequest[],
  ): Promise<InnerDiskTradeResponse> {
    this.logger.log(`开始执行内盘交易，用户数量: ${tradeRequests.length}`);

    // 打印详细的请求信息（隐藏敏感信息）
    tradeRequests.forEach((request, index) => {
      this.logger.log(
        `🔄 [DEBUG] 交易请求 ${index + 1}: ${JSON.stringify(
          {
            userBean: {
              apiKey: request.userBean.apiKey
                ? request.userBean.apiKey.substring(0, 8) + '...'
                : 'null',
              secret: request.userBean.secret
                ? request.userBean.secret.substring(0, 8) + '...'
                : 'null',
            },
            innerTradeBean: request.innerTradeBean,
          },
          null,
          2,
        )}`,
      );
    });

    try {
      // 验证和处理交易请求
      const processedRequests = this.processTradeRequests(tradeRequests);

      this.logger.log(`🔄 [DEBUG] 请求验证通过，准备调用外部API`);

      // 调用外部 API
      const response =
        await this.innerDiskTradeApiService.callInnerDiskTradeApi(
          processedRequests,
        );

      this.logger.log(
        `内盘交易执行完成，成功处理 ${response.data?.length || 0} 个交易结果`,
      );

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`内盘交易执行失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 使用默认凭据执行内盘交易
   * @param trades 交易操作列表
   * @param poolId 可选的统一池ID
   * @returns 交易结果
   */
  async executeInnerDiskTradeWithDefaults(
    trades: InnerTradeBean[],
    poolId?: number,
  ): Promise<InnerDiskTradeResponse> {
    this.logger.log(`使用默认凭据执行内盘交易，交易数量: ${trades.length}`);

    // 获取默认凭据
    const credentials = this.credentialsManager.getInnerDiskTradeCredentials();

    // 如果指定了统一池ID，更新所有交易的池ID
    const processedTrades = poolId
      ? trades.map((trade) => ({ ...trade, poolId }))
      : trades;

    // 构建交易请求
    const tradeRequest: InnerDiskTradeUserRequest = {
      userBean: {
        apiKey: credentials.apiKey,
        secret: credentials.secret,
      },
      innerTradeBean: processedTrades,
    };

    return this.executeInnerDiskTrade([tradeRequest]);
  }

  /**
   * 批量执行多用户内盘交易
   * @param userTrades 用户交易映射
   * @returns 交易结果
   */
  async executeBatchInnerDiskTrade(
    userTrades: Map<string, InnerTradeBean[]>,
  ): Promise<InnerDiskTradeResponse> {
    this.logger.log(`批量执行内盘交易，用户数量: ${userTrades.size}`);

    const tradeRequests: InnerDiskTradeUserRequest[] = [];

    for (const [apiKey, trades] of userTrades.entries()) {
      const credentials = this.getCredentialsByApiKey(apiKey);

      tradeRequests.push({
        userBean: {
          apiKey: credentials.apiKey,
          secret: credentials.secret,
        },
        innerTradeBean: trades,
      });
    }

    return this.executeInnerDiskTrade(tradeRequests);
  }

  /**
   * 获取当前使用的交易者信息
   * 用于其他服务获取交易者详细信息
   * @returns 交易者信息，包含邮箱和API Key
   */
  getCurrentTraderInfo(): { email: string; apiKey: string } | null {
    try {
      // 获取默认凭据
      const credentials =
        this.credentialsManager.getInnerDiskTradeCredentials();

      // 从AccountDataService获取对应的账户信息
      const account = this.accountDataService.findAccountByApiKey(
        credentials.apiKey,
      );

      if (account) {
        return {
          email: account.email,
          apiKey: credentials.apiKey,
        };
      }

      // 如果找不到账户信息，返回基本信息
      return {
        email: '<EMAIL>',
        apiKey: credentials.apiKey,
      };
    } catch (error) {
      this.logger.warn('获取当前交易者信息失败:', error);
      return null;
    }
  }

  // ========================================
  // 私有方法 - 内部实现细节
  // ========================================

  /**
   * 处理交易请求，验证和补充凭据
   * @private
   */
  private processTradeRequests(
    tradeRequests: InnerDiskTradeUserRequest[],
  ): InnerDiskTradeRequest {
    return tradeRequests.map((request) => {
      // 验证交易操作
      if (!request.innerTradeBean || request.innerTradeBean.length === 0) {
        throw new BadRequestException('交易操作列表不能为空');
      }

      // 验证用户凭据
      if (!request.userBean.apiKey || !request.userBean.secret) {
        throw new BadRequestException('用户凭据不完整');
      }

      return request;
    });
  }

  /**
   * 根据 API Key 获取凭据
   * @private
   */
  private getCredentialsByApiKey(apiKey: string): {
    apiKey: string;
    secret: string;
  } {
    const credentials = this.credentialsManager.getCredentialsByApiKey(apiKey);
    if (!credentials) {
      throw new BadRequestException(`未找到 API Key 对应的账户: ${apiKey}`);
    }

    return credentials;
  }
}
