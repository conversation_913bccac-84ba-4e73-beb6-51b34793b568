import { Injectable } from '@nestjs/common';
import {
  ENV_KEYS,
  EXTERNAL_API_ENDPOINTS,
} from '../../../../core/config/app.constants';
import {
  InnerDiskApiRequest,
  InnerDiskApiResponse,
  InnerDiskApiResponseData,
} from '../types/inner-disk.types';
import {
  BaseExternalApiService,
  ExternalApiConfig,
} from '../../../../core/shared/base-external-api.service';

/**
 * 内盘API原始响应结构
 * 用于类型安全的响应数据访问
 */
interface InnerDiskRawApiResponse {
  data: {
    data: InnerDiskApiResponseData;
  };
}

/**
 * 内盘 API 客户端服务
 *
 * 职责：
 * - 专门处理对外部内盘 API 的调用
 * - 处理 HTTP 请求和响应
 * - 统一错误处理和日志记录
 *
 * 设计模式：
 * - 继承BaseExternalApiService以获得统一的HTTP请求处理能力
 * - 使用环境变量配置，避免硬编码
 * - 遵循SOLID原则，便于测试和维护
 */
@Injectable()
export class InnerDiskApiService extends BaseExternalApiService {
  constructor() {
    super();
    this.logger.log('内盘 API 服务初始化完成');
    this.logger.log(`API 基础 URL: ${this.getApiBaseUrl()}`);
  }

  /**
   * 获取API基础URL
   */
  protected getApiBaseUrl(): string {
    return (
      process.env[ENV_KEYS.NINE_API_BASE_URL] || 'https://openapi.binineex.com'
    );
  }

  /**
   * 获取默认配置
   */
  protected getDefaultConfig(): ExternalApiConfig {
    return {
      timeout: 30000,
      retries: 2, // 内盘API允许重试
      enableDetailedLogging: true,
      retryDelay: 1000,
    };
  }

  /**
   * 调用内盘 API
   * 使用基类的统一API调用方法
   * @param requestBody 请求参数（已包含凭据）
   * @returns API 响应数据
   */
  async callInnerDiskApi(
    requestBody: InnerDiskApiRequest,
  ): Promise<InnerDiskApiResponse> {
    const url = `${this.getApiBaseUrl()}${EXTERNAL_API_ENDPOINTS.INNER_DISK}`;

    const response = await this.callExternalApi<
      InnerDiskApiRequest,
      InnerDiskRawApiResponse
    >(url, requestBody, this.getDefaultConfig());

    // 从响应中提取实际的内盘数据
    // response.data 包含用户信息和实际数据，实际的内盘数据在 response.data.data.data 中
    const actualData = response.data?.data?.data;

    if (!actualData) {
      throw new Error('内盘 API 响应数据格式错误');
    }

    // 转换为InnerDiskApiResponse格式以保持兼容性
    const result: InnerDiskApiResponse = {
      code: response.code,
      msg: response.msg,
      data: actualData,
    };

    this.logger.log(
      `内盘 API 调用成功，返回 ${result.data?.records?.length || 0} 条记录`,
    );

    return result;
  }

  /**
   * 延迟函数 - 用于避免 API 频率限制
   * @param ms 延迟毫秒数
   */
  async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
