import { Injectable } from '@nestjs/common';
import {
  ENV_KEYS,
  EXTERNAL_API_ENDPOINTS,
} from '../../../../core/config/app.constants';
import {
  InnerDiskTradeRequest,
  InnerDiskTradeResponse,
} from '../types/inner-disk.types';
import {
  BaseExternalApiService,
  ExternalApiConfig,
} from '../../../../core/shared/base-external-api.service';

/**
 * 内盘交易 API 客户端服务
 *
 * 职责：
 * - 专门处理对外部内盘交易 API 的调用
 * - 处理 HTTP 请求和响应
 * - 统一错误处理和日志记录
 *
 * 设计模式：
 * - 继承BaseExternalApiService以获得统一的HTTP请求处理能力
 * - 使用环境变量配置，避免硬编码
 * - 遵循SOLID原则，便于测试和维护
 */
@Injectable()
export class InnerDiskTradeApiService extends BaseExternalApiService {
  constructor() {
    super();
    this.logger.log('内盘交易 API 服务初始化完成');
    this.logger.log(`API 基础 URL: ${this.getApiBaseUrl()}`);
  }

  /**
   * 获取API基础URL
   */
  protected getApiBaseUrl(): string {
    return (
      process.env[ENV_KEYS.NINE_API_BASE_URL] || 'https://openapi.binineex.com'
    );
  }

  /**
   * 获取默认配置
   */
  protected getDefaultConfig(): ExternalApiConfig {
    return {
      timeout: 30000,
      retries: 2, // 交易API允许重试
      enableDetailedLogging: true,
      retryDelay: 1000,
    };
  }

  /**
   * 调用内盘交易 API
   * 使用基类的统一API调用方法
   * @param requestBody 交易请求数据
   * @returns API 响应数据
   */
  async callInnerDiskTradeApi(
    requestBody: InnerDiskTradeRequest,
  ): Promise<InnerDiskTradeResponse> {
    const url = `${this.getApiBaseUrl()}${EXTERNAL_API_ENDPOINTS.INNER_DISK_TRADE}`;

    // 计算总交易操作数用于日志记录
    const totalTrades = requestBody.reduce(
      (sum, userRequest) => sum + userRequest.innerTradeBean.length,
      0,
    );
    this.logger.log(
      `调用内盘交易 API，用户数量: ${requestBody.length}，总交易操作数: ${totalTrades}`,
    );

    const response = await this.callExternalApi<InnerDiskTradeRequest, unknown>(
      url,
      requestBody,
      this.getDefaultConfig(),
    );

    // 转换为InnerDiskTradeResponse格式以保持兼容性
    const result: InnerDiskTradeResponse = {
      code: response.code,
      msg: response.msg,
      data: response.data as InnerDiskTradeResponse['data'],
    };

    this.logger.log(
      `内盘交易 API 调用成功，返回 ${result.data?.length || 0} 个交易结果`,
    );

    // 打印完整的API响应用于调试
    this.logger.log(`完整API响应: ${JSON.stringify(result, null, 2)}`);

    return result;
  }

  /**
   * 延迟函数 - 用于避免 API 频率限制
   * @param ms 延迟毫秒数
   */
  async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
