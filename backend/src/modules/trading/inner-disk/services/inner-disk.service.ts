import { Injectable, BadRequestException } from '@nestjs/common';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { CredentialsManagerService } from '../../../../core/infrastructure/credentials-manager.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import { InnerDiskApiService } from './inner-disk-api.service';
import { NumberUtils } from '../../../../core/shared/number.utils';
import {
  InnerDiskApiRequest,
  InnerDiskQueryResult,
  InnerDiskRecord,
  InnerDiskQueryParams,
} from '../types/inner-disk.types';
import { PoolAvailabilityUtil } from '../../../../core/shared/pool-availability.util';

/**
 * 内盘信息获取服务 - 协调器
 *
 * 职责：
 * - 协调 API 调用和业务逻辑
 * - 处理凭据验证和管理
 * - 提供统一的数据访问接口
 * - 数据转换和业务计算
 *
 * 设计原则：
 * - 单一职责：只处理内盘信息相关业务逻辑
 * - 依赖注入：使用 AccountDataService 和 InnerDiskApiService
 * - 统一响应：符合项目响应格式标准
 */
@Injectable()
export class InnerDiskService extends BaseBusinessService {
  constructor(
    private readonly accountDataService: AccountDataService,
    private readonly credentialsManager: CredentialsManagerService,
    private readonly innerDiskApiService: InnerDiskApiService,
    businessLogger: BusinessLoggerService,
  ) {
    super('InnerDiskService', businessLogger);
    this.logServiceStart();
  }

  /**
   * 获取内盘信息
   * 支持分页查询和自定义API凭据
   *
   * @param params 查询参数
   * @returns 内盘信息查询结果
   */
  async getInnerDiskInfo(
    params: InnerDiskQueryParams = {},
  ): Promise<InnerDiskQueryResult> {
    try {
      const { currentPage = 1, pageSize = 20, apiKey, secret } = params;

      // 获取凭据
      const credentials = this.getCredentials(apiKey, secret);

      // 构建 API 请求
      const requestBody: InnerDiskApiRequest = {
        apiKey: credentials.apiKey,
        secret: credentials.secret,
        currentPage,
        pageSize,
      };

      // 调用外部 API
      const response =
        await this.innerDiskApiService.callInnerDiskApi(requestBody);

      // 提取和转换数据
      const records = response.data?.records || [];
      const totalRecords = response.data?.total || 0;
      const totalPages =
        response.data?.pages || Math.ceil(totalRecords / pageSize);

      this.logger.log(
        `Successfully retrieved ${records.length} inner disk records (page ${currentPage}/${totalPages})`,
      );

      return {
        success: true,
        data: records,
        pagination: {
          current: currentPage,
          size: pageSize,
          total: totalRecords,
          pages: totalPages,
        },
        message: 'Inner disk data retrieved successfully',
      };
    } catch (error) {
      const errorMessage = this.formatError(error);
      this.logger.error(`获取内盘信息失败: ${errorMessage}`, error);
      throw new BadRequestException(`获取内盘信息失败: ${errorMessage}`);
    }
  }

  /**
   * 获取所有内盘数据（支持分页自动获取）
   */
  async getAllInnerDiskData(credentials?: {
    apiKey: string;
    secret: string;
  }): Promise<InnerDiskRecord[]> {
    try {
      const creds =
        credentials || this.credentialsManager.getInnerDiskCredentials();
      let allRecords: InnerDiskRecord[] = [];
      let currentPage = 1;
      const pageSize = 100;
      let hasMore = true;

      while (hasMore) {
        const result = await this.getInnerDiskInfo({
          currentPage,
          pageSize,
          apiKey: creds.apiKey,
          secret: creds.secret,
        });

        allRecords = [...allRecords, ...result.data];
        hasMore = result.pagination.current < result.pagination.pages;
        currentPage++;

        if (hasMore) {
          await this.innerDiskApiService.delay(100); // 避免频率限制
        }
      }

      this.logger.log(
        `Retrieved total ${allRecords.length} inner disk records`,
      );
      return allRecords;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to get all inner disk data: ${errorMessage}`,
        errorStack,
      );
      throw new BadRequestException(`获取所有内盘数据失败: ${errorMessage}`);
    }
  }

  /**
   * 根据代币符号获取内盘数据
   */
  async getInnerDiskByToken(
    tokenSymbol: string,
    credentials?: { apiKey: string; secret: string },
  ): Promise<InnerDiskRecord[]> {
    const allData = await this.getAllInnerDiskData(credentials);
    return allData.filter(
      (record) =>
        record.tokenSymbol.toLowerCase() === tokenSymbol.toLowerCase(),
    );
  }

  /**
   * 获取活跃的内盘数据（poolStatus为active且未停止）
   */
  async getActiveInnerDiskData(credentials?: {
    apiKey: string;
    secret: string;
  }): Promise<InnerDiskRecord[]> {
    const allData = await this.getAllInnerDiskData(credentials);
    return PoolAvailabilityUtil.filterAvailablePools(allData);
  }

  /**
   * 获取内盘数据统计信息
   */
  async getInnerDiskStatistics(credentials?: {
    apiKey: string;
    secret: string;
  }): Promise<{
    totalRecords: number;
    activeRecords: number;
    totalMarketCap: number;
    totalVolume24h: number;
    averagePrice: number;
    chainDistribution: Record<string, number>;
  }> {
    const allData = await this.getAllInnerDiskData(credentials);
    const activeData = PoolAvailabilityUtil.filterAvailablePools(allData);

    const totalMarketCap = allData.reduce(
      (sum, record) => sum + (record.marketCap || 0),
      0,
    );
    const totalVolume24h = allData.reduce(
      (sum, record) => sum + (record.volume24h || 0),
      0,
    );
    const averagePrice =
      allData.length > 0
        ? allData.reduce((sum, record) => sum + (record.price || 0), 0) /
          allData.length
        : 0;

    const chainDistribution: Record<string, number> = {};
    allData.forEach((record) => {
      chainDistribution[record.chain] =
        (chainDistribution[record.chain] || 0) + 1;
    });

    return {
      totalRecords: allData.length,
      activeRecords: activeData.length,
      totalMarketCap: NumberUtils.roundToDecimal(totalMarketCap),
      totalVolume24h: NumberUtils.roundToDecimal(totalVolume24h),
      averagePrice: NumberUtils.roundToDecimal(averagePrice),
      chainDistribution,
    };
  }

  /**
   * 获取 API 凭据（优先使用提供的，否则使用默认的）
   * @private
   */
  private getCredentials(
    apiKey?: string,
    secret?: string,
  ): { apiKey: string; secret: string } {
    if (apiKey && secret) {
      // 验证提供的凭据
      if (!this.accountDataService.validateCredentials(apiKey, secret)) {
        throw new BadRequestException('Invalid API credentials provided');
      }
      return { apiKey, secret };
    }

    return this.credentialsManager.getInnerDiskCredentials();
  }
}
