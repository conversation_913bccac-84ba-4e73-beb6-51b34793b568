import { Injectable, Logger } from '@nestjs/common';
import { InnerDiskService } from './inner-disk.service';
import { InnerDiskRecord } from '../types/inner-disk.types';

/**
 * 池子选择选项
 */
export interface PoolSelectionOptions {
  /** 最小流动性要求 */
  minLiquidity?: number;
  /** 最小价格要求 */
  minPrice?: number;
  /** 是否只选择活跃池子 */
  activeOnly?: boolean;
  /** 是否排除已停止的池子 */
  excludeStopped?: boolean;
  /** 最大池子数量限制 */
  maxPools?: number;
  /** 选择模式 */
  selectionMode: 'random' | 'all' | 'filtered';
  /** 避重时间窗口（毫秒） */
  recentWindowMs?: number;
  /** 最近使用的池子记录 */
  recentlyUsedPools?: Map<number, Date>;
  /** 冷却中的池子记录 */
  cooldownPools?: Map<number, number>;
}

/**
 * 池子选择结果
 */
export interface PoolSelectionResult {
  /** 选择的池子列表 */
  selectedPools: InnerDiskRecord[];
  /** 总可用池子数量 */
  totalAvailable: number;
  /** 过滤后的池子数量 */
  filteredCount: number;
  /** 选择统计信息 */
  statistics: {
    totalPools: number;
    activePools: number;
    liquidityFiltered: number;
    priceFiltered: number;
    recentFiltered: number;
    cooldownFiltered: number;
  };
  /** 执行时间 */
  executionTimeMs: number;
}

/**
 * 可选择的池子信息（简化版）
 */
export interface SelectablePoolInfo {
  poolId: number;
  symbol: string;
  stableCoinSymbol: string;
  price: number;
  liquidity: number;
  volume24h: number;
  poolStatus: string;
  isStop: number;
  lastSelectedAt?: number;
}

/**
 * 交易模块的池子选择服务
 *
 * 职责：
 * - 提供统一的池子选择逻辑
 * - 支持不同的筛选条件和选择模式
 * - 实现避重和冷却机制
 * - 提供缓存机制以提高性能
 *
 * 设计原则：
 * - 复用现有的 InnerDiskService
 * - 支持多种选择模式和筛选条件
 * - 提供详细的统计信息
 * - 遵循现有框架的设计模式
 */
@Injectable()
export class TradingPoolSelectionService {
  private readonly logger = new Logger(TradingPoolSelectionService.name);

  /** 池子数据缓存 */
  private poolsCache: InnerDiskRecord[] = [];
  private cacheExpiry: number = 0;
  private readonly CACHE_TTL = 60000; // 1分钟缓存

  constructor(private readonly innerDiskService: InnerDiskService) {}

  /**
   * 选择池子
   *
   * @param options 选择选项
   * @returns 选择结果
   */
  async selectPools(
    options: PoolSelectionOptions,
  ): Promise<PoolSelectionResult> {
    const startTime = Date.now();

    this.logger.log(
      `开始池子选择: mode=${options.selectionMode}, minLiquidity=${options.minLiquidity}, maxPools=${options.maxPools}`,
    );

    try {
      // 1. 获取所有池子数据
      const allPools = await this.getCachedPools();

      // 2. 应用基础筛选
      const filteredPools = this.applyBasicFilters(allPools, options);

      // 3. 应用避重筛选
      const recentFilteredPools = this.applyRecentFilter(
        filteredPools,
        options,
      );

      // 4. 应用冷却筛选
      const cooldownFilteredPools = this.applyCooldownFilter(
        recentFilteredPools,
        options,
      );

      // 5. 根据选择模式进行最终选择
      const selectedPools = this.applySelectionMode(
        cooldownFilteredPools,
        options,
      );

      // 6. 构建结果
      const result: PoolSelectionResult = {
        selectedPools,
        totalAvailable: allPools.length,
        filteredCount: cooldownFilteredPools.length,
        statistics: {
          totalPools: allPools.length,
          activePools: filteredPools.length,
          liquidityFiltered: filteredPools.length,
          priceFiltered: filteredPools.length,
          recentFiltered: recentFilteredPools.length,
          cooldownFiltered: cooldownFilteredPools.length,
        },
        executionTimeMs: Date.now() - startTime,
      };

      this.logger.log(
        `池子选择完成: 总池子${result.totalAvailable}, 筛选后${result.filteredCount}, 选择${result.selectedPools.length}, 耗时${result.executionTimeMs}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error('池子选择失败:', error);
      throw new Error(`池子选择失败: ${(error as Error).message}`);
    }
  }

  /**
   * 获取单个随机池子
   *
   * @param options 选择选项
   * @returns 选择的池子，如果没有可用池子则返回null
   */
  async selectRandomPool(
    options: Omit<PoolSelectionOptions, 'selectionMode' | 'maxPools'>,
  ): Promise<InnerDiskRecord | null> {
    const result = await this.selectPools({
      ...options,
      selectionMode: 'random',
      maxPools: 1,
    });

    return result.selectedPools.length > 0 ? result.selectedPools[0] : null;
  }

  /**
   * 获取可选择的池子列表（用于前端显示）
   *
   * @param options 筛选选项
   * @returns 可选择的池子信息列表
   */
  async getSelectablePools(
    options: Partial<PoolSelectionOptions> = {},
  ): Promise<SelectablePoolInfo[]> {
    const defaultOptions: PoolSelectionOptions = {
      minLiquidity: 500,
      minPrice: 0,
      activeOnly: true,
      excludeStopped: true,
      selectionMode: 'all',
      ...options,
    };

    const result = await this.selectPools(defaultOptions);

    return result.selectedPools.map((pool) => ({
      poolId: pool.id,
      symbol: pool.tokenSymbol,
      stableCoinSymbol: pool.stableCoinSymbol,
      price: pool.price,
      liquidity: pool.liquidity,
      volume24h: pool.volume24h,
      poolStatus: pool.poolStatus,
      isStop: pool.isStop,
    }));
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.poolsCache = [];
    this.cacheExpiry = 0;
    this.logger.log('池子缓存已清除');
  }

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 获取缓存的池子数据
   */
  private async getCachedPools(): Promise<InnerDiskRecord[]> {
    // 检查缓存
    if (this.poolsCache.length > 0 && Date.now() < this.cacheExpiry) {
      this.logger.debug(
        `使用缓存的池子数据，共 ${this.poolsCache.length} 个池子`,
      );
      return this.poolsCache;
    }

    try {
      this.logger.log('开始获取内盘数据...');
      const innerDiskData = await this.innerDiskService.getAllInnerDiskData();
      this.logger.log(`获取到 ${innerDiskData.length} 条内盘数据`);

      // 更新缓存
      this.poolsCache = innerDiskData;
      this.cacheExpiry = Date.now() + this.CACHE_TTL;

      return innerDiskData;
    } catch (error) {
      this.logger.error('获取池子数据失败:', error);
      // 返回缓存数据作为备用（如果有的话）
      if (this.poolsCache.length > 0) {
        this.logger.warn('使用过期缓存数据作为备用');
        return this.poolsCache;
      }
      return [];
    }
  }

  /**
   * 应用基础筛选条件
   */
  private applyBasicFilters(
    pools: InnerDiskRecord[],
    options: PoolSelectionOptions,
  ): InnerDiskRecord[] {
    let filtered = pools;

    // 活跃状态筛选
    if (options.activeOnly) {
      filtered = filtered.filter(
        (pool) => pool.poolStatus.toLowerCase() === 'active',
      );
    }

    // 排除已停止的池子
    if (options.excludeStopped) {
      filtered = filtered.filter((pool) => pool.isStop === 0);
    }

    // 流动性筛选
    if (options.minLiquidity !== undefined) {
      filtered = filtered.filter(
        (pool) => pool.liquidity >= options.minLiquidity!,
      );
    }

    // 价格筛选
    if (options.minPrice !== undefined) {
      filtered = filtered.filter((pool) => pool.price >= options.minPrice!);
    }

    return filtered;
  }

  /**
   * 应用避重筛选
   */
  private applyRecentFilter(
    pools: InnerDiskRecord[],
    options: PoolSelectionOptions,
  ): InnerDiskRecord[] {
    if (!options.recentlyUsedPools || !options.recentWindowMs) {
      return pools;
    }

    const now = Date.now();
    return pools.filter((pool) => {
      const lastUsed = options.recentlyUsedPools!.get(pool.id);
      return !lastUsed || now - lastUsed.getTime() > options.recentWindowMs!;
    });
  }

  /**
   * 应用冷却筛选
   */
  private applyCooldownFilter(
    pools: InnerDiskRecord[],
    options: PoolSelectionOptions,
  ): InnerDiskRecord[] {
    if (!options.cooldownPools) {
      return pools;
    }

    const now = Date.now();
    return pools.filter((pool) => {
      const cooldownEnd = options.cooldownPools!.get(pool.id) || 0;
      return now > cooldownEnd;
    });
  }

  /**
   * 应用选择模式
   */
  private applySelectionMode(
    pools: InnerDiskRecord[],
    options: PoolSelectionOptions,
  ): InnerDiskRecord[] {
    switch (options.selectionMode) {
      case 'random': {
        if (pools.length === 0) return [];
        const randomIndex = Math.floor(Math.random() * pools.length);
        const selectedPool = pools[randomIndex];
        return options.maxPools === 1 ? [selectedPool] : [selectedPool];
      }

      case 'all':
        return options.maxPools ? pools.slice(0, options.maxPools) : pools;

      case 'filtered':
        return options.maxPools ? pools.slice(0, options.maxPools) : pools;

      default:
        throw new Error(`不支持的选择模式: ${options.selectionMode as string}`);
    }
  }
}
