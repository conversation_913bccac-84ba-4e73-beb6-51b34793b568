import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../../../../modules/auth/guards/jwt-auth.guard';
import { InnerDiskTradeService } from '../services/inner-disk-trade.service';
import { InnerDiskTradeRequestDto } from '../dto/inner-disk-trade.dto';
import { InnerDiskTradeResponse } from '../types/inner-disk.types';

/**
 * 内盘交易控制器
 *
 * 职责：
 * - 处理内盘交易相关的 HTTP 请求
 * - 参数验证和响应格式化
 * - 委托业务逻辑给服务层处理
 *
 * 设计模式：
 * - 参考 inner-disk.controller.ts 的设计模式
 * - 使用框架响应拦截器自动处理响应格式
 * - 保持控制器层简洁
 */
@Controller('inner-disk')
@UseGuards(JwtAuthGuard)
export class InnerDiskTradeController {
  private readonly logger = new Logger(InnerDiskTradeController.name);

  constructor(private readonly innerDiskTradeService: InnerDiskTradeService) {
    this.logger.log('内盘交易控制器初始化完成');
  }

  /**
   * 执行内盘交易
   * POST /inner-disk/trade
   */
  @Post('trade')
  async executeInnerDiskTrade(
    @Body() tradeRequestDto: InnerDiskTradeRequestDto,
  ): Promise<InnerDiskTradeResponse> {
    this.logger.log(
      `收到内盘交易请求，用户数量: ${tradeRequestDto.trades.length}`,
    );

    // 计算总交易操作数
    const totalTrades = tradeRequestDto.trades.reduce(
      (sum, userRequest) => sum + userRequest.innerTradeBean.length,
      0,
    );
    this.logger.log(`总交易操作数: ${totalTrades}`);

    // 转换 DTO 为业务对象
    const tradeRequests = tradeRequestDto.trades.map((trade) => ({
      userBean: {
        apiKey: trade.userBean.apiKey,
        secret: trade.userBean.secret,
      },
      innerTradeBean: trade.innerTradeBean.map((tradeBean) => ({
        poolId: tradeBean.poolId,
        amount: tradeBean.amount,
        tradeType: tradeBean.tradeType,
      })),
    }));

    // 执行交易
    return await this.innerDiskTradeService.executeInnerDiskTrade(
      tradeRequests,
    );
  }

  /**
   * 健康检查
   * GET /inner-disk/trade/health
   */
  @Post('trade/health')
  getTradeHealth(): {
    status: string;
    service: string;
    timestamp: string;
  } {
    return {
      status: 'healthy',
      service: 'inner-disk-trade',
      timestamp: new Date().toISOString(),
    };
  }
}
