import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger,
  Param,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../../../modules/auth/guards/jwt-auth.guard';
import { Public } from '../../../../modules/auth/decorators/public.decorator';
import { InnerDiskService } from '../services/inner-disk.service';
import { InnerDiskQueryDto, InnerDiskAuthDto } from '../dto/inner-disk.dto';
import {
  InnerDiskQueryResult,
  InnerDiskRecord,
} from '../types/inner-disk.types';

/**
 * 内盘信息控制器
 *
 * 提供内盘交易数据的RESTful API接口
 * 使用统一的响应格式 {success, code, message, data, timestamp}
 */
@Controller('inner-disk')
@UseGuards(JwtAuthGuard)
export class InnerDiskController {
  private readonly logger = new Logger(InnerDiskController.name);

  constructor(private readonly innerDiskService: InnerDiskService) {}

  /**
   * 获取内盘信息（分页）
   * GET /inner-disk
   */
  @Get()
  async getInnerDisk(
    @Query() queryDto: InnerDiskQueryDto,
  ): Promise<InnerDiskQueryResult> {
    this.logger.log(
      `获取内盘信息请求: 页码=${queryDto.currentPage}, 页大小=${queryDto.pageSize}`,
    );

    // 直接返回业务数据，让框架处理响应格式
    return await this.innerDiskService.getInnerDiskInfo({
      currentPage: queryDto.currentPage,
      pageSize: queryDto.pageSize,
      apiKey: queryDto.apiKey,
      secret: queryDto.secret,
    });
  }

  /**
   * 获取所有内盘数据
   * GET /inner-disk/all
   */
  @Get('all')
  async getAllInnerDisk(@Query() queryDto: InnerDiskAuthDto): Promise<{
    records: InnerDiskRecord[];
    total: number;
  }> {
    this.logger.log('获取所有内盘数据请求');

    const credentials =
      queryDto.apiKey && queryDto.secret
        ? { apiKey: queryDto.apiKey, secret: queryDto.secret }
        : undefined;

    const data = await this.innerDiskService.getAllInnerDiskData(credentials);

    return {
      records: data,
      total: data.length,
    };
  }

  /**
   * 根据代币符号获取内盘数据
   * GET /inner-disk/token/:symbol
   */
  @Get('token/:symbol')
  async getInnerDiskByToken(
    @Param('symbol') symbol: string,
    @Query() queryDto: InnerDiskAuthDto,
  ): Promise<{
    symbol: string;
    records: InnerDiskRecord[];
    total: number;
  }> {
    this.logger.log(`获取代币${symbol}的内盘数据请求`);

    const credentials =
      queryDto.apiKey && queryDto.secret
        ? { apiKey: queryDto.apiKey, secret: queryDto.secret }
        : undefined;

    const data = await this.innerDiskService.getInnerDiskByToken(
      symbol,
      credentials,
    );

    return {
      symbol,
      records: data,
      total: data.length,
    };
  }

  /**
   * 获取活跃的内盘数据
   * GET /inner-disk/active
   */
  @Get('active')
  async getActiveInnerDisk(@Query() queryDto: InnerDiskAuthDto): Promise<{
    records: InnerDiskRecord[];
    total: number;
  }> {
    this.logger.log('获取活跃内盘数据请求');

    const credentials =
      queryDto.apiKey && queryDto.secret
        ? { apiKey: queryDto.apiKey, secret: queryDto.secret }
        : undefined;

    const data =
      await this.innerDiskService.getActiveInnerDiskData(credentials);

    return {
      records: data,
      total: data.length,
    };
  }

  /**
   * 获取内盘数据统计信息
   * GET /inner-disk/statistics
   */
  @Get('statistics')
  async getInnerDiskStatistics(@Query() queryDto: InnerDiskAuthDto): Promise<{
    totalRecords: number;
    activeRecords: number;
    totalMarketCap: number;
    totalVolume24h: number;
    averagePrice: number;
    chainDistribution: Record<string, number>;
  }> {
    this.logger.log('获取内盘统计信息请求');

    const credentials =
      queryDto.apiKey && queryDto.secret
        ? { apiKey: queryDto.apiKey, secret: queryDto.secret }
        : undefined;

    return await this.innerDiskService.getInnerDiskStatistics(credentials);
  }

  /**
   * 健康检查
   * GET /inner-disk/health
   */
  @Get('health')
  @Public()
  getHealth(): {
    status: string;
    service: string;
    timestamp: string;
  } {
    return {
      status: 'healthy',
      service: 'inner-disk',
      timestamp: new Date().toISOString(),
    };
  }
}
