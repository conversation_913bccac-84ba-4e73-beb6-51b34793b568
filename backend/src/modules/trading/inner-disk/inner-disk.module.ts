import { Module } from '@nestjs/common';
import { InnerDiskController } from './controllers/inner-disk.controller';
import { InnerDiskTradeController } from './controllers/inner-disk-trade.controller';
import { InnerDiskService } from './services/inner-disk.service';
import { InnerDiskApiService } from './services/inner-disk-api.service';
import { InnerDiskTradeService } from './services/inner-disk-trade.service';
import { InnerDiskTradeApiService } from './services/inner-disk-trade-api.service';
import { TradingPoolSelectionService } from './services/trading-pool-selection.service';
import { CommonModule } from '../../../common/common.module';

/**
 * 内盘信息模块
 *
 * 封装内盘数据获取和交易的相关功能
 * 依赖CommonModule获取账户数据服务
 */
@Module({
  imports: [CommonModule],
  controllers: [InnerDiskController, InnerDiskTradeController],
  providers: [
    InnerDiskService,
    InnerDiskApiService,
    InnerDiskTradeService,
    InnerDiskTradeApiService,
    TradingPoolSelectionService,
  ],
  exports: [
    InnerDiskService,
    InnerDiskTradeService,
    TradingPoolSelectionService,
  ],
})
export class InnerDiskModule {}
