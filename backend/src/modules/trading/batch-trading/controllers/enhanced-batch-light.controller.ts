import { Controller, Post, Get, Body, Query, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../auth/decorators/current-user.decorator';
import { User } from '@prisma/client';
import {
  EnhancedBatchLightService,
  type EnhancedBatchLightResult,
} from '../services/enhanced-batch-light.service';
import {
  EnhancedBatchLightDto,
  BatchStatusQueryDto,
} from '../dto/enhanced-batch-light.dto';
import { BatchLightExecutionHistoryDto } from '../types/batch.types';

/**
 * API响应基础接口
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 增强的批量点亮控制器
 * 提供增强的批量点亮功能，包括首次点亮分析、随机数量、批次处理等
 */
@Controller('batch/enhanced-light')
@UseGuards(JwtAuthGuard)
export class EnhancedBatchLightController {
  constructor(
    private readonly enhancedBatchLightService: EnhancedBatchLightService,
  ) {}

  /**
   * 执行增强的批量点亮
   */
  @Post('execute')
  async executeEnhancedBatchLight(
    @Body() dto: EnhancedBatchLightDto,
    @CurrentUser() _user?: User,
  ): Promise<ApiResponse<EnhancedBatchLightResult>> {
    try {
      // 验证数量配置
      this.validateAmountConfig(dto);

      const result =
        await this.enhancedBatchLightService.executeEnhancedBatchLight({
          userBeanList: dto.userBeanList,
          assetId: dto.assetId,
          amount: dto.amount,
          randomAmount: dto.randomAmount,
          batchConfig: dto.batchConfig,
          enableFirstLightAnalysis: dto.enableFirstLightAnalysis !== false, // 默认启用
          customLightCount: dto.customLightCount,
        });

      return {
        code: 200,
        msg: '增强批量点亮启动成功',
        data: result,
      };
    } catch (error) {
      console.error('增强批量点亮执行失败:', error);
      return {
        code: 500,
        msg: `增强批量点亮执行失败: ${(error as Error).message}`,
        data: {} as EnhancedBatchLightResult,
      };
    }
  }

  /**
   * 获取批次执行状态
   */
  @Get('status')
  getBatchStatus(
    @Query() query: BatchStatusQueryDto,
    @CurrentUser() _user?: User,
  ): ApiResponse<EnhancedBatchLightResult | null> {
    try {
      const result = this.enhancedBatchLightService.getBatchStatus(
        query.batchId,
      );

      return {
        code: 200,
        msg: result ? '批次状态获取成功' : '批次不存在',
        data: result,
      };
    } catch (error) {
      console.error('获取批次状态失败:', error);
      return {
        code: 500,
        msg: `获取批次状态失败: ${(error as Error).message}`,
        data: null,
      };
    }
  }

  /**
   * 获取所有活跃批次
   */
  @Get('active-batches')
  getAllActiveBatches(
    @CurrentUser() _user?: User,
  ): ApiResponse<EnhancedBatchLightResult[]> {
    try {
      const batches = this.enhancedBatchLightService.getAllActiveBatches();

      return {
        code: 200,
        msg: '活跃批次获取成功',
        data: batches,
      };
    } catch (error) {
      console.error('获取活跃批次失败:', error);
      return {
        code: 500,
        msg: `获取活跃批次失败: ${(error as Error).message}`,
        data: [],
      };
    }
  }

  /**
   * 获取批量点亮执行历史
   * 参考随机点亮的优秀实现
   */
  @Get('execution-history')
  getBatchExecutionHistory(
    @Query('limit') limit?: string,
    @CurrentUser() _user?: User,
  ): ApiResponse<BatchLightExecutionHistoryDto[]> {
    try {
      const limitNum = limit ? parseInt(limit, 10) : 100;
      const history =
        this.enhancedBatchLightService.getBatchExecutionHistory(limitNum);

      return {
        code: 200,
        msg: '批量点亮执行历史获取成功',
        data: history,
      };
    } catch (error) {
      console.error('获取批量点亮执行历史失败:', error);
      return {
        code: 500,
        msg: `获取批量点亮执行历史失败: ${(error as Error).message}`,
        data: [],
      };
    }
  }

  /**
   * 获取指定批次的执行历史
   */
  @Get('execution-history/:batchId')
  getBatchExecutionHistoryByBatchId(
    @Query('batchId') batchId: string,
    @CurrentUser() _user?: User,
  ): ApiResponse<BatchLightExecutionHistoryDto[]> {
    try {
      if (!batchId) {
        return {
          code: 400,
          msg: '批次ID不能为空',
          data: [],
        };
      }

      const history =
        this.enhancedBatchLightService.getBatchExecutionHistoryByBatchId(
          batchId,
        );

      return {
        code: 200,
        msg: '指定批次执行历史获取成功',
        data: history,
      };
    } catch (error) {
      console.error('获取指定批次执行历史失败:', error);
      return {
        code: 500,
        msg: `获取指定批次执行历史失败: ${(error as Error).message}`,
        data: [],
      };
    }
  }

  /**
   * 清理已完成的批次
   */
  @Post('cleanup-completed')
  cleanupCompletedBatches(
    @CurrentUser() _user?: User,
  ): ApiResponse<{ message: string }> {
    try {
      this.enhancedBatchLightService.cleanupCompletedBatches();

      return {
        code: 200,
        msg: '已完成批次清理成功',
        data: { message: '已清理超过24小时的已完成批次' },
      };
    } catch (error) {
      console.error('清理已完成批次失败:', error);
      return {
        code: 500,
        msg: `清理已完成批次失败: ${(error as Error).message}`,
        data: { message: '清理失败' },
      };
    }
  }

  /**
   * 验证数量配置
   */
  private validateAmountConfig(dto: EnhancedBatchLightDto): void {
    const hasFixedAmount = dto.amount !== undefined;
    const hasRandomAmount = dto.randomAmount?.enabled === true;

    if (!hasFixedAmount && !hasRandomAmount) {
      throw new Error('必须指定固定数量或启用随机数量');
    }

    if (hasFixedAmount && hasRandomAmount) {
      throw new Error('不能同时使用固定数量和随机数量');
    }

    if (hasRandomAmount && dto.randomAmount) {
      if (dto.randomAmount.minAmount >= dto.randomAmount.maxAmount) {
        throw new Error('随机数量的最小值必须小于最大值');
      }
    }
  }

  /**
   * 生成随机数量（整数）
   */
  private generateRandomAmount(min: number, max: number): number {
    // 确保生成的是整数
    const minInt = Math.floor(min);
    const maxInt = Math.floor(max);
    return Math.floor(Math.random() * (maxInt - minInt + 1)) + minInt;
  }
}
