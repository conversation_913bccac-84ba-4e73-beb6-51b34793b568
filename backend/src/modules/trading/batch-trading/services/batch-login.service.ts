import { Injectable, Logger } from '@nestjs/common';
import { BatchLoginDto } from '../dto/batch-login.dto';
import { LoginResult, BatchLoginResponse } from '../types/batch.types';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { CredentialsManagerService } from '../../../../core/infrastructure/credentials-manager.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';

/**
 * 批量登录服务
 * 专门处理批量登录验证逻辑，符合单一职责原则
 */
@Injectable()
export class BatchLoginService extends BaseBusinessService {
  protected readonly logger = new Logger(BatchLoginService.name);

  constructor(
    private readonly accountDataService: AccountDataService,
    private readonly credentialsManager: CredentialsManagerService,
    businessLogger: BusinessLoggerService,
  ) {
    super('BatchLoginService', businessLogger);
  }

  /**
   * 批量登录验证
   * @param batchLoginDto 批量登录DTO
   * @returns 批量登录结果
   */
  batchLogin(batchLoginDto: BatchLoginDto): BatchLoginResponse {
    try {
      const results: LoginResult[] = [];

      // 使用 AccountDataService 动态加载凭据
      const accounts = this.accountDataService.getAccounts();

      const validCredentials: Record<string, string> = {};
      accounts.forEach((account) => {
        validCredentials[account.apiKey] = account.secret;
      });

      // 验证每个账户
      for (const account of batchLoginDto.userBeanList) {
        const result = this.validateSingleAccount(account, validCredentials);
        results.push(result);
      }

      // 统计结果
      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.length - successCount;

      return {
        code: 200,
        msg: '批量登录完成',
        data: {
          total: results.length,
          success: successCount,
          failure: failureCount,
          results,
        },
      };
    } catch (error: unknown) {
      this.logger.error('批量登录失败:', error);
      return {
        code: 500,
        msg: '服务器内部错误',
        data: {
          total: 0,
          success: 0,
          failure: 0,
          results: [],
        },
      };
    }
  }

  /**
   * 验证单个账户
   * @param account 账户信息
   * @param _validCredentials 有效凭据映射（未使用）
   * @returns 登录结果
   */
  private validateSingleAccount(
    account: { email: string; password: string },
    _validCredentials: Record<string, string>,
  ): LoginResult {
    try {
      // 使用 AccountDataService 查找匹配的账户
      const accounts = this.accountDataService.getAccounts();

      const matchedAccount = accounts.find(
        (acc) =>
          acc.email === account.email && acc.password === account.password,
      );

      if (!matchedAccount) {
        return {
          email: account.email,
          status: 'failed',
          message: 'Invalid email or password',
          success: false,
        };
      }

      // 可以在这里添加额外的验证逻辑，比如检查账户状态等
      this.recordLoginAttempt(matchedAccount.apiKey, true);

      return {
        email: account.email,
        status: 'success',
        message: 'Login successful',
        success: true,
      };
    } catch (error: unknown) {
      this.logger.error(`账户 ${account.email} 验证失败:`, error);

      this.recordLoginAttempt('unknown', false);

      return {
        email: account.email,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        success: false,
      };
    }
  }

  /**
   * 记录登录尝试（可选功能，用于审计）
   * @param apiKey API密钥
   * @param success 是否成功
   */
  private recordLoginAttempt(apiKey: string, success: boolean): void {
    try {
      // 记录登录尝试结果用于审计和监控
      // 在生产环境中应该使用适当的日志系统
      if (process.env.NODE_ENV !== 'production') {
        this.logger.debug(
          `Login attempt: ${apiKey} - ${success ? 'SUCCESS' : 'FAILURE'}`,
        );
      }
    } catch (error) {
      // 记录失败不应该影响主要业务逻辑
      this.logger.warn('Failed to record login attempt:', error);
    }
  }

  /**
   * 获取有效凭据映射
   * 将来可以从数据库或配置服务获取
   * @returns 凭据映射
   */
  private getValidCredentials(): Record<string, string> {
    const accounts = this.accountDataService.getAccounts();

    const validCredentials: Record<string, string> = {};
    accounts.forEach((account) => {
      validCredentials[account.apiKey] = account.secret;
    });

    return validCredentials;
  }

  /**
   * 验证API凭据
   * @param apiKey API密钥
   * @param secret 密钥
   * @returns 是否有效
   */
  validateCredentials(apiKey: string, secret: string): boolean {
    try {
      const validCredentials = this.getValidCredentials();
      const expectedSecret = validCredentials[apiKey];
      return expectedSecret === secret;
    } catch (error) {
      this.logger.error('凭据验证失败:', error);
      return false;
    }
  }

  /**
   * 获取账户统计信息
   * @returns 账户统计
   */
  getAccountStats(): {
    totalAccounts: number;
    activeAccounts: number;
  } {
    try {
      const accounts = this.accountDataService.getAccounts();

      return {
        totalAccounts: accounts.length,
        activeAccounts: accounts.length, // 假设所有账户都是活跃的
      };
    } catch (error) {
      this.logger.error('获取账户统计失败:', error);
      return {
        totalAccounts: 0,
        activeAccounts: 0,
      };
    }
  }
}
