import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../../../core/database/database.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';

/**
 * Symbol到AssetId的查询结果接口
 */
export interface SymbolToAssetResult {
  symbol: string;
  matches: Array<{
    assetId: number;
    tokenName: string;
    lightNum: number;
    postId: number;
    status: string;
    canLight: boolean; // light_num < 999
  }>;
  isUnique: boolean;
  selectedAssetId?: number;
}

/**
 * 点亮验证结果接口
 */
export interface LightValidationResult {
  assetId: number;
  symbol: string;
  tokenName: string;
  lightNum: number;
  canLight: boolean;
  postId: number;
  status: string;
}

/**
 * 点亮功能查询优化服务
 * 直接查询 square_tag_token 表，提供高性能的 symbol 到 asset_id 转换和点亮验证
 */
@Injectable()
export class LightQueryOptimizerService extends BaseBusinessService {
  protected readonly logger = new Logger(LightQueryOptimizerService.name);

  constructor(
    private readonly db: DatabaseService,
    businessLogger: BusinessLoggerService,
  ) {
    super('LightQueryOptimizerService', businessLogger);
  }

  /**
   * 根据 symbol 查询对应的 asset_id 和点亮信息
   * 直接查询 square_tag_token 表，避免通过接口获取完整帖子数据
   *
   * @param symbol 代币符号
   * @returns 查询结果，包含所有匹配的 asset_id 和点亮状态
   */
  async resolveSymbolToAsset(symbol: string): Promise<SymbolToAssetResult> {
    const normalizedSymbol = symbol.trim();

    try {
      // 直接查询 square_tag_token 表，使用参数化查询
      const matches = await this.db.$queryRawUnsafe<
        Array<{
          asset_id: bigint;
          tokenName: string;
          tokenSymbol: string;
          light_num: number;
          post_id: number;
          status: string;
        }>
      >(
        `SELECT asset_id, tokenName, tokenSymbol, light_num, post_id, status
         FROM square_tag_token
         WHERE tokenSymbol = ?
         ORDER BY light_num ASC, asset_id ASC`,
        normalizedSymbol,
      );

      const processedMatches = matches.map((match) => ({
        assetId: Number(match.asset_id),
        tokenName: match.tokenName,
        lightNum: match.light_num,
        postId: match.post_id,
        status: match.status,
        canLight: match.light_num < 999,
      }));

      return {
        symbol: normalizedSymbol,
        matches: processedMatches,
        isUnique: matches.length === 1,
        selectedAssetId:
          matches.length === 1 ? Number(matches[0].asset_id) : undefined,
      };
    } catch (error) {
      this.logger.error('Symbol 查询失败:', error);
      return {
        symbol: normalizedSymbol,
        matches: [],
        isUnique: false,
      };
    }
  }

  /**
   * 验证指定 asset_id 是否可以点亮
   * 检查 light_num 字段是否小于 999
   *
   * @param assetId 资产ID
   * @returns 点亮验证结果
   */
  async validateLightability(
    assetId: number,
  ): Promise<LightValidationResult | null> {
    try {
      // 使用参数化查询避免SQL注入和连接问题
      const result = await this.db.$queryRawUnsafe<
        Array<{
          asset_id: bigint;
          tokenName: string;
          tokenSymbol: string;
          light_num: number;
          post_id: number;
          status: string;
        }>
      >(
        `SELECT asset_id, tokenName, tokenSymbol, light_num, post_id, status
         FROM square_tag_token
         WHERE asset_id = ?
         LIMIT 1`,
        assetId,
      );

      if (result.length === 0) {
        return null;
      }

      const token = result[0];
      return {
        assetId: Number(token.asset_id),
        symbol: token.tokenSymbol,
        tokenName: token.tokenName,
        lightNum: token.light_num,
        canLight: token.light_num < 999,
        postId: token.post_id,
        status: token.status,
      };
    } catch (error) {
      this.logger.error('点亮验证失败:', error);
      return null;
    }
  }

  /**
   * 批量查询多个 symbol 的 asset_id 信息
   * 高性能批量查询，适用于批量处理场景
   *
   * @param symbols 代币符号数组
   * @returns 批量查询结果
   */
  async batchResolveSymbols(
    symbols: string[],
  ): Promise<Map<string, SymbolToAssetResult>> {
    const normalizedSymbols = symbols.map((s) => s.trim());
    const results = new Map<string, SymbolToAssetResult>();

    try {
      if (normalizedSymbols.length === 0) {
        return results;
      }

      // 使用循环查询每个symbol（避免复杂的IN查询语法问题）
      const allMatches: Array<{
        asset_id: bigint;
        tokenName: string;
        tokenSymbol: string;
        light_num: number;
        post_id: number;
        status: string;
      }> = [];

      for (const symbol of normalizedSymbols) {
        const symbolMatches = await this.db.$queryRawUnsafe<
          Array<{
            asset_id: bigint;
            tokenName: string;
            tokenSymbol: string;
            light_num: number;
            post_id: number;
            status: string;
          }>
        >(
          `SELECT asset_id, tokenName, tokenSymbol, light_num, post_id, status
           FROM square_tag_token
           WHERE tokenSymbol = ?
           ORDER BY light_num ASC, asset_id ASC`,
          symbol,
        );
        allMatches.push(...symbolMatches);
      }

      const matches = allMatches;

      // 按 symbol 分组处理结果
      const groupedMatches = new Map<string, typeof matches>();
      matches.forEach((match) => {
        const symbol = match.tokenSymbol;
        if (!groupedMatches.has(symbol)) {
          groupedMatches.set(symbol, []);
        }
        groupedMatches.get(symbol)!.push(match);
      });

      // 为每个 symbol 构建结果
      normalizedSymbols.forEach((symbol) => {
        const symbolMatches = groupedMatches.get(symbol) || [];
        const processedMatches = symbolMatches.map((match) => ({
          assetId: Number(match.asset_id),
          tokenName: match.tokenName,
          lightNum: match.light_num,
          postId: match.post_id,
          status: match.status,
          canLight: match.light_num < 999,
        }));

        results.set(symbol, {
          symbol,
          matches: processedMatches,
          isUnique: symbolMatches.length === 1,
          selectedAssetId:
            symbolMatches.length === 1
              ? Number(symbolMatches[0].asset_id)
              : undefined,
        });
      });

      return results;
    } catch (error) {
      this.logger.error('批量 Symbol 查询失败:', error);
      return results;
    }
  }

  /**
   * 获取所有可点亮的代币列表
   * 查询 light_num < 999 的所有代币
   *
   * @param limit 限制返回数量，默认 50
   * @returns 可点亮的代币列表
   */
  async getLightableTokens(
    limit: number = 50,
  ): Promise<LightValidationResult[]> {
    try {
      const tokens = await this.db.$queryRawUnsafe<
        Array<{
          asset_id: bigint;
          tokenName: string;
          tokenSymbol: string;
          light_num: number;
          post_id: number;
          status: string;
        }>
      >(
        `SELECT asset_id, tokenName, tokenSymbol, light_num, post_id, status
         FROM square_tag_token
         WHERE light_num < 999
         AND tokenSymbol IS NOT NULL
         ORDER BY light_num DESC, asset_id ASC
         LIMIT ?`,
        limit,
      );

      return tokens.map((token) => ({
        assetId: Number(token.asset_id),
        symbol: token.tokenSymbol,
        tokenName: token.tokenName,
        lightNum: token.light_num,
        canLight: true, // 已经过滤了 light_num < 999
        postId: token.post_id,
        status: token.status,
      }));
    } catch (error) {
      this.logger.error('获取可点亮代币失败:', error);
      return [];
    }
  }

  /**
   * 获取代币的点亮统计信息
   *
   * @param symbol 可选的代币符号，如果不提供则返回所有代币的统计
   * @param limit 限制返回数量，默认 20
   * @returns 点亮统计信息
   */
  async getLightStatistics(symbol?: string, limit: number = 20) {
    try {
      if (symbol) {
        // 查询特定symbol的统计
        const stats = await this.db.$queryRawUnsafe(
          `SELECT tokenSymbol,
                  COUNT(*) as total_posts,
                  MIN(light_num) as min_light_num,
                  MAX(light_num) as max_light_num,
                  AVG(light_num) as avg_light_num,
                  SUM(CASE WHEN light_num < 999 THEN 1 ELSE 0 END) as lightable_count,
                  SUM(CASE WHEN light_num >= 999 THEN 1 ELSE 0 END) as full_light_count,
                  MIN(asset_id) as min_asset_id,
                  MAX(asset_id) as max_asset_id
           FROM square_tag_token
           WHERE tokenSymbol = ?
           GROUP BY tokenSymbol`,
          symbol.trim(),
        );
        return stats;
      } else {
        // 查询所有symbol的统计
        const stats = await this.db.$queryRawUnsafe(
          `SELECT tokenSymbol,
                  COUNT(*) as total_posts,
                  MIN(light_num) as min_light_num,
                  MAX(light_num) as max_light_num,
                  AVG(light_num) as avg_light_num,
                  SUM(CASE WHEN light_num < 999 THEN 1 ELSE 0 END) as lightable_count,
                  SUM(CASE WHEN light_num >= 999 THEN 1 ELSE 0 END) as full_light_count,
                  MIN(asset_id) as min_asset_id,
                  MAX(asset_id) as max_asset_id
           FROM square_tag_token
           WHERE tokenSymbol IS NOT NULL
           GROUP BY tokenSymbol
           ORDER BY total_posts DESC
           LIMIT ?`,
          limit,
        );
        return stats;
      }
    } catch (error) {
      this.logger.error('获取点亮统计失败:', error);
      return [];
    }
  }
}
