import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../../../core/database/database.service';
import { GetAllPostDto } from '../dto/get-all-post.dto';
import { GetAllPostResponse } from '../types/batch.types';
import { BatchLoginService } from './batch-login.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { PaginationUtil } from '../../../../core/shared/pagination.util';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';

/**
 * 数据库查询结果类型定义
 */
interface SquareTagTokenRecord {
  id: number;
  asset_id: bigint;
  tokenName: string;
  tokenSymbol: string;
  light_num: number;
  post_id: number;
  status: string;
  stake: bigint;
  created_time: Date;
}

interface CountResult {
  total: bigint;
}

/**
 * 帖子查询服务
 * 专门处理帖子数据查询逻辑，符合单一职责原则
 */
@Injectable()
export class PostQueryService extends BaseBusinessService {
  protected readonly logger = new Logger(PostQueryService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly loginService: BatchLoginService,
    businessLogger: BusinessLoggerService,
  ) {
    super('PostQueryService', businessLogger);
  }

  /**
   * 获取所有帖子数据
   * 使用数据库查询替代mock数据，基于square_tag_token表
   * @param getAllPostDto 获取帖子数据DTO
   * @returns 帖子数据响应
   */
  async getAllPost(getAllPostDto: GetAllPostDto): Promise<GetAllPostResponse> {
    try {
      // 验证凭据
      const isValidCredentials = this.loginService.validateCredentials(
        getAllPostDto.apiKey,
        getAllPostDto.secret,
      );

      if (!isValidCredentials) {
        return this.createErrorResponse(
          getAllPostDto,
          401,
          'Invalid API credentials',
        );
      }

      // 执行数据库查询
      const queryResult = await this.executePostQuery(getAllPostDto);

      return this.createSuccessResponse(getAllPostDto, queryResult);
    } catch (error: unknown) {
      this.logger.error('获取帖子数据失败:', error);
      return this.createErrorResponse(
        getAllPostDto,
        500,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * 执行帖子查询
   * @param getAllPostDto 查询参数
   * @returns 查询结果
   */
  private async executePostQuery(getAllPostDto: GetAllPostDto): Promise<{
    records: SquareTagTokenRecord[];
    total: number;
    pages: number;
  }> {
    // 计算分页参数
    const { limit, offset } = PaginationUtil.calculateSqlLimitOffset(
      getAllPostDto.currentPage,
      getAllPostDto.pageSize,
    );

    // 执行查询获取数据
    let records: SquareTagTokenRecord[];
    let totalResult: CountResult[];

    if (getAllPostDto.symbol && typeof getAllPostDto.symbol === 'string') {
      const symbol = getAllPostDto.symbol.trim();
      records = await this.db.$queryRaw<SquareTagTokenRecord[]>`
        SELECT 
          id,
          asset_id,
          tokenName,
          tokenSymbol,
          light_num,
          post_id,
          status,
          stake,
          created_time
        FROM square_tag_token 
        WHERE tokenSymbol IS NOT NULL
        AND tokenSymbol = ${symbol}
        ORDER BY created_time DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      // 获取总数
      totalResult = await this.db.$queryRaw<CountResult[]>`
        SELECT COUNT(*) as total
        FROM square_tag_token 
        WHERE tokenSymbol IS NOT NULL
        AND tokenSymbol = ${symbol}
      `;
    } else {
      records = await this.db.$queryRaw<SquareTagTokenRecord[]>`
        SELECT 
          id,
          asset_id,
          tokenName,
          tokenSymbol,
          light_num,
          post_id,
          status,
          stake,
          created_time
        FROM square_tag_token 
        WHERE tokenSymbol IS NOT NULL
        ORDER BY created_time DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      // 获取总数
      totalResult = await this.db.$queryRaw<CountResult[]>`
        SELECT COUNT(*) as total
        FROM square_tag_token 
        WHERE tokenSymbol IS NOT NULL
      `;
    }

    const total = Number(totalResult[0]?.total || 0);
    const pages = Math.ceil(total / getAllPostDto.pageSize);

    return { records, total, pages };
  }

  /**
   * 转换数据格式
   * @param records 原始数据库记录
   * @returns 格式化的帖子记录
   */
  private formatPostRecords(records: SquareTagTokenRecord[]) {
    return records.map((record) => ({
      postId: Number(record.post_id || record.id),
      assetId: Number(record.asset_id),
      userId: 1, // 默认值
      poolId: null,
      tokenId: Number(record.asset_id), // 使用assetId作为tokenId
      tradePairId: null,
      content: `${record.tokenName} (${record.tokenSymbol}) - Light: ${record.light_num}`,
      source: 0, // 默认source
      createdTime: record.created_time.toISOString(),
      tweetUserId: null,
      tweetId: null,
      tweetUserName: null,
      tweetNickName: null,
      tweetUrl: null,
      tweetLikeCount: 0,
      tweetUserPhotoUrl: null,
      tweetRetweetCount: 0,
      tweetReplyCount: 0,
      tweetQuoteCount: 0,
      tweetBookmarkCount: 0,
      tweetImpressionCount: 0,
      logo: '',
      tokenSymbol: record.tokenSymbol,
      tokenName: record.tokenName,
      lightNum: Number(record.light_num),
      status: Number(record.status) || 0,
      stake: Number(record.stake) || 0,
      change24h: 0,
      innerPrice: 0,
      outerPrice: 0,
      marketCap: null,
      process: 0,
      assetProperty: 0,
      userAvatar: '',
      nickName: 'System',
      lighted: false,
      isLiked: false,
      images: [],
      stableCoinSymbol: null,
      isBlueV: 0,
      postLikeCount: 0,
      postCommentCount: 0,
      postShareCount: 0,
      postViewCount: 0,
      postHotScore: 0,
    }));
  }

  /**
   * 创建成功响应
   */
  private createSuccessResponse(
    getAllPostDto: GetAllPostDto,
    queryResult: {
      records: SquareTagTokenRecord[];
      total: number;
      pages: number;
    },
  ): GetAllPostResponse {
    const formattedRecords = this.formatPostRecords(queryResult.records);

    return {
      code: 200,
      msg: '成功',
      data: {
        email: null,
        phone: null,
        password: null,
        apiKey: getAllPostDto.apiKey,
        secret: getAllPostDto.secret,
        data: {
          message: null,
          code: 200,
          timezone: null,
          data: {
            records: formattedRecords,
            total: queryResult.total,
            size: getAllPostDto.pageSize,
            current: getAllPostDto.currentPage,
            pages: queryResult.pages,
          },
        },
      },
    };
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(
    getAllPostDto: GetAllPostDto,
    code: number,
    message: string,
  ): GetAllPostResponse {
    return {
      code,
      msg: code === 401 ? '认证失败' : '服务器内部错误',
      data: {
        email: null,
        phone: null,
        password: null,
        apiKey: getAllPostDto.apiKey,
        secret: getAllPostDto.secret,
        data: {
          message,
          code,
          timezone: null,
          data: {
            records: [],
          },
        },
      },
    };
  }

  /**
   * 获取帖子统计信息
   * @returns 帖子统计
   */
  async getPostStats(): Promise<{
    totalPosts: number;
    totalTokens: number;
    avgLightNum: number;
  }> {
    try {
      const stats = await this.db.$queryRaw<
        Array<{
          total_posts: bigint;
          total_tokens: bigint;
          avg_light_num: number;
        }>
      >`
        SELECT 
          COUNT(*) as total_posts,
          COUNT(DISTINCT tokenSymbol) as total_tokens,
          AVG(light_num) as avg_light_num
        FROM square_tag_token 
        WHERE tokenSymbol IS NOT NULL
      `;

      const result = stats[0];
      return {
        totalPosts: Number(result?.total_posts || 0),
        totalTokens: Number(result?.total_tokens || 0),
        avgLightNum: Number(result?.avg_light_num || 0),
      };
    } catch (error) {
      this.logger.error('获取帖子统计失败:', error);
      return {
        totalPosts: 0,
        totalTokens: 0,
        avgLightNum: 0,
      };
    }
  }

  /**
   * 根据条件过滤帖子
   * @param filters 过滤条件
   * @returns 过滤后的帖子
   */
  async filterPosts(filters: {
    symbol?: string;
    minLightNum?: number;
    maxLightNum?: number;
    limit?: number;
  }) {
    try {
      const limit = filters.limit || 50;

      // 使用Prisma的安全查询方式
      let records: SquareTagTokenRecord[];

      if (filters.symbol) {
        records = await this.db.$queryRaw<SquareTagTokenRecord[]>`
          SELECT
            id, asset_id, tokenName, tokenSymbol, light_num,
            post_id, status, stake, created_time
          FROM square_tag_token
          WHERE tokenSymbol IS NOT NULL
            AND tokenSymbol = ${filters.symbol}
            ${filters.minLightNum !== undefined ? `AND light_num >= ${filters.minLightNum}` : ''}
            ${filters.maxLightNum !== undefined ? `AND light_num <= ${filters.maxLightNum}` : ''}
          ORDER BY light_num DESC, created_time DESC
          LIMIT ${limit}
        `;
      } else {
        records = await this.db.$queryRaw<SquareTagTokenRecord[]>`
          SELECT
            id, asset_id, tokenName, tokenSymbol, light_num,
            post_id, status, stake, created_time
          FROM square_tag_token
          WHERE tokenSymbol IS NOT NULL
            ${filters.minLightNum !== undefined ? `AND light_num >= ${filters.minLightNum}` : ''}
            ${filters.maxLightNum !== undefined ? `AND light_num <= ${filters.maxLightNum}` : ''}
          ORDER BY light_num DESC, created_time DESC
          LIMIT ${limit}
        `;
      }

      return this.formatPostRecords(records);
    } catch (error) {
      this.logger.error('帖子过滤失败:', error);
      return [];
    }
  }
}
