import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../../../core/database/database.service';
import {
  AccountData,
  UserBean,
  DatabaseUser,
  DatabaseLightRecord,
} from '../../../../core/shared/common.types';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';

/**
 * 首次点亮分析结果
 */
interface FirstLightAnalysisResult {
  totalAccounts: number;
  firstLightAccounts: number;
  alreadyLightedAccounts: number;
  firstLightUserBeanList: UserBean[];
  alreadyLightedUserBeanList: UserBean[];
  executionTimeMs: number;
}

/**
 * 查询参数接口
 */
interface FirstLightQueryParams {
  assetId: number;
  postId?: number;
  batchSize?: number;
}

/**
 * 查询配置常量
 */
const QUERY_CONFIG = {
  DEFAULT_BATCH_SIZE: 1000,
  MAX_BATCH_SIZE: 1000,
  DEFAULT_LIMIT: 100,
} as const;

/**
 * 首次点亮分析服务
 * 高性能查询和筛选首次点亮的账户
 *
 * 架构设计原则：
 * - 高封装：将复杂的数据库查询逻辑封装在私有方法中
 * - 低耦合：通过接口定义清晰的数据结构，减少对外部实现的依赖
 * - 单一职责：专注于首次点亮分析的核心业务逻辑
 */
@Injectable()
export class FirstLightAnalyzerService extends BaseBusinessService {
  protected readonly logger = new Logger(FirstLightAnalyzerService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly accountDataService: AccountDataService,
    businessLogger: BusinessLoggerService,
  ) {
    super('FirstLightAnalyzerService', businessLogger);
  }

  /**
   * 数据库查询配置
   */
  private readonly queryConfig = {
    batchSize: QUERY_CONFIG.DEFAULT_BATCH_SIZE,
    maxBatchSize: QUERY_CONFIG.MAX_BATCH_SIZE,
    defaultLimit: QUERY_CONFIG.DEFAULT_LIMIT,
  };

  /**
   * 分析首次点亮账户
   * 主要业务逻辑方法，协调各个子模块完成完整的分析流程
   *
   * @param params 查询参数
   * @returns 分析结果
   */
  async analyzeFirstLightAccounts(
    params: FirstLightQueryParams,
  ): Promise<FirstLightAnalysisResult> {
    const startTime = Date.now();
    const accounts = this.accountDataService.getAccounts();
    this.logger.log(
      `开始分析首次点亮账户，assetId: ${params.assetId}, 总账户数: ${accounts.length}`,
    );

    try {
      // 1. 构建用户映射关系
      const userIdMap = await this.buildUserIdMapping();

      // 2. 获取已点亮用户集合
      const lightedUserIds = await this.getLightedUserIds(
        userIdMap,
        params.assetId,
        params.postId,
      );

      // 3. 分类账户
      const { firstLightAccounts, alreadyLightedAccounts } =
        this.categorizeAccounts(userIdMap, lightedUserIds);

      // 4. 构建分析结果
      const result = this.buildAnalysisResult(
        firstLightAccounts,
        alreadyLightedAccounts,
        Date.now() - startTime,
      );

      this.logAnalysisCompletion(result);
      return result;
    } catch (error) {
      this.logger.error('首次点亮分析失败:', error);
      throw new Error(`首次点亮分析失败: ${(error as Error).message}`);
    }
  }

  /**
   * 构建用户ID映射关系
   * 将API密钥映射到用户ID，为后续查询做准备
   *
   * @returns API密钥到用户ID的映射
   */
  private async buildUserIdMapping(): Promise<Map<string, number>> {
    const accounts = this.accountDataService.getAccounts();
    const apiKeys = accounts.map((account) => account.apiKey);
    const existingUsers = await this.batchQueryExistingUsers(apiKeys);

    return new Map(existingUsers.map((user) => [user.apiKey, user.id]));
  }

  /**
   * 获取已点亮用户ID集合
   * 查询指定资产的所有点亮记录，返回已点亮的用户ID集合
   *
   * @param userIdMap 用户ID映射
   * @param assetId 资产ID
   * @param postId 可选的帖子ID
   * @returns 已点亮用户ID集合
   */
  private async getLightedUserIds(
    userIdMap: Map<string, number>,
    assetId: number,
    postId?: number,
  ): Promise<Set<number>> {
    const userIds = Array.from(userIdMap.values());
    const existingLightRecords = await this.batchQueryLightRecords(
      userIds,
      assetId,
      postId,
    );

    return new Set(existingLightRecords.map((record) => record.userId));
  }

  /**
   * 分类账户
   * 根据点亮状态将账户分为首次点亮和已点亮两类
   *
   * @param userIdMap 用户ID映射
   * @param lightedUserIds 已点亮用户ID集合
   * @returns 分类后的账户数组
   */
  private categorizeAccounts(
    userIdMap: Map<string, number>,
    lightedUserIds: Set<number>,
  ): {
    firstLightAccounts: AccountData[];
    alreadyLightedAccounts: AccountData[];
  } {
    const firstLightAccounts: AccountData[] = [];
    const alreadyLightedAccounts: AccountData[] = [];
    const accounts = this.accountDataService.getAccounts();

    for (const account of accounts) {
      const userId = userIdMap.get(account.apiKey);

      if (!userId) {
        // 用户不存在，跳过（需要先创建用户）
        continue;
      }

      if (lightedUserIds.has(userId)) {
        alreadyLightedAccounts.push(account);
      } else {
        firstLightAccounts.push(account);
      }
    }

    return { firstLightAccounts, alreadyLightedAccounts };
  }

  /**
   * 构建分析结果
   * 将分析数据组装成标准的返回格式
   *
   * @param firstLightAccounts 首次点亮账户
   * @param alreadyLightedAccounts 已点亮账户
   * @param executionTime 执行时间
   * @returns 完整的分析结果
   */
  private buildAnalysisResult(
    firstLightAccounts: AccountData[],
    alreadyLightedAccounts: AccountData[],
    executionTime: number,
  ): FirstLightAnalysisResult {
    const accounts = this.accountDataService.getAccounts();
    return {
      totalAccounts: accounts.length,
      firstLightAccounts: firstLightAccounts.length,
      alreadyLightedAccounts: alreadyLightedAccounts.length,
      firstLightUserBeanList: this.convertToUserBeans(firstLightAccounts),
      alreadyLightedUserBeanList: this.convertToUserBeans(
        alreadyLightedAccounts,
      ),
      executionTimeMs: executionTime,
    };
  }

  /**
   * 转换为UserBean格式
   * 提取账户中的关键信息，用于批量操作
   *
   * @param accounts 账户数组
   * @returns UserBean数组
   */
  private convertToUserBeans(accounts: AccountData[]): UserBean[] {
    return accounts.map((account) => ({
      apiKey: account.apiKey,
      secret: account.secret,
    }));
  }

  /**
   * 记录分析完成日志
   * 统一的日志格式，便于监控和调试
   *
   * @param result 分析结果
   */
  private logAnalysisCompletion(result: FirstLightAnalysisResult): void {
    this.logger.log(
      `首次点亮分析完成: 总账户${result.totalAccounts}, 首次点亮${result.firstLightAccounts}, 已点亮${result.alreadyLightedAccounts}, 耗时${result.executionTimeMs}ms`,
    );
  }

  /**
   * 批量查询现有用户
   * 使用分批查询策略提高性能，避免单次查询过多数据
   *
   * @param apiKeys API密钥列表
   * @returns 用户ID和API密钥的映射列表
   */
  private async batchQueryExistingUsers(
    apiKeys: string[],
  ): Promise<Array<{ id: number; apiKey: string }>> {
    const results: Array<{ id: number; apiKey: string }> = [];

    for (let i = 0; i < apiKeys.length; i += this.queryConfig.batchSize) {
      const batch = apiKeys.slice(i, i + this.queryConfig.batchSize);

      if (batch.length === 0) continue;

      const batchResults = await this.queryUsersByApiKeys(batch);
      const formattedResults = this.formatUserQueryResults(batchResults);

      results.push(...formattedResults);
    }

    return results;
  }

  /**
   * 查询指定API密钥对应的用户信息
   * 封装数据库查询逻辑，提高代码可维护性
   *
   * @param apiKeys API密钥数组
   * @returns 数据库查询结果
   */
  private async queryUsersByApiKeys(
    apiKeys: string[],
  ): Promise<DatabaseUser[]> {
    const query = `
      SELECT user_id, api_key
      FROM user_api_key
      WHERE api_key IN (${apiKeys.map(() => '?').join(',')})
      AND enabled = 1
    `;

    return await this.db.$queryRawUnsafe<DatabaseUser[]>(query, ...apiKeys);
  }

  /**
   * 格式化用户查询结果
   * 将数据库字段映射为服务层统一接口
   *
   * @param dbResults 数据库查询结果
   * @returns 格式化后的用户信息
   */
  private formatUserQueryResults(
    dbResults: DatabaseUser[],
  ): Array<{ id: number; apiKey: string }> {
    return dbResults.map((row) => ({
      id: row.user_id,
      apiKey: row.api_key,
    }));
  }

  /**
   * 批量查询点亮记录
   * 使用分批查询和去重策略，确保查询性能和数据准确性
   *
   * @param userIds 用户ID列表
   * @param assetId 资产ID
   * @param postId 帖子ID（可选）
   * @returns 点亮记录列表
   */
  private async batchQueryLightRecords(
    userIds: number[],
    assetId: number,
    postId?: number,
  ): Promise<DatabaseLightRecord[]> {
    if (userIds.length === 0) {
      return [];
    }

    const results: DatabaseLightRecord[] = [];

    for (let i = 0; i < userIds.length; i += this.queryConfig.batchSize) {
      const batch = userIds.slice(i, i + this.queryConfig.batchSize);

      if (batch.length === 0) continue;

      const batchResults = await this.queryLightRecordsByUsers(
        batch,
        assetId,
        postId,
      );

      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 查询指定用户的点亮记录
   * 封装复杂的SQL查询逻辑，支持可选的帖子ID过滤
   *
   * @param userIds 用户ID数组
   * @param assetId 资产ID
   * @param postId 可选的帖子ID
   * @returns 点亮记录数组
   */
  private async queryLightRecordsByUsers(
    userIds: number[],
    assetId: number,
    postId?: number,
  ): Promise<DatabaseLightRecord[]> {
    const { query, params } = this.buildLightRecordQuery(
      userIds,
      assetId,
      postId,
    );

    return await this.db.$queryRawUnsafe<DatabaseLightRecord[]>(
      query,
      ...params,
    );
  }

  /**
   * 构建点亮记录查询SQL
   * 将SQL构建逻辑独立出来，提高代码可测试性和可维护性
   *
   * @param userIds 用户ID数组
   * @param assetId 资产ID
   * @param postId 可选的帖子ID
   * @returns 查询语句和参数
   */
  private buildLightRecordQuery(
    userIds: number[],
    assetId: number,
    postId?: number,
  ): { query: string; params: (number | string)[] } {
    let query = `
      SELECT DISTINCT light_user_id as userId, asset_id as assetId, post_id as postId, created_time as createdAt
      FROM square_light
      WHERE light_user_id IN (${userIds.map(() => '?').join(',')})
      AND asset_id = ?
    `;

    const params: (number | string)[] = [...userIds, assetId];

    if (postId !== undefined) {
      query += ' AND post_id = ?';
      params.push(postId);
    }

    return { query, params };
  }

  /**
   * 获取指定数量的首次点亮账户
   * 基于完整分析结果，返回指定数量的首次点亮账户
   *
   * @param params 查询参数
   * @param limit 限制数量，默认使用配置中的默认值
   * @returns 首次点亮账户列表
   */
  async getFirstLightAccountsBatch(
    params: FirstLightQueryParams,
    limit: number = this.queryConfig.defaultLimit,
  ): Promise<UserBean[]> {
    const analysis = await this.analyzeFirstLightAccounts(params);

    return analysis.firstLightUserBeanList.slice(0, limit);
  }

  /**
   * 检查单个账户是否为首次点亮
   * 提供快速的单账户检查功能，适用于实时验证场景
   *
   * @param apiKey API密钥
   * @param assetId 资产ID
   * @param postId 帖子ID（可选）
   * @returns 是否为首次点亮
   */
  async isFirstLight(
    apiKey: string,
    assetId: number,
    postId?: number,
  ): Promise<boolean> {
    try {
      const userId = await this.getUserIdByApiKey(apiKey);

      if (!userId) {
        return true; // 用户不存在，视为首次点亮
      }

      const lightCount = await this.getLightRecordCount(
        userId,
        assetId,
        postId,
      );

      return lightCount === 0; // 没有记录则为首次点亮
    } catch (error) {
      this.logger.error(`检查首次点亮失败 (${apiKey}):`, error);
      return false; // 出错时保守处理，视为非首次点亮
    }
  }

  /**
   * 根据API密钥获取用户ID
   * 封装用户查询逻辑，提高代码复用性
   *
   * @param apiKey API密钥
   * @returns 用户ID，如果不存在则返回null
   */
  private async getUserIdByApiKey(apiKey: string): Promise<number | null> {
    const userResult = await this.db.$queryRawUnsafe<
      Array<{ user_id: number }>
    >(
      'SELECT user_id FROM user_api_key WHERE api_key = ? AND enabled = 1',
      apiKey,
    );

    return userResult.length > 0 ? userResult[0].user_id : null;
  }

  /**
   * 获取用户的点亮记录数量
   * 支持可选的帖子ID过滤，提供灵活的查询能力
   *
   * @param userId 用户ID
   * @param assetId 资产ID
   * @param postId 可选的帖子ID
   * @returns 点亮记录数量
   */
  private async getLightRecordCount(
    userId: number,
    assetId: number,
    postId?: number,
  ): Promise<number> {
    const { query, params } = this.buildLightCountQuery(
      userId,
      assetId,
      postId,
    );

    const result = await this.db.$queryRawUnsafe<Array<{ count: number }>>(
      query,
      ...params,
    );

    return result[0].count;
  }

  /**
   * 构建点亮记录计数查询
   * 将SQL构建逻辑独立出来，提高可维护性
   *
   * @param userId 用户ID
   * @param assetId 资产ID
   * @param postId 可选的帖子ID
   * @returns 查询语句和参数
   */
  private buildLightCountQuery(
    userId: number,
    assetId: number,
    postId?: number,
  ): { query: string; params: number[] } {
    let query = `
      SELECT COUNT(*) as count
      FROM square_light
      WHERE light_user_id = ? AND asset_id = ?
    `;
    const params = [userId, assetId];

    if (postId !== undefined) {
      query += ' AND post_id = ?';
      params.push(postId);
    }

    return { query, params };
  }

  /**
   * 获取账户统计信息
   * 提供账户加载状态和基本统计信息
   *
   * @returns 账户统计信息
   */
  getAccountsStatistics(): {
    totalAccounts: number;
    accountsLoaded: boolean;
    sampleAccount: AccountData | null;
  } {
    const accounts = this.accountDataService.getAccounts();
    return {
      totalAccounts: accounts.length,
      accountsLoaded: accounts.length > 0,
      sampleAccount: accounts[0] || null,
    };
  }
}
