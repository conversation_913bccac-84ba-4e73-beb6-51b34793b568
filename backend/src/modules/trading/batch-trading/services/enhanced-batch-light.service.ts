import { Injectable, Logger } from '@nestjs/common';
import { FirstLightAnalyzerService } from './first-light-analyzer.service';

import { RandomUtils } from '../../../../core/shared/random.utils';
import { EnhancedBatchLightCoreService } from './enhanced-batch-light-core.service';
import { LightQueryOptimizerService } from './light-query-optimizer.service';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { UserBean } from '../../../../core/shared/common.types';
import { BaseBusinessService } from '../../../../core/shared/base-business.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import {
  BatchLightExecutionHistory,
  BatchLightExecutionHistoryDto,
} from '../types/batch.types';
import { v4 as uuidv4 } from 'uuid';
import { FirstLightAnalysisResult } from '../types/batch.types';

/**
 * 随机数量配置接口
 */
export interface RandomAmountConfig {
  enabled: boolean;
  minAmount: number;
  maxAmount: number;
}

/**
 * 批次配置接口
 */
export interface BatchConfig {
  batchSize: number;
  intervalMs: number;
}

/**
 * 增强的批量点亮请求接口
 */
export interface EnhancedBatchLightRequest {
  userBeanList: UserBean[];
  assetId: number;
  amount?: number; // 固定数量模式
  randomAmount?: RandomAmountConfig; // 随机数量模式
  batchConfig?: BatchConfig; // 批次配置
  enableFirstLightAnalysis?: boolean; // 是否启用首次点亮分析
  customLightCount?: number; // 自定义点亮数量
}

/**
 * 批次执行状态
 */
export interface BatchExecutionStatus {
  batchIndex: number;
  batchSize: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  successCount: number;
  failedCount: number;
  results: Array<{
    apiKey: string;
    status: string;
    message: string;
    amount?: number;
    recordId?: number;
  }>;
}

/**
 * 增强的批量点亮结果
 */
export interface EnhancedBatchLightResult {
  batchId: string;
  totalAccounts: number;
  firstLightAccounts?: number;
  alreadyLightedAccounts?: number;
  batches: BatchExecutionStatus[];
  overallStatus: 'pending' | 'processing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  summary: {
    totalProcessed: number;
    totalSuccess: number;
    totalFailed: number;
    averageAmountPerAccount?: number;
    totalAmount?: number;
  };
}

/**
 * 增强的批量点亮服务
 * 集成首次点亮分析、随机数量、批次处理等功能
 */
@Injectable()
export class EnhancedBatchLightService extends BaseBusinessService {
  protected readonly logger = new Logger(EnhancedBatchLightService.name);
  private readonly activeBatches = new Map<string, EnhancedBatchLightResult>();

  // 批量点亮执行历史记录 - 参考随机点亮的优秀实现
  private readonly executionHistory: BatchLightExecutionHistory[] = [];
  private readonly maxHistorySize = 2000; // 比随机点亮多一些，因为批量操作记录更多

  constructor(
    private readonly firstLightAnalyzer: FirstLightAnalyzerService,
    private readonly enhancedBatchLightCoreService: EnhancedBatchLightCoreService,
    private readonly lightQueryOptimizer: LightQueryOptimizerService,
    private readonly accountDataService: AccountDataService,
    businessLogger: BusinessLoggerService,
  ) {
    super('EnhancedBatchLightService', businessLogger);
  }

  /**
   * 执行增强的批量点亮
   */
  async executeEnhancedBatchLight(
    request: EnhancedBatchLightRequest,
  ): Promise<EnhancedBatchLightResult> {
    const batchId = this.generateBatchId();
    const startTime = new Date();

    this.logger.log(
      `开始执行增强批量点亮，batchId: ${batchId}, 账户数: ${request.userBeanList.length}`,
    );

    try {
      // 1. 验证资产ID
      await this.validateAssetId(request.assetId);

      // 2. 首次点亮分析（默认启用）
      let firstLightAnalysis: FirstLightAnalysisResult | undefined;
      let processUserList = request.userBeanList;

      if (request.enableFirstLightAnalysis !== false) {
        // 默认启用
        firstLightAnalysis = await this.performFirstLightAnalysis(
          request.assetId,
          request.userBeanList,
        );
        // 只处理首次点亮的账户
        processUserList = firstLightAnalysis.firstLightUserBeanList;
        this.logger.log(
          `首次点亮分析完成: 总账户${firstLightAnalysis.totalAccounts}, 首次点亮${firstLightAnalysis.firstLightAccounts}`,
        );
      }

      // 3. 自定义点亮数量处理
      if (request.customLightCount && request.customLightCount > 0) {
        const limitedCount = Math.min(
          request.customLightCount,
          processUserList.length,
        );
        processUserList = processUserList.slice(0, limitedCount);
        this.logger.log(
          `应用自定义点亮数量限制: 请求${request.customLightCount}个, 实际处理${limitedCount}个账户`,
        );
      }

      // 4. 生成随机数量（如果启用）
      const userAmountMap = this.generateUserAmounts(
        processUserList,
        request.amount,
        request.randomAmount,
      );

      // 4. 创建批量点亮结果对象
      const result: EnhancedBatchLightResult = {
        batchId,
        totalAccounts: request.userBeanList.length,
        firstLightAccounts: firstLightAnalysis?.firstLightAccounts,
        alreadyLightedAccounts: firstLightAnalysis?.alreadyLightedAccounts,
        batches: [],
        overallStatus: 'pending',
        startTime,
        summary: {
          totalProcessed: 0,
          totalSuccess: 0,
          totalFailed: 0,
        },
      };

      // 5. 存储到活跃批次中
      this.activeBatches.set(batchId, result);

      // 6. 执行批次处理
      await this.executeBatchProcessing(
        batchId,
        processUserList,
        userAmountMap,
        request.assetId,
        request.batchConfig,
      );

      return this.activeBatches.get(batchId)!;
    } catch (error) {
      this.logger.error(
        `增强批量点亮执行失败: ${(error as Error).message}`,
        (error as Error).stack,
      );
      throw error;
    }
  }

  /**
   * 获取批次执行状态
   */
  getBatchStatus(batchId: string): EnhancedBatchLightResult | null {
    return this.activeBatches.get(batchId) || null;
  }

  /**
   * 获取所有活跃批次
   */
  getAllActiveBatches(): EnhancedBatchLightResult[] {
    return Array.from(this.activeBatches.values());
  }

  /**
   * 清理已完成的批次
   */
  cleanupCompletedBatches(): void {
    for (const [batchId, result] of this.activeBatches.entries()) {
      if (
        result.overallStatus === 'completed' ||
        result.overallStatus === 'failed'
      ) {
        // 保留最近24小时的记录
        const hoursSinceCompletion = result.endTime
          ? (Date.now() - result.endTime.getTime()) / (1000 * 60 * 60)
          : 0;

        if (hoursSinceCompletion > 24) {
          this.activeBatches.delete(batchId);
          this.logger.log(`清理已完成批次: ${batchId}`);
        }
      }
    }
  }

  /**
   * 获取批量点亮执行历史
   * 参考随机点亮的实现
   */
  getBatchExecutionHistory(
    limit: number = 100,
  ): BatchLightExecutionHistoryDto[] {
    return this.executionHistory
      .slice(-limit)
      .reverse()
      .map((history) => ({
        id: history.id,
        batchId: history.batchId,
        timestamp: history.timestamp.toISOString(),
        accountApiKey: history.account.apiKey.substring(0, 8) + '...',
        accountEmail: history.account.email,
        assetId: history.asset.assetId,
        assetSymbol: history.asset.symbol,
        assetName: history.asset.tokenName,
        amount: history.amount,
        status: history.status,
        message: history.message,
        duration: history.duration,
        lightNumBefore: history.asset.lightNumBefore,
        lightNumAfter: history.asset.lightNumAfter,
        batchIndex: history.batchIndex,
      }));
  }

  /**
   * 获取指定批次的执行历史
   */
  getBatchExecutionHistoryByBatchId(
    batchId: string,
  ): BatchLightExecutionHistoryDto[] {
    return this.executionHistory
      .filter((history) => history.batchId === batchId)
      .map((history) => ({
        id: history.id,
        batchId: history.batchId,
        timestamp: history.timestamp.toISOString(),
        accountApiKey: history.account.apiKey.substring(0, 8) + '...',
        accountEmail: history.account.email,
        assetId: history.asset.assetId,
        assetSymbol: history.asset.symbol,
        assetName: history.asset.tokenName,
        amount: history.amount,
        status: history.status,
        message: history.message,
        duration: history.duration,
        lightNumBefore: history.asset.lightNumBefore,
        lightNumAfter: history.asset.lightNumAfter,
        batchIndex: history.batchIndex,
      }));
  }

  /**
   * 生成批次ID
   */
  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 验证资产ID
   */
  private async validateAssetId(assetId: number): Promise<void> {
    const validation =
      await this.lightQueryOptimizer.validateLightability(assetId);
    if (!validation || !validation.canLight) {
      throw new Error(`资产ID ${assetId} 不可点亮或不存在`);
    }
  }

  /**
   * 执行首次点亮分析
   */
  private async performFirstLightAnalysis(
    assetId: number,
    userBeanList: UserBean[],
  ) {
    return await this.firstLightAnalyzer.analyzeFirstLightAccounts({
      assetId,
      batchSize: userBeanList.length,
    });
  }

  /**
   * 生成用户数量映射
   */
  private generateUserAmounts(
    userList: UserBean[],
    fixedAmount?: number,
    randomConfig?: RandomAmountConfig,
  ): Map<string, number> {
    const userAmountMap = new Map<string, number>();

    for (const user of userList) {
      let amount: number;

      if (randomConfig?.enabled) {
        // 随机数量模式
        amount = this.generateRandomAmount(
          randomConfig.minAmount,
          randomConfig.maxAmount,
        );
      } else {
        // 固定数量模式
        amount = fixedAmount || 1;
      }

      userAmountMap.set(user.apiKey, amount);
    }

    return userAmountMap;
  }

  /**
   * 生成随机数量（整数）
   */
  private generateRandomAmount(min: number, max: number): number {
    return RandomUtils.randomInt(Math.floor(min), Math.floor(max));
  }

  /**
   * 执行批次处理
   */
  private async executeBatchProcessing(
    batchId: string,
    userList: UserBean[],
    userAmountMap: Map<string, number>,
    assetId: number,
    batchConfig?: BatchConfig,
  ): Promise<void> {
    const result = this.activeBatches.get(batchId)!;
    result.overallStatus = 'processing';

    const defaultBatchSize = 50;
    const defaultIntervalMs = 1000;
    const batchSize = batchConfig?.batchSize || defaultBatchSize;
    const intervalMs = batchConfig?.intervalMs || defaultIntervalMs;

    // 分割用户列表为批次
    const batches = this.splitIntoBatches(userList, batchSize);

    this.logger.log(
      `开始批次处理: ${batches.length}个批次, 每批${batchSize}个账户, 间隔${intervalMs}ms`,
    );

    try {
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const batchStatus: BatchExecutionStatus = {
          batchIndex: i + 1,
          batchSize: batch.length,
          status: 'pending',
          successCount: 0,
          failedCount: 0,
          results: [],
        };

        result.batches.push(batchStatus);

        // 执行单个批次
        await this.executeSingleBatch(
          batchId,
          i,
          batch,
          userAmountMap,
          assetId,
        );

        // 批次间隔
        if (i < batches.length - 1 && intervalMs > 0) {
          this.logger.log(
            `批次${i + 1}完成，等待${intervalMs}ms后开始下一批次`,
          );
          await this.sleep(intervalMs);
        }
      }

      // 更新整体状态
      result.overallStatus = 'completed';
      result.endTime = new Date();
      this.updateSummary(batchId);

      this.logger.log(`批量点亮完成: ${batchId}`);
    } catch (error) {
      result.overallStatus = 'failed';
      result.endTime = new Date();
      this.logger.error(`批量点亮失败: ${batchId}`, error);
      throw error;
    }
  }

  /**
   * 执行单个批次
   */
  private async executeSingleBatch(
    batchId: string,
    batchIndex: number,
    batch: UserBean[],
    userAmountMap: Map<string, number>,
    assetId: number,
  ): Promise<void> {
    const result = this.activeBatches.get(batchId)!;
    const batchStatus = result.batches[batchIndex];

    batchStatus.status = 'processing';
    batchStatus.startTime = new Date();

    this.logger.log(`开始执行批次${batchIndex + 1}: ${batch.length}个账户`);

    try {
      // 获取资产信息用于历史记录
      const assetInfo =
        await this.lightQueryOptimizer.validateLightability(assetId);

      // 调用增强的批量点亮核心服务
      const batchResult =
        await this.enhancedBatchLightCoreService.executeEnhancedBatchLight(
          batch,
          assetId,
          userAmountMap,
        );

      // 处理结果并记录执行历史
      for (let i = 0; i < batchResult.data.results.length; i++) {
        const tradeResult = batchResult.data.results[i];
        const userBean = batch[i]; // 对应的用户信息
        const executionStartTime = Date.now() - 1000; // 估算开始时间

        batchStatus.results.push({
          apiKey: tradeResult.apiKey,
          status: tradeResult.status,
          message: tradeResult.message,
          amount: tradeResult.amount,
          recordId: tradeResult.recordId,
        });

        if (tradeResult.success) {
          batchStatus.successCount++;
        } else {
          batchStatus.failedCount++;
        }

        // 记录详细的执行历史 - 参考随机点亮的实现
        if (assetInfo && userBean) {
          // 异步获取用户信息（包括邮箱）
          this.recordBatchExecutionAsync(
            batchId,
            userBean.apiKey,
            {
              assetId: assetId,
              symbol: assetInfo.symbol || '',
              tokenName: assetInfo.tokenName || '',
              lightNum: assetInfo.lightNum || 0,
              postId: assetInfo.postId || 0,
            },
            {
              success: tradeResult.success || false,
              status: tradeResult.status,
              message: tradeResult.message,
              amount: tradeResult.amount || 0,
              recordId: tradeResult.recordId,
            },
            batchIndex,
            executionStartTime,
          );
        }
      }

      batchStatus.status = 'completed';
      batchStatus.endTime = new Date();

      this.logger.log(
        `批次${batchIndex + 1}完成: 成功${batchStatus.successCount}, 失败${batchStatus.failedCount}`,
      );
    } catch (error) {
      batchStatus.status = 'failed';
      batchStatus.endTime = new Date();
      this.logger.error(`批次${batchIndex + 1}执行失败:`, error);
      throw error;
    }
  }

  /**
   * 分割用户列表为批次
   */
  private splitIntoBatches(
    userList: UserBean[],
    batchSize: number,
  ): UserBean[][] {
    const batches: UserBean[][] = [];
    for (let i = 0; i < userList.length; i += batchSize) {
      batches.push(userList.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 更新汇总信息
   */
  private updateSummary(batchId: string): void {
    const result = this.activeBatches.get(batchId)!;

    let totalProcessed = 0;
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalAmount = 0;

    for (const batch of result.batches) {
      totalProcessed += batch.results.length;
      totalSuccess += batch.successCount;
      totalFailed += batch.failedCount;

      for (const batchResult of batch.results) {
        if (batchResult.amount) {
          totalAmount += batchResult.amount;
        }
      }
    }

    result.summary = {
      totalProcessed,
      totalSuccess,
      totalFailed,
      totalAmount,
      averageAmountPerAccount:
        totalProcessed > 0 ? totalAmount / totalProcessed : 0,
    };
  }

  /**
   * 根据apiKey获取邮箱
   * 使用 AccountDataService 统一获取账户信息
   */
  private getEmailByApiKey(apiKey: string): string {
    try {
      const account = this.accountDataService.findAccountByApiKey(apiKey);
      return account?.email || '<EMAIL>';
    } catch (error) {
      this.logger.error(`获取账户邮箱失败: ${apiKey}`, error);
      return '<EMAIL>';
    }
  }

  /**
   * 记录批量点亮执行历史
   * 使用 AccountDataService 获取邮箱信息
   */
  private recordBatchExecutionAsync(
    batchId: string,
    apiKey: string,
    asset: {
      assetId: number;
      symbol: string;
      tokenName: string;
      lightNum: number;
      postId: number;
    },
    result: {
      success: boolean;
      status: string;
      message: string;
      amount: number;
      recordId?: number;
    },
    batchIndex: number,
    startTime: number,
  ): void {
    const account = {
      apiKey,
      email: this.getEmailByApiKey(apiKey),
      userId: 0, // 暂时设为0，因为我们主要关注邮箱信息
    };

    this.recordBatchExecution(
      batchId,
      account,
      asset,
      result,
      batchIndex,
      startTime,
    );
  }

  /**
   * 记录批量点亮执行历史
   * 参考随机点亮的优秀实现
   */
  private recordBatchExecution(
    batchId: string,
    account: { apiKey: string; email: string; userId: number },
    asset: {
      assetId: number;
      symbol: string;
      tokenName: string;
      lightNum: number;
      postId: number;
    },
    result: {
      success: boolean;
      status: string;
      message: string;
      amount: number;
      recordId?: number;
    },
    batchIndex: number,
    startTime: number,
  ): void {
    const execution: BatchLightExecutionHistory = {
      id: uuidv4(),
      batchId,
      timestamp: new Date(),
      account: {
        apiKey: account.apiKey,
        email: account.email,
        userId: account.userId,
      },
      asset: {
        assetId: asset.assetId,
        symbol: asset.symbol,
        tokenName: asset.tokenName,
        lightNumBefore: asset.lightNum,
        lightNumAfter: result.success ? asset.lightNum + 1 : undefined,
        postId: asset.postId,
      },
      amount: result.amount,
      status: result.success ? 'success' : 'failed',
      message: result.message,
      duration: Date.now() - startTime,
      batchIndex,
    };

    // 添加到历史记录
    this.executionHistory.push(execution);

    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory.shift();
    }

    this.logger.debug(
      `记录批量点亮执行历史: ${execution.id}, 状态: ${execution.status}, 耗时: ${execution.duration}ms`,
    );
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
