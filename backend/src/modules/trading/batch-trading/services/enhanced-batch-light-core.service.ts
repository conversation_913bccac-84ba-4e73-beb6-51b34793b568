import { Injectable } from '@nestjs/common';
import { UserBean } from '../../../../core/shared/common.types';
import { LightQueryOptimizerService } from './light-query-optimizer.service';
import {
  BaseExternalApiService,
  ExternalApiConfig,
} from '../../../../core/shared/base-external-api.service';

/**
 * 增强的点亮结果接口
 */
export interface EnhancedLightResult {
  apiKey: string;
  status: string;
  message: string;
  amount?: number;
  recordId?: number;
  success: boolean;
}

/**
 * 增强的批量点亮响应接口
 */
export interface EnhancedBatchLightResponse {
  code: number;
  msg: string;
  data: {
    total: number;
    success: number;
    failed: number;
    results: EnhancedLightResult[];
  };
}

/**
 * 真实点亮API请求接口（匹配Nine API格式）
 */
interface LightApiRequest {
  userBeanList: Array<{
    apiKey: string;
    secret: string;
    amount: number;
  }>;
  postId: number;
  assetId: number;
}

/**
 * 真实点亮API响应接口（Nine API格式）
 */
interface LightApiResponse {
  code: number;
  msg: string | null;
  data?: {
    recordId?: number;
    transactionId?: string;
    timestamp?: string;
  };
  // 添加便利属性用于兼容
  success?: boolean;
  message?: string;
}

/**
 * 增强的批量点亮核心服务
 * 支持个性化数量的批量点亮处理，直接调用Nine Light API
 * 继承BaseExternalApiService以获得统一的HTTP请求处理能力
 */
@Injectable()
export class EnhancedBatchLightCoreService extends BaseExternalApiService {
  constructor(
    private readonly lightQueryOptimizer: LightQueryOptimizerService,
  ) {
    super();
    this.logger.log('初始化增强批量点亮核心服务，使用统一的外部API基类');
    this.logger.log(`API基础URL: ${this.getApiBaseUrl()}`);
  }

  /**
   * 获取API基础URL
   */
  protected getApiBaseUrl(): string {
    return process.env.NINE_API_BASE_URL || 'https://openapi.binineex.com';
  }

  /**
   * 获取默认配置
   */
  protected getDefaultConfig(): ExternalApiConfig {
    return {
      timeout: 30000,
      retries: 2, // 点亮操作允许重试一次
      enableDetailedLogging: true,
      retryDelay: 1000,
    };
  }

  /**
   * 执行增强的批量点亮
   * 支持为每个用户指定不同的点亮数量，使用并行处理提高性能
   */
  async executeEnhancedBatchLight(
    userList: UserBean[],
    assetId: number,
    userAmountMap: Map<string, number>,
  ): Promise<EnhancedBatchLightResponse> {
    try {
      this.logger.log(
        `开始执行增强批量点亮: ${userList.length}个用户, 资产ID: ${assetId}`,
      );

      // 并行处理所有用户的点亮，提高性能
      const lightPromises = userList.map(async (user) => {
        const amount = userAmountMap.get(user.apiKey) || 1;
        return this.processSingleEnhancedLight(user, assetId, amount);
      });

      // 等待所有点亮完成
      const results = await Promise.all(lightPromises);

      // 统计结果
      const successCount = results.filter((r) => r.success).length;
      const failedCount = results.length - successCount;

      this.logger.log(
        `增强批量点亮完成: 总计${results.length}, 成功${successCount}, 失败${failedCount}`,
      );

      return {
        code: 200,
        msg: '增强批量点亮完成',
        data: {
          total: results.length,
          success: successCount,
          failed: failedCount,
          results,
        },
      };
    } catch (error) {
      this.logger.error('增强批量点亮执行失败:', error);
      return {
        code: 500,
        msg: '增强批量点亮执行失败',
        data: {
          total: 0,
          success: 0,
          failed: 0,
          results: [],
        },
      };
    }
  }

  /**
   * 处理单个用户的增强点亮
   */
  private async processSingleEnhancedLight(
    user: UserBean,
    assetId: number,
    amount: number,
  ): Promise<EnhancedLightResult> {
    try {
      this.logger.debug(
        `处理用户点亮: ${user.apiKey.substring(0, 8)}..., 资产ID: ${assetId}, 数量: ${amount}`,
      );

      // 执行真实的Nine Light API调用
      const apiResult = await this.executeLightApi(user, assetId, amount);

      if (apiResult.success) {
        return {
          apiKey: user.apiKey,
          status: 'success',
          message: apiResult.message || '点亮成功',
          amount,
          recordId: this.generateRecordId(),
          success: true,
        };
      } else {
        return {
          apiKey: user.apiKey,
          status: 'failed',
          message: apiResult.message || '点亮失败',
          amount,
          success: false,
        };
      }
    } catch (error) {
      this.logger.error(
        `用户点亮处理失败 (${user.apiKey.substring(0, 8)}...):`,
        error,
      );

      return {
        apiKey: user.apiKey,
        status: 'error',
        message: `点亮处理异常: ${(error as Error).message}`,
        amount,
        success: false,
      };
    }
  }

  /**
   * 执行真实的Nine Light API调用
   */
  private async executeLightApi(
    user: UserBean,
    assetId: number,
    amount: number,
  ): Promise<LightApiResponse> {
    try {
      // 首先查询获取postId
      const validation =
        await this.lightQueryOptimizer.validateLightability(assetId);
      if (!validation || !validation.canLight) {
        return {
          code: 400,
          msg: `资产ID ${assetId} 不可点亮或不存在`,
          success: false,
          message: `资产ID ${assetId} 不可点亮或不存在`,
        };
      }

      // 构建请求数据，匹配Nine API格式
      const requestData: LightApiRequest = {
        userBeanList: [
          {
            apiKey: user.apiKey,
            secret: user.secret,
            amount: amount,
          },
        ],
        postId: validation.postId,
        assetId: assetId,
      };

      const result = await this.callNineLightApi(requestData);

      if (!result.success) {
        this.logger.error(
          `API调用失败: ${user.apiKey.substring(0, 8)}..., 代码: ${result.code}, 消息: ${result.message}`,
        );
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.logger.error(
        `API调用异常: ${user.apiKey.substring(0, 8)}..., 错误: ${errorMessage}`,
      );

      return {
        code: 500,
        msg: `API调用失败: ${errorMessage}`,
        success: false,
        message: `API调用失败: ${errorMessage}`,
      };
    }
  }

  /**
   * 调用Nine Light API的核心方法
   * 使用基类的统一API调用方法
   */
  private async callNineLightApi(
    requestData: LightApiRequest,
  ): Promise<LightApiResponse> {
    const endpoint = '/batch/light';
    const url = `${this.getApiBaseUrl()}${endpoint}`;

    try {
      const response = await this.callExternalApi<
        LightApiRequest,
        LightApiResponse['data']
      >(url, requestData, this.getDefaultConfig());

      // 转换为LightApiResponse格式以保持兼容性
      const result: LightApiResponse = {
        code: response.code,
        msg: response.msg,
        data: response.data,
        success: response.success,
        message: response.message || this.getErrorMessage(response.code),
      };

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Nine Light API调用失败: ${errorMessage}`);

      // 返回失败响应而不是抛出异常，保持原有的错误处理逻辑
      return {
        code: 500,
        msg: `API调用失败: ${errorMessage}`,
        success: false,
        message: `API调用失败: ${errorMessage}`,
      };
    }
  }

  /**
   * 生成记录ID
   */
  private generateRecordId(): number {
    return Math.floor(Math.random() * 1000000) + 100000;
  }

  /**
   * 根据错误代码获取详细的错误消息
   */
  private getErrorMessage(code: number): string {
    const errorMessages: Record<number, string> = {
      // 客户端错误
      400: '请求参数错误，请检查资产ID和数量是否正确',
      401: '认证失败，API密钥无效或已过期',
      403: '权限不足，账户无法执行点亮操作',
      404: '资产不存在或不可点亮，请检查资产ID',
      409: '操作冲突，可能存在重复请求',
      422: '请求数据格式错误或验证失败',
      429: '请求过于频繁，已触发限流保护',

      // 服务器错误
      500: '服务器内部错误，请稍后重试',
      502: '网关错误，上游服务暂时不可用',
      503: '服务暂时不可用，系统维护中',
      504: '请求超时，网络连接异常',

      // 业务错误
      1001: '余额不足，无法完成点亮操作',
      1002: '账户被暂时锁定，请联系客服',
      1003: '点亮数量超出单次限制',
      1004: '账户权限不足，无法进行此操作',
      1005: '资产已下架或暂停交易',
    };

    return errorMessages[code] || `未知错误 (代码: ${code})，请联系技术支持`;
  }

  /**
   * 验证用户账户列表
   * 检查账户的有效性，包括API密钥和密钥格式
   */
  validateUserAccounts(userList: UserBean[]): {
    validUsers: UserBean[];
    invalidUsers: Array<{ user: UserBean; reason: string }>;
  } {
    const validUsers: UserBean[] = [];
    const invalidUsers: Array<{ user: UserBean; reason: string }> = [];

    for (const user of userList) {
      // 检查API密钥是否存在
      if (!user.apiKey || user.apiKey.trim() === '') {
        invalidUsers.push({
          user,
          reason: 'API密钥为空',
        });
        continue;
      }

      // 检查密钥是否存在
      if (!user.secret || user.secret.trim() === '') {
        invalidUsers.push({
          user,
          reason: '密钥为空',
        });
        continue;
      }

      // 检查API密钥格式（通常是32位十六进制字符串）
      if (!/^[a-fA-F0-9]{32}$/.test(user.apiKey)) {
        invalidUsers.push({
          user,
          reason: 'API密钥格式无效',
        });
        continue;
      }

      // 检查密钥格式（通常是32位十六进制字符串）
      if (!/^[a-fA-F0-9]{32}$/.test(user.secret)) {
        invalidUsers.push({
          user,
          reason: '密钥格式无效',
        });
        continue;
      }

      // 通过所有验证
      validUsers.push(user);
    }

    this.logger.log(
      `账户验证完成: 总计${userList.length}, 有效${validUsers.length}, 无效${invalidUsers.length}`,
    );

    return {
      validUsers,
      invalidUsers,
    };
  }
}
