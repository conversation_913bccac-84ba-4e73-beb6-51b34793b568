import { Controller, Post, Get, Body, UseGuards, Param } from '@nestjs/common';
import { BatchService } from './batch.service';
import { BatchLoginDto } from './dto/batch-login.dto';
import { BatchLightDto } from './dto/batch-light.dto';
import { GetAllPostDto } from './dto/get-all-post.dto';

import {
  FirstLightQueryDto,
  SingleAccountFirstLightDto,
} from './dto/first-light-query.dto';
import {
  BatchLoginResponse,
  BatchTradeResponse,
  GetAllPostResponse,
  FirstLightAnalysisResponse,
  FirstLightBatchResponse,
  SingleAccountFirstLightResponse,
  AccountsStatisticsResponse,
} from './types/batch.types';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User } from '../../auth/strategies/jwt.strategy';
import { FirstLightAnalyzerService } from './services/first-light-analyzer.service';

@Controller('batch')
@UseGuards(JwtAuthGuard)
export class BatchController {
  constructor(
    private readonly batchService: BatchService,
    private readonly firstLightAnalyzer: FirstLightAnalyzerService,
  ) {}

  @Post('login')
  batchLogin(
    @Body() batchLoginDto: BatchLoginDto,
    @CurrentUser() _user?: User,
  ): BatchLoginResponse {
    return this.batchService.batchLogin(batchLoginDto);
  }

  /**
   * 批量点亮接口
   * 移除了postId依赖，直接使用assetId、amount、userBeanList参数
   */
  @Post('light')
  async batchLight(
    @Body() batchLightDto: BatchLightDto,
    @CurrentUser() _user?: User,
  ): Promise<BatchTradeResponse> {
    return await this.batchService.batchLight(batchLightDto);
  }

  @Get('records')
  async getLightRecords(@CurrentUser() _user?: User): Promise<any[]> {
    return await this.batchService.getLightRecords();
  }

  @Post('getAllPost')
  async getAllPost(
    @Body() getAllPostDto: GetAllPostDto,
    @CurrentUser() _user?: User,
  ): Promise<GetAllPostResponse> {
    return await this.batchService.getAllPost(getAllPostDto);
  }

  /**
   * 分析首次点亮账户
   */
  @Post('first-light/analyze')
  async analyzeFirstLight(
    @Body() queryDto: FirstLightQueryDto,
    @CurrentUser() _user?: User,
  ): Promise<FirstLightAnalysisResponse> {
    const result = await this.firstLightAnalyzer.analyzeFirstLightAccounts({
      assetId: queryDto.assetId,
      postId: queryDto.postId,
      batchSize: queryDto.batchSize,
    });

    return {
      code: 200,
      msg: '首次点亮分析完成',
      data: result,
    };
  }

  /**
   * 获取首次点亮账户批次
   */
  @Post('first-light/batch')
  async getFirstLightBatch(
    @Body() queryDto: FirstLightQueryDto,
    @CurrentUser() _user?: User,
  ): Promise<FirstLightBatchResponse> {
    const userBeanList =
      await this.firstLightAnalyzer.getFirstLightAccountsBatch(
        {
          assetId: queryDto.assetId,
          postId: queryDto.postId,
          batchSize: queryDto.batchSize,
        },
        queryDto.batchSize || 100,
      );

    return {
      code: 200,
      msg: '首次点亮账户批次获取成功',
      data: {
        assetId: queryDto.assetId,
        postId: queryDto.postId,
        batchSize: queryDto.batchSize || 100,
        userBeanList,
      },
    };
  }

  /**
   * 检查单个账户是否为首次点亮
   */
  @Post('first-light/check')
  async checkSingleAccountFirstLight(
    @Body() checkDto: SingleAccountFirstLightDto,
    @CurrentUser() _user?: User,
  ): Promise<SingleAccountFirstLightResponse> {
    const isFirstLight = await this.firstLightAnalyzer.isFirstLight(
      checkDto.apiKey,
      checkDto.assetId,
      checkDto.postId,
    );

    return {
      code: 200,
      msg: '首次点亮检查完成',
      data: {
        apiKey: checkDto.apiKey,
        assetId: checkDto.assetId,
        postId: checkDto.postId,
        isFirstLight,
      },
    };
  }

  /**
   * 获取账户统计信息
   */
  @Get('accounts/statistics')
  getAccountsStatistics(
    @CurrentUser() _user?: User,
  ): AccountsStatisticsResponse {
    const statistics = this.firstLightAnalyzer.getAccountsStatistics();

    return {
      code: 200,
      msg: '账户统计信息获取成功',
      data: statistics,
    };
  }

  /**
   * 查询单个账户的 NINE 余额（快速查询）
   */
  @Get('balance/:apiKey')
  async getSingleAccountBalance(
    @Param('apiKey') apiKey: string,
    @CurrentUser() _user?: User,
  ) {
    return this.batchService.getNineBalanceByApiKey(apiKey);
  }
}
