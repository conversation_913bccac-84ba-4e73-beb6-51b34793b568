import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../../core/database/database.service';

import { BatchLoginDto } from './dto/batch-login.dto';

import { BatchLightDto } from './dto/batch-light.dto';
import { GetAllPostDto } from './dto/get-all-post.dto';

import {
  BatchLoginResponse,
  BatchTradeResponse,
  GetAllPostResponse,
} from './types/batch.types';
import { LightQueryOptimizerService } from './services/light-query-optimizer.service';
import { BatchLoginService } from './services/batch-login.service';
import { EnhancedBatchLightCoreService } from './services/enhanced-batch-light-core.service';
import { PostQueryService } from './services/post-query.service';
import {
  NineBalanceService,
  type NineBalanceQueryResult,
} from '../../balance/services/nine-balance.service';

/**
 * 批处理服务协调器
 * 作为各个专门服务的协调器，符合单一职责原则
 * 不再直接处理业务逻辑，而是委托给专门的服务
 */
@Injectable()
export class BatchService {
  constructor(
    private readonly db: DatabaseService,
    private readonly lightQueryOptimizer: LightQueryOptimizerService,
    private readonly loginService: BatchLoginService,
    private readonly enhancedBatchLightCoreService: EnhancedBatchLightCoreService,
    private readonly postService: PostQueryService,
    private readonly nineBalanceService: NineBalanceService,
  ) {}

  /**
   * 批量登录
   * 委托给专门的登录服务处理
   * @param batchLoginDto 批量登录DTO
   * @returns 批量登录结果
   */
  batchLogin(batchLoginDto: BatchLoginDto): BatchLoginResponse {
    return this.loginService.batchLogin(batchLoginDto);
  }

  /**
   * 新的批量点亮功能
   * 移除了postId依赖，直接使用assetId、amount、userBeanList参数
   * @param batchLightDto 批量点亮DTO
   * @returns 批量点亮结果
   */
  async batchLight(batchLightDto: BatchLightDto): Promise<BatchTradeResponse> {
    // 首先验证assetId是否可点亮
    const validation = await this.lightQueryOptimizer.validateLightability(
      batchLightDto.assetId,
    );
    if (!validation || !validation.canLight) {
      throw new Error(`资产ID ${batchLightDto.assetId} 不可点亮或不存在`);
    }

    // 转换用户数据格式
    const userList = batchLightDto.userBeanList.map((user) => ({
      apiKey: user.apiKey,
      secret: user.secret,
    }));

    // 创建用户数量映射（所有用户使用相同数量）
    const userAmountMap = new Map<string, number>();
    userList.forEach((user) => {
      userAmountMap.set(user.apiKey, batchLightDto.amount);
    });

    // 调用增强的批量点亮核心服务
    const result =
      await this.enhancedBatchLightCoreService.executeEnhancedBatchLight(
        userList,
        batchLightDto.assetId,
        userAmountMap,
      );

    // 转换响应格式以保持兼容性
    return {
      code: result.code,
      msg: result.msg,
      data: {
        total: result.data.total,
        success: result.data.success,
        failure: result.data.failed,
        results: result.data.results.map((r) => ({
          apiKey: r.apiKey,
          status: r.status,
          message: r.message,
          success: r.success,
          recordId: r.recordId,
        })),
      },
    };
  }

  /**
   * 获取所有帖子数据
   * 委托给专门的帖子查询服务处理
   * @param getAllPostDto 获取帖子数据DTO
   * @returns 帖子数据响应
   */
  async getAllPost(getAllPostDto: GetAllPostDto): Promise<GetAllPostResponse> {
    return await this.postService.getAllPost(getAllPostDto);
  }

  async getLightRecords(limit = 100): Promise<any[]> {
    return await this.db.lightRecord.findMany({
      include: {
        user: {
          select: {
            email: true,
            apiKey: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  /**
   * 查询所有账户的 NINE 代币余额
   * 高性能批量查询，支持 1100+ 账户记录
   * @returns NINE代币余额查询结果
   */
  async queryAllNineBalances() {
    try {
      const result: NineBalanceQueryResult =
        await this.nineBalanceService.queryAllNineBalances();

      return {
        code: 200,
        msg: '成功',
        data: {
          summary: {
            totalAccounts: result.totalAccounts,
            accountsWithBalance: result.accountsWithBalance,
            accountsWithoutBalance: result.accountsWithoutBalance,
            totalNineBalance: result.totalNineBalance,
            averageBalance: result.averageBalance,
            executionTimeMs: result.executionTimeMs,
          },
          balanceDetails: result.balanceDetails,
        },
      };
    } catch (error: unknown) {
      console.error('查询NINE余额失败:', error);
      return {
        code: 500,
        msg: '服务器内部错误',
        data: {
          summary: {
            totalAccounts: 0,
            accountsWithBalance: 0,
            accountsWithoutBalance: 0,
            totalNineBalance: 0,
            averageBalance: 0,
            executionTimeMs: 0,
          },
          balanceDetails: [],
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * 获取有 NINE 余额的账户列表
   * 筛选出有余额的账户，用于后续批量操作
   * @param minBalance 最小余额阈值，默认为0
   * @returns 有余额的账户信息
   */
  async getAccountsWithNineBalance(minBalance: number = 0) {
    try {
      const accounts =
        await this.nineBalanceService.getAccountsWithNineBalance(minBalance);

      return {
        code: 200,
        msg: '成功',
        data: {
          totalCount: accounts.length,
          minBalance,
          accounts: accounts.map((account) => ({
            userId: account.userId,
            apiKey: account.apiKey,
            email: account.email,
            totalBalance: account.totalBalance,
            hasBalance: account.hasBalance,
          })),
        },
      };
    } catch (error: unknown) {
      console.error('获取有余额账户失败:', error);
      return {
        code: 500,
        msg: '服务器内部错误',
        data: {
          totalCount: 0,
          minBalance,
          accounts: [],
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * 查询单个账户的 NINE 余额
   * @param apiKey API密钥
   * @returns 单个账户的NINE余额信息
   */
  async getNineBalanceByApiKey(apiKey: string) {
    try {
      const balance =
        await this.nineBalanceService.getNineBalanceByApiKey(apiKey);

      if (!balance) {
        return {
          code: 404,
          msg: '账户不存在',
          data: null,
        };
      }

      return {
        code: 200,
        msg: '成功',
        data: {
          userId: balance.userId,
          apiKey: balance.apiKey,
          email: balance.email,
          totalBalance: balance.totalBalance,
          hasBalance: balance.hasBalance,
        },
      };
    } catch (error: unknown) {
      console.error(`查询账户${apiKey}的NINE余额失败:`, error);
      return {
        code: 500,
        msg: '服务器内部错误',
        data: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }
}
