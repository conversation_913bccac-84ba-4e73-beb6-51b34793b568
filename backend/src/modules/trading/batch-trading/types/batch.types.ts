export interface LoginResult {
  email: string;
  status: string;
  message: string;
  success?: boolean;
}

export interface TradeResult {
  apiKey: string;
  status: string;
  message: string;
  recordId?: number;
  success?: boolean;
}

export interface BatchLoginResponse {
  code: number;
  msg: string;
  data: {
    total: number;
    success: number;
    failure: number;
    results: LoginResult[];
  };
}

export interface BatchTradeResponse {
  code: number;
  msg: string;
  data: {
    total: number;
    success: number;
    failure: number;
    results: TradeResult[];
  };
}

// 帖子相关类型定义
export interface PostImage {
  postCoverUrl: string;
}

export interface PostRecord {
  postId: number;
  assetId: number;
  userId: number;
  poolId: number | null;
  tokenId: number;
  tradePairId: number | null;
  content: string;
  source: number;
  createdTime: string;
  tweetUserId: number | null;
  tweetId: string | null;
  tweetUserName: string | null;
  tweetNickName: string | null;
  tweetUrl: string | null;
  tweetLikeCount: number;
  tweetUserPhotoUrl: string | null;
  tweetRetweetCount: number;
  tweetReplyCount: number;
  tweetQuoteCount: number;
  tweetBookmarkCount: number;
  tweetImpressionCount: number;
  logo: string;
  tokenSymbol: string;
  tokenName: string;
  lightNum: number;
  status: number;
  stake: number;
  change24h: number;
  innerPrice: number;
  outerPrice: number;
  marketCap: number | null;
  process: number;
  assetProperty: number;
  userAvatar: string;
  nickName: string;
  lighted: boolean;
  isLiked: boolean;
  images: PostImage[];
  stableCoinSymbol: string | null;
  isBlueV: number;
  postLikeCount: number;
  postCommentCount: number;
  postShareCount: number;
  postViewCount: number;
  postHotScore: number;
}

export interface PostListData {
  records: PostRecord[];
  total?: number;
  size?: number;
  current?: number;
  pages?: number;
}

export interface PostApiResponse {
  message: string | null;
  code: number;
  timezone: string | null;
  data: PostListData;
}

export interface GetAllPostResponse {
  code: number;
  msg: string;
  data: {
    email: string | null;
    phone: string | null;
    password: string | null;
    apiKey: string;
    secret: string;
    data: PostApiResponse;
  };
}

// Symbol相关类型定义
export interface SymbolAssetOption {
  assetId: number;
  tokenName: string;
  description?: string;
}

export interface SymbolResolveResponse {
  code: number;
  msg: string;
  data: {
    symbol: string;
    matches: SymbolAssetOption[];
    isUnique: boolean;
    selectedAssetId?: number;
  };
}

export interface SymbolSuggestionResponse {
  code: number;
  msg: string;
  data: {
    query: string;
    suggestions: string[];
  };
}

// 首次点亮相关类型定义
import { UserBean } from '../../../../core/shared/common.types';

export interface FirstLightAnalysisResult {
  totalAccounts: number;
  firstLightAccounts: number;
  alreadyLightedAccounts: number;
  firstLightUserBeanList: UserBean[];
  alreadyLightedUserBeanList: UserBean[];
  executionTimeMs: number;
}

export interface FirstLightAnalysisResponse {
  code: number;
  msg: string;
  data: FirstLightAnalysisResult;
}

export interface FirstLightBatchResponse {
  code: number;
  msg: string;
  data: {
    assetId: number;
    postId?: number;
    batchSize: number;
    userBeanList: UserBean[];
  };
}

export interface SingleAccountFirstLightResponse {
  code: number;
  msg: string;
  data: {
    apiKey: string;
    assetId: number;
    postId?: number;
    isFirstLight: boolean;
  };
}

export interface AccountsStatisticsResponse {
  code: number;
  msg: string;
  data: {
    totalAccounts: number;
    accountsLoaded: boolean;
    sampleAccount: {
      apiKey: string;
      secret: string;
      email: string;
      password: string;
    } | null;
  };
}

// NINE代币余额相关类型定义
export interface NineBalanceInfo {
  userId: number;
  apiKey: string;
  email: string;
  totalBalance: number;
  hasBalance: boolean;
  accountActive: boolean;
}

export interface NineBalanceQueryResult {
  totalAccounts: number;
  accountsWithBalance: number;
  accountsWithoutBalance: number;
  totalNineBalance: number;
  averageBalance: number;
  balanceDetails: NineBalanceInfo[];
  executionTimeMs: number;
}

export interface NineBalanceResponse {
  code: number;
  msg: string;
  data: {
    summary: {
      totalAccounts: number;
      accountsWithBalance: number;
      accountsWithoutBalance: number;
      totalNineBalance: number;
      averageBalance: number;
      executionTimeMs: number;
    };
    balanceDetails: NineBalanceInfo[];
  };
}

export interface AccountsWithBalanceResponse {
  code: number;
  msg: string;
  data: {
    totalCount: number;
    minBalance: number;
    accounts: Array<{
      userId: number;
      apiKey: string;
      email: string;
      totalBalance: number;
      accountActive: boolean;
    }>;
  };
}

export interface SingleNineBalanceResponse {
  code: number;
  msg: string;
  data: {
    userId: number;
    apiKey: string;
    email: string;
    totalBalance: number;
    hasBalance: boolean;
    accountActive: boolean;
  } | null;
}

// 增强批量点亮相关类型定义
/**
 * 批量点亮执行历史记录
 * 参考随机点亮的优秀实现，记录每次点亮的详细信息
 */
export interface BatchLightExecutionHistory {
  id: string;
  batchId: string;
  timestamp: Date;
  account: {
    apiKey: string;
    email: string;
    userId: number;
  };
  asset: {
    assetId: number;
    symbol: string;
    tokenName: string;
    lightNumBefore: number;
    lightNumAfter?: number; // 点亮后的数量，成功时才有值
    postId: number;
  };
  amount: number;
  status: 'success' | 'failed' | 'error';
  message: string;
  duration: number; // 执行耗时(ms)
  batchIndex: number; // 属于第几个批次
  error?: string; // 错误详情
}

/**
 * 批量点亮执行历史DTO
 * 用于前端展示
 */
export interface BatchLightExecutionHistoryDto {
  id: string;
  batchId: string;
  timestamp: string;
  accountApiKey: string; // 脱敏后的API Key
  accountEmail: string;
  assetId: number;
  assetSymbol: string;
  assetName: string;
  amount: number;
  status: 'success' | 'failed' | 'error';
  message: string;
  duration: number;
  lightNumBefore: number;
  lightNumAfter?: number;
  batchIndex: number;
}
