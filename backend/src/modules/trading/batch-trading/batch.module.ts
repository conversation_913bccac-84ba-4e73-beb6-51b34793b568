import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BatchController } from './batch.controller';
import { BatchService } from './batch.service';
import { FirstLightAnalyzerService } from './services/first-light-analyzer.service';
import { LightQueryOptimizerService } from './services/light-query-optimizer.service';
import { BatchLoginService } from './services/batch-login.service';

import { PostQueryService } from './services/post-query.service';
import { EnhancedBatchLightService } from './services/enhanced-batch-light.service';
import { EnhancedBatchLightCoreService } from './services/enhanced-batch-light-core.service';
import { LightOptimizerController } from './controllers/light-optimizer.controller';
import { EnhancedBatchLightController } from './controllers/enhanced-batch-light.controller';
import { DatabaseModule } from '../../../core/database/database.module';
import { AuthModule } from '../../auth/auth.module';
import { BalanceModule } from '../../balance/balance.module';
import { UsersModule } from '../../users/users.module';
import { CommonModule } from '../../../common/common.module';
import { CoreModule } from '../../../core/core.module';

@Module({
  imports: [
    DatabaseModule,
    CoreModule,
    AuthModule,
    BalanceModule,
    UsersModule,
    CommonModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [
    BatchController,
    LightOptimizerController,
    EnhancedBatchLightController,
  ],
  providers: [
    BatchService,
    FirstLightAnalyzerService,
    LightQueryOptimizerService,
    BatchLoginService,
    PostQueryService,
    EnhancedBatchLightService,
    EnhancedBatchLightCoreService,
  ],
  exports: [
    BatchService,
    FirstLightAnalyzerService,
    LightQueryOptimizerService,
    BatchLoginService,
    PostQueryService,
    EnhancedBatchLightService,
    EnhancedBatchLightCoreService,
  ],
})
export class BatchModule {}
