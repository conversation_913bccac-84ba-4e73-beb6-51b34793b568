import { Injectable, Logger } from '@nestjs/common';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
// import { RandomUtils } from '../../../../core/shared/random.utils';
import { PoolState } from '../types/random-pool-trade.types';
import { DatabaseTradeRecordService } from './database-trade-record.service';
import { TradeStrategyService } from './trade-strategy.service';

/**
 * 仓位清理服务
 * 负责清理活跃池子和历史未卖出仓位
 */
@Injectable()
export class PositionCleanupService {
  private readonly logger = new Logger(PositionCleanupService.name);

  constructor(
    private readonly databaseTradeRecordService: DatabaseTradeRecordService,
    private readonly tradeStrategy: TradeStrategyService,
    private readonly businessLogger: BusinessLoggerService,
  ) {}

  /**
   * 清空所有活跃池子的仓位
   */
  async clearAllPositions(
    activePools: Map<number, PoolState>,
    sessionId?: string,
  ): Promise<boolean> {
    const activePoolsArray = Array.from(activePools.values());
    let allClearingSuccess = true;

    for (const poolState of activePoolsArray) {
      const sellSuccess = await this.sellAllPoolTokens(poolState, sessionId);
      if (sellSuccess) {
        this.logger.log(`✅ 池子 ${poolState.symbol} 清仓成功`);
      } else {
        this.logger.log(`❌ 池子 ${poolState.symbol} 清仓失败`);
        allClearingSuccess = false;
      }
      // 无论成功失败都从活跃池子列表中移除
      activePools.delete(poolState.poolId);
    }

    // 确保活跃池子Map完全清空
    activePools.clear();

    if (allClearingSuccess) {
      this.logger.log('✅ 所有活跃池子清仓成功，清仓完成');
    } else {
      this.logger.log('⚠️ 部分池子清仓失败，但已从活跃列表移除');
    }

    return allClearingSuccess;
  }

  /**
   * 清理所有历史未卖出仓位
   */
  async clearAllHistoricalPositions(): Promise<void> {
    this.logger.log('🧹 开始清理所有历史未卖出仓位...');

    try {
      // 先检查数据库中是否有任何交易记录
      const allRecords = this.databaseTradeRecordService.getAllTradeRecords();
      // 只在有记录时显示总数
      if (allRecords.length > 0) {
        this.logger.log(`📊 数据库中有 ${allRecords.length} 条交易记录`);
      }

      // 获取所有未卖出的仓位
      const unsoldPositions =
        this.databaseTradeRecordService.getAllUnsoldPositions();

      if (unsoldPositions.length === 0) {
        this.logger.log('✅ 没有发现历史未卖出仓位');
        return;
      }

      this.logger.log(
        `🔍 发现 ${unsoldPositions.length} 个历史未卖出仓位，开始清仓...`,
      );

      for (const position of unsoldPositions) {
        await this.sellHistoricalPosition(position);
      }

      this.logger.log('✅ 所有历史仓位清理完成');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`清理历史仓位失败: ${errorMessage}`);

      this.businessLogger.logApiCall({
        service: 'PositionCleanupService',
        method: '清理历史仓位',
        duration: 0,
        success: false,
        error: errorMessage,
      });
    }
  }

  /**
   * 卖出池子的所有Token
   */
  private async sellAllPoolTokens(poolState: PoolState, sessionId?: string): Promise<boolean> {
    try {
      const startTime = Date.now();
      
      this.logger.log(
        `🔄 准备清仓活跃池子 ${poolState.symbol}，当前token数量: ${poolState.accumulatedTokenAmount}`,
      );
      
      const sellResult = await this.tradeStrategy.executeSellStrategy(
        sessionId || `cleanup-${Date.now()}`,
        poolState,
      );

      const duration = Date.now() - startTime;

      if (sellResult.success) {
        this.logger.log(`✅ 池子 ${poolState.symbol} 所有用户卖出成功，卖出金额: ${sellResult.sellAmount || 0} USDT`);

        // 记录成功的卖出交易
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: sellResult.sellAmount || 0,
          success: true,
          traderEmail: poolState.assignedAccount?.email,
        });

        this.businessLogger.logApiCall({
          service: 'PositionCleanupService',
          method: `卖出池子 ${poolState.symbol}`,
          duration,
          success: true,
        });
      } else {
        this.logger.error(`池子 ${poolState.symbol} 部分用户卖出失败`);

        // 记录失败的卖出交易
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: 0,
          success: false,
          traderEmail: poolState.assignedAccount?.email,
          error: '卖出操作失败',
        });

        this.businessLogger.logApiCall({
          service: 'PositionCleanupService',
          method: `卖出池子 ${poolState.symbol}`,
          duration,
          success: false,
          error: '卖出操作失败',
        });
      }

      return sellResult.success;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `卖出池子 ${poolState.symbol} 时发生错误: ${errorMessage}`,
        error,
      );

      this.businessLogger.logApiCall({
        service: 'PositionCleanupService',
        method: `卖出池子 ${poolState.symbol}`,
        duration: 0,
        success: false,
        error: errorMessage,
      });

      return false;
    }
  }

  /**
   * 卖出历史仓位
   */
  private async sellHistoricalPosition(position: {
    poolId: number;
    symbol: string;
    totalTokenAmount: number;
    buyRecords: any[];
  }): Promise<void> {
    if (position.buyRecords.length === 0) {
      this.logger.warn(`池子 ${position.symbol} 没有买入记录，跳过清仓`);
      return;
    }

    try {
      // 获取第一个买入记录的账户信息用于卖出
      const firstBuyRecord = position.buyRecords[0];
      
      // 创建临时的池子状态用于清仓
      const now = Date.now();
      const tempPoolState: PoolState = {
        poolId: position.poolId,
        symbol: position.symbol,
        completedBuyCount: 0,
        targetBuyCount: 0,
        status: 'selling',
        lastOperationTime: now,
        createdTime: now,
        accumulatedTokenAmount: position.totalTokenAmount, // 使用真实的token数量
        assignedAccount: {
          apiKey: firstBuyRecord.traderApiKey,
          email: firstBuyRecord.traderEmail,
          secret: '', // 历史记录中没有secret，需要重新获取
        },
      };

      this.logger.log(
        `🔄 准备清仓池子 ${position.symbol}，token数量: ${position.totalTokenAmount}`,
      );

      // 使用交易策略服务执行卖出
      const sellResult = await this.tradeStrategy.executeSellStrategy(
        `cleanup-${Date.now()}`,
        tempPoolState,
      );

      if (sellResult.success) {
        this.logger.log(`✅ 池子 ${position.symbol} 历史仓位清仓成功`);
      } else {
        this.logger.error(`❌ 池子 ${position.symbol} 历史仓位清仓失败`);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `清仓池子 ${position.symbol} 时发生错误: ${errorMessage}`,
        error,
      );
    }
  }
}
