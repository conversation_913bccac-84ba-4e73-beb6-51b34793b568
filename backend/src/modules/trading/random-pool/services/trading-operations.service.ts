import { Injectable, Logger } from '@nestjs/common';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import { RandomUtils } from '../../../../core/shared/random.utils';
import {
  RandomPoolTradeConfig,
  PoolState,
  RandomPoolTradeSession,
} from '../types/random-pool-trade.types';
import { DatabaseTradeRecordService } from './database-trade-record.service';
import { PoolManagerService } from './pool-manager.service';
import { TradeStrategyService } from './trade-strategy.service';

/**
 * 交易操作服务
 * 负责买入卖出操作的协调和执行
 */
@Injectable()
export class TradingOperationsService {
  private readonly logger = new Logger(TradingOperationsService.name);

  constructor(
    private readonly poolManager: PoolManagerService,
    private readonly tradeStrategy: TradeStrategyService,
    private readonly databaseTradeRecordService: DatabaseTradeRecordService,
    private readonly businessLogger: BusinessLoggerService,
  ) {}

  /**
   * 处理卖出操作
   */
  async processSellOperations(
    session: RandomPoolTradeSession,
    onSellComplete?: (poolState: PoolState, success: boolean) => void,
  ): Promise<void> {
    const now = Date.now();
    const activePools = Array.from(session.activePools.values());

    for (const poolState of activePools) {
      // 检查是否达到买入次数且需要卖出
      if (
        poolState.completedBuyCount >= poolState.targetBuyCount &&
        (poolState.status === 'buying' ||
          poolState.status === 'waiting_to_sell')
      ) {
        // 如果还没有确定卖出时间，现在确定
        if (!poolState.scheduledSellTime) {
          const sellDelay = RandomUtils.randomDelay(
            session.config.minSellDelayMs,
            session.config.maxSellDelayMs,
          );
          poolState.scheduledSellTime = poolState.lastOperationTime + sellDelay;
          this.logger.log(
            `池子 ${poolState.symbol} 完成买入目标，预定 ${Math.round(sellDelay / 1000)}秒后卖出`,
          );
        }

        // 检查是否到了预定的卖出时间
        if (now >= poolState.scheduledSellTime) {
          this.logger.log(`池子 ${poolState.symbol} 开始卖出操作`);
          poolState.status = 'selling';

          const sellSuccess = await this.executeSellOperation(
            session.sessionId,
            poolState,
          );

          if (sellSuccess) {
            // 只有卖出成功时才移除池子
            session.activePools.delete(poolState.poolId);
            // 更新活跃池子计数
            session.statistics.activePoolsCount = session.activePools.size;
            this.logger.log(`池子 ${poolState.symbol} 卖出完成，剩余活跃池子数=${session.statistics.activePoolsCount}`);
          } else {
            // 卖出失败，重置状态等待下次重试
            poolState.status = 'waiting_to_sell';
            this.logger.log(`池子 ${poolState.symbol} 卖出失败，等待重试`);
          }

          // 通知调用方卖出操作完成
          onSellComplete?.(poolState, sellSuccess);
        }
      }
    }
  }

  /**
   * 处理买入操作
   */
  async processBuyOperations(
    session: RandomPoolTradeSession,
    onBuyComplete?: (
      poolState: PoolState,
      success: boolean,
      buyAmount?: number,
    ) => void,
  ): Promise<void> {
    // 如果会话已停止，不执行新的买入操作
    if (!session.isRunning) {
      return;
    }

    // 首先检查是否有未完成的池子需要继续买入
    const incompletePools = Array.from(session.activePools.values()).filter(
      (pool) =>
        pool.completedBuyCount < pool.targetBuyCount &&
        pool.status === 'buying',
    );

    let poolState: PoolState | undefined;

    if (incompletePools.length > 0) {
      // 优先选择未完成的池子（已有固定账户）
      const selectedIncompletePool = RandomUtils.randomChoice(incompletePools);
      poolState = selectedIncompletePool;
    } else {
      // 如果没有未完成的池子，开始新一轮：同时选择池子和账户
      const newRoundResult = await this.startNewTradingRound(session);
      if (!newRoundResult) return;

      poolState = newRoundResult;
    }

    // 执行买入操作
    if (poolState && poolState.completedBuyCount < poolState.targetBuyCount) {
      const buyResult = await this.executeBuyOperation(
        session.sessionId,
        poolState,
        session.config,
      );
      onBuyComplete?.(poolState, buyResult.success, buyResult.buyAmount);
    }
  }

  /**
   * 开始新的交易轮次
   */
  private async startNewTradingRound(
    session: RandomPoolTradeSession,
  ): Promise<PoolState | null> {
    // 1. 选择池子
    const selectedPool = await this.poolManager.selectRandomPool();
    if (!selectedPool) return null;

    // 2. 选择账户
    const selectedAccount = await this.tradeStrategy.selectTradingAccount();
    if (!selectedAccount) {
      this.logger.error('没有找到符合条件的交易账户，无法开始新轮次');
      return null;
    }

    // 3. 创建新的池子状态，绑定固定账户
    const now = Date.now();
    const poolState: PoolState = {
      poolId: selectedPool.poolId,
      symbol: selectedPool.symbol,
      completedBuyCount: 0,
      targetBuyCount: session.config.buyCount,
      status: 'buying',
      lastOperationTime: now,
      createdTime: now,
      accumulatedTokenAmount: 0,
      assignedAccount: {
        apiKey: selectedAccount.apiKey,
        email: selectedAccount.email,
        secret: selectedAccount.secret,
      },
    };

    session.activePools.set(selectedPool.poolId, poolState);
    // 更新活跃池子计数
    session.statistics.activePoolsCount = session.activePools.size;
    
    this.logger.log(
      `🎯 新轮次开始: 池子=${poolState.symbol}, 账户=${selectedAccount.email}, 活跃池子数=${session.statistics.activePoolsCount}`,
    );

    return poolState;
  }

  /**
   * 执行买入操作
   */
  private async executeBuyOperation(
    sessionId: string,
    poolState: PoolState,
    config: RandomPoolTradeConfig,
  ): Promise<{ success: boolean; buyAmount?: number }> {
    try {
      const buyResult = await this.tradeStrategy.executeBuyStrategy(
        sessionId,
        poolState,
        config,
      );

      if (buyResult.success) {
        // 更新池子状态
        this.poolManager.updatePoolBuyCount(poolState);

        // 记录成功的买入交易
        this.businessLogger.logTradeOperation({
          type: 'BUY',
          symbol: poolState.symbol,
          amount: buyResult.buyAmount || 0,
          success: true,
          traderEmail: poolState.assignedAccount?.email,
        });

        this.logger.log(
          `✅ 池子 ${poolState.symbol} 买入成功: ${buyResult.buyAmount} USDT`,
        );
      } else {
        // 记录失败的买入交易
        this.businessLogger.logTradeOperation({
          type: 'BUY',
          symbol: poolState.symbol,
          amount: 0,
          success: false,
          traderEmail: poolState.assignedAccount?.email,
          error: '买入操作失败',
        });

        this.logger.error(`❌ 池子 ${poolState.symbol} 买入失败`);
      }

      return {
        success: buyResult.success,
        buyAmount: buyResult.buyAmount,
      };
    } catch (error) {
      this.logger.error(`池子 ${poolState.symbol} 买入操作失败:`, error);
      return { success: false };
    }
  }

  /**
   * 执行卖出操作
   */
  private async executeSellOperation(
    sessionId: string,
    poolState: PoolState,
  ): Promise<boolean> {
    try {
      const sellResult = await this.tradeStrategy.executeSellStrategy(
        sessionId,
        poolState,
      );

      if (sellResult.success) {
        this.logger.log(`池子 ${poolState.symbol} 所有用户卖出成功`);

        // 记录成功的卖出交易
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: sellResult.sellAmount || 0,
          success: true,
          traderEmail: poolState.assignedAccount?.email,
        });
      } else {
        this.logger.error(`池子 ${poolState.symbol} 部分用户卖出失败`);

        // 记录失败的卖出交易
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: 0,
          success: false,
          traderEmail: poolState.assignedAccount?.email,
          error: '卖出操作失败',
        });
      }

      return sellResult.success;
    } catch (error) {
      this.logger.error(`池子 ${poolState.symbol} 卖出操作失败:`, error);
      return false;
    }
  }
}
