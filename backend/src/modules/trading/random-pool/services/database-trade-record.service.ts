import { Injectable, Logger } from '@nestjs/common';
import { NineLightDatabaseService } from '../../../../core/database/nine-light-database.service';
import { TradeOperationRecord } from '../types/random-pool-trade.types';
import { v4 as uuidv4 } from 'uuid';
import { <PERSON>rror<PERSON>andler } from '../../../../core/shared/error-handler.util';

/**
 * 基于SQLite数据库的交易记录服务
 * 替代原有的JSON文件存储方案
 */
@Injectable()
export class DatabaseTradeRecordService {
  private readonly logger = new Logger(DatabaseTradeRecordService.name);

  constructor(private readonly nineLightDb: NineLightDatabaseService) {}

  /**
   * 保存交易记录
   */
  saveTradeRecord(record: {
    sessionId: string;
    poolId: number;
    symbol: string;
    tradeType: 'BUY' | 'SELL';
    amount: number;
    tokenAmount?: number;
    success: boolean;
    error?: string;
    transactionId?: string;
    traderEmail?: string;
    traderApiKey?: string;
    accumulatedAmount?: number;
    completedBuyCount?: number;
    targetBuyCount?: number;
  }): string {
    return ErrorHandler.safeExecute(
      () => {
        const id = uuidv4();

        const stmt = this.nineLightDb.client.prepare(
          `INSERT INTO random_pool_trade_records (
            id, session_id, pool_id, symbol, trade_type, amount, token_amount,
            success, error, transaction_id, trader_email, trader_api_key,
            accumulated_amount, completed_buy_count, target_buy_count
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        );

        stmt.run([
          id,
          record.sessionId,
          record.poolId,
          record.symbol,
          record.tradeType,
          record.amount,
          record.tokenAmount ?? null,
          record.success ? 1 : 0,
          record.error ?? null,
          record.transactionId ?? null,
          record.traderEmail ?? null,
          record.traderApiKey ?? null,
          record.accumulatedAmount ?? null,
          record.completedBuyCount ?? null,
          record.targetBuyCount ?? null,
        ]);

        stmt.free();

        this.logger.debug(
          `交易记录已保存: ${record.symbol} ${record.tradeType} ${record.amount} USDT`,
        );

        return id;
      },
      this.logger,
      '保存交易记录',
      '',
    );
  }

  /**
   * 获取会话的交易历史记录
   */
  getSessionTradeHistory(
    sessionId: string,
    options?: {
      page?: number;
      pageSize?: number;
      tradeType?: 'BUY' | 'SELL';
      poolId?: number;
      startTime?: Date;
      endTime?: Date;
    },
  ): {
    records: TradeOperationRecord[];
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
    };
  } {
    return ErrorHandler.safeExecute(
      () => {
        const page = options?.page || 1;
        const pageSize = options?.pageSize || 20;
        const offset = (page - 1) * pageSize;

        // 构建查询条件
        const { whereClause, params } = this.buildWhereClause(
          sessionId,
          options,
        );

        // 获取总数
        const total = this.getRecordCount(whereClause, params);

        // 获取分页数据
        const records = this.getRecordsPaginated(
          whereClause,
          params,
          pageSize,
          offset,
        );

        // 转换为 TradeOperationRecord 格式
        const tradeRecords: TradeOperationRecord[] = records.map(
          (record: any) => this.convertToTradeOperationRecord(record),
        );

        return {
          records: tradeRecords,
          pagination: {
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
          },
        };
      },
      this.logger,
      '获取会话交易历史',
      {
        records: [],
        pagination: {
          total: 0,
          page: options?.page || 1,
          pageSize: options?.pageSize || 20,
          totalPages: 0,
        },
      },
    );
  }

  /**
   * 获取池子的累积金额
   */
  getPoolAccumulatedAmount(sessionId: string, poolId: number): number {
    return ErrorHandler.safeExecute(
      () => {
        const stmt = this.nineLightDb.client.prepare(
          `SELECT COALESCE(SUM(amount), 0) as accumulated_amount
           FROM random_pool_trade_records
           WHERE session_id = ? AND pool_id = ? AND trade_type = 'BUY' AND success = 1`,
        );

        stmt.bind([sessionId, poolId]);
        stmt.step();
        const result = stmt.getAsObject() as { accumulated_amount: number };
        stmt.free();

        return result.accumulated_amount;
      },
      this.logger,
      '获取池子累积金额',
      0,
    );
  }

  /**
   * 获取会话记录
   */
  getSessionRecords(sessionId: string): any[] {
    return ErrorHandler.safeExecute(
      () => {
        const stmt = this.nineLightDb.client.prepare(
          'SELECT * FROM random_pool_trade_records WHERE session_id = ? ORDER BY timestamp DESC',
        );

        const records: any[] = [];
        stmt.bind([sessionId]);
        while (stmt.step()) {
          records.push(stmt.getAsObject());
        }
        stmt.free();

        return records;
      },
      this.logger,
      '获取会话记录',
      [],
    );
  }

  /**
   * 获取所有交易记录
   */
  getAllTradeRecords(): any[] {
    return ErrorHandler.safeExecute(
      () => {
        const stmt = this.nineLightDb.client.prepare(
          'SELECT * FROM random_pool_trade_records ORDER BY timestamp DESC',
        );

        const records: any[] = [];
        while (stmt.step()) {
          records.push(stmt.getAsObject());
        }
        stmt.free();

        return records;
      },
      this.logger,
      '获取所有交易记录',
      [],
    );
  }

  /**
   * 获取所有交易记录（不依赖会话）
   */
  getAllTradeRecordsWithoutSession(): any[] {
    return this.getAllTradeRecords();
  }

  /**
   * 获取所有未卖出的仓位
   */
  getAllUnsoldPositions(): Array<{
    poolId: number;
    symbol: string;
    totalTokenAmount: number;
    buyRecords: any[];
  }> {
    return ErrorHandler.safeExecute(
      () => {
        // 获取所有成功的买入记录
        const buyStmt = this.nineLightDb.client.prepare(
          `SELECT * FROM random_pool_trade_records
           WHERE trade_type = 'BUY' AND success = 1
           ORDER BY pool_id, timestamp`,
        );

        const buyRecords: any[] = [];
        while (buyStmt.step()) {
          buyRecords.push(buyStmt.getAsObject());
        }
        buyStmt.free();

        // 按池子分组
        const poolGroups = new Map<number, any[]>();
        buyRecords.forEach((record) => {
          if (!poolGroups.has(record.pool_id)) {
            poolGroups.set(record.pool_id, []);
          }
          poolGroups.get(record.pool_id)!.push(record);
        });

        // 检查每个池子是否有对应的卖出记录
        const unsoldPositions: Array<{
          poolId: number;
          symbol: string;
          totalTokenAmount: number;
          buyRecords: any[];
        }> = [];

        for (const [poolId, records] of poolGroups) {
          const sellStmt = this.nineLightDb.client.prepare(
            `SELECT COUNT(*) as sell_count FROM random_pool_trade_records
             WHERE pool_id = ? AND trade_type = 'SELL' AND success = 1`,
          );

          sellStmt.bind([poolId]);
          sellStmt.step();
          const sellResult = sellStmt.getAsObject() as { sell_count: number };
          sellStmt.free();

          if (sellResult.sell_count === 0) {
            // 没有卖出记录，这是未卖出的仓位
            const totalTokenAmount = records.reduce(
              (sum, record) => sum + (record.token_amount || 0),
              0,
            );

            unsoldPositions.push({
              poolId,
              symbol: records[0]?.symbol || '',
              totalTokenAmount,
              buyRecords: records,
            });
          }
        }

        return unsoldPositions;
      },
      this.logger,
      '获取未卖出仓位',
      [],
    );
  }

  /**
   * 更新交易记录
   */
  updateTradeRecord(
    _sessionId: string,
    _poolId: number,
    recordId: string,
    updates: any,
  ): void {
    ErrorHandler.safeExecute(
      () => {
        // 构建更新SQL
        const updateFields = Object.keys(updates)
          .map((key) => `${key} = ?`)
          .join(', ');
        const updateValues = Object.values(updates);

        const stmt = this.nineLightDb.client.prepare(
          `UPDATE random_pool_trade_records SET ${updateFields} WHERE id = ?`,
        );

        stmt.run([...updateValues, recordId]);
        stmt.free();
      },
      this.logger,
      '更新交易记录',
      undefined,
    );
  }

  // ========================================
  // 私有辅助方法
  // ========================================

  /**
   * 构建 WHERE 子句和参数
   */
  private buildWhereClause(
    sessionId: string,
    options?: {
      tradeType?: 'BUY' | 'SELL';
      poolId?: number;
      startTime?: Date;
      endTime?: Date;
    },
  ): { whereClause: string; params: any[] } {
    let whereClause = 'WHERE session_id = ?';
    const params: any[] = [sessionId];

    if (options?.tradeType) {
      whereClause += ' AND trade_type = ?';
      params.push(options.tradeType);
    }

    if (options?.poolId) {
      whereClause += ' AND pool_id = ?';
      params.push(options.poolId);
    }

    if (options?.startTime) {
      whereClause += ' AND timestamp >= ?';
      params.push(options.startTime.toISOString());
    }

    if (options?.endTime) {
      whereClause += ' AND timestamp <= ?';
      params.push(options.endTime.toISOString());
    }

    return { whereClause, params };
  }

  /**
   * 获取记录总数
   */
  private getRecordCount(whereClause: string, params: any[]): number {
    const countStmt = this.nineLightDb.client.prepare(
      `SELECT COUNT(*) as total FROM random_pool_trade_records ${whereClause}`,
    );
    countStmt.bind(params);
    countStmt.step();
    const countResult = countStmt.getAsObject() as { total: number };
    countStmt.free();
    return countResult.total;
  }

  /**
   * 获取分页记录
   */
  private getRecordsPaginated(
    whereClause: string,
    params: any[],
    pageSize: number,
    offset: number,
  ): any[] {
    const dataStmt = this.nineLightDb.client.prepare(
      `SELECT * FROM random_pool_trade_records ${whereClause}
       ORDER BY timestamp DESC LIMIT ? OFFSET ?`,
    );

    const records: any[] = [];
    dataStmt.bind([...params, pageSize, offset]);
    while (dataStmt.step()) {
      records.push(dataStmt.getAsObject());
    }
    dataStmt.free();

    return records;
  }

  /**
   * 转换为 TradeOperationRecord 格式
   */
  private convertToTradeOperationRecord(record: any): TradeOperationRecord {
    return {
      id: record.id,
      sessionId: record.session_id,
      poolId: record.pool_id,
      symbol: record.symbol,
      tradeType: record.trade_type as 'BUY' | 'SELL',
      amount: record.amount,
      timestamp: new Date(record.timestamp).getTime(),
      success: record.success === 1,
      error: record.error,
      transactionId: record.transaction_id,
      trader:
        record.trader_email && record.trader_api_key
          ? {
              email: record.trader_email,
              apiKey: record.trader_api_key,
            }
          : undefined,
      accumulationSnapshot: {
        completedBuyCount: record.completed_buy_count || 0,
        targetBuyCount: record.target_buy_count || 0,
        accumulatedAmount: record.accumulated_amount || 0,
      },
    };
  }
}
