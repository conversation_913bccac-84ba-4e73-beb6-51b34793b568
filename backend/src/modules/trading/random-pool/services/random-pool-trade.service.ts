import { Injectable, BadRequestException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { BaseSessionService } from '../../../../core/shared/base-session.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import { RandomUtils } from '../../../../core/shared/random.utils';

import {
  RandomPoolTradeConfig,
  RandomPoolTradeSession,
  PoolState,
  TradeOperationRecord,
  RandomPoolTradeStatistics,
  RandomPoolTradeEvent,
  ActivePoolInfo,
  SelectablePool,
} from '../types/random-pool-trade.types';

import { DatabaseTradeRecordService } from './database-trade-record.service';
import { PoolManagerService } from './pool-manager.service';
import { SessionManagementService } from './session-management.service';
import { PositionCleanupService } from './position-cleanup.service';
import { TradingOperationsService } from './trading-operations.service';
import { StatisticsService } from './statistics.service';

// 内部类型定义
interface PoolInfo {
  poolId: number;
  symbol: string;
}

/**
 * 随机池子刷量服务
 * 重构后使用分层架构，职责分离
 */
@Injectable()
export class RandomPoolTradeService extends BaseSessionService<
  RandomPoolTradeSession,
  RandomPoolTradeConfig,
  RandomPoolTradeEvent
> {
  constructor(
    private readonly sessionManagement: SessionManagementService,
    private readonly positionCleanup: PositionCleanupService,
    private readonly tradingOperations: TradingOperationsService,
    private readonly statistics: StatisticsService,
    private readonly poolManager: PoolManagerService,
    private readonly databaseTradeRecordService: DatabaseTradeRecordService,
    private readonly businessLogger: BusinessLoggerService,
    eventEmitter: EventEmitter2,
  ) {
    super(eventEmitter, RandomPoolTradeService.name);
    this.logger.log('随机池子刷量服务初始化完成');
    this.businessLogger.logServiceStart('RandomPoolTradeService');
  }

  /**
   * 启动随机池子刷量
   */
  async startRandomPoolTrade(config: RandomPoolTradeConfig): Promise<{
    success: boolean;
    sessionId: string;
    message: string;
  }> {
    this.sessionManagement.validateConfig(config);

    if (this.sessionManagement.isSessionRunning()) {
      throw new BadRequestException('随机池子刷量已在运行中');
    }

    // 启动前先清理所有历史未卖出仓位
    await this.positionCleanup.clearAllHistoricalPositions();

    // 创建新会话
    this.currentSession = this.sessionManagement.createSession(config);

    // 启动主循环
    this.startMainLoop();

    this.emitSessionEvent('session_started', {});

    return {
      success: true,
      sessionId: this.currentSession.sessionId,
      message: '随机池子刷量已启动',
    };
  }

  /**
   * 停止随机池子刷量
   */
  async stopRandomPoolTrade(): Promise<{
    success: boolean;
    message: string;
  }> {
    if (!this.sessionManagement.isSessionRunning()) {
      throw new BadRequestException('随机池子刷量未在运行');
    }

    // 先停止主循环，防止新的交易
    this.stopMainLoop();

    // 清空所有活跃池子的仓位（在停止会话之前）
    let clearingSuccess = false;
    if (this.currentSession) {
      this.logger.log('开始清空所有活跃池子仓位...');
      clearingSuccess = await this.positionCleanup.clearAllPositions(
        this.currentSession.activePools,
        this.currentSession.sessionId,
      );
      this.currentSession.clearingCompleted = clearingSuccess;
      
      if (clearingSuccess) {
        this.logger.log('✅ 所有活跃池子清仓完成');
      } else {
        this.logger.warn('⚠️ 部分池子清仓失败');
      }
    }

    // 最后停止会话
    this.sessionManagement.stopSession();

    this.emitSessionEvent('session_stopped', {
      clearingCompleted: clearingSuccess,
    });

    return {
      success: true,
      message: clearingSuccess 
        ? '随机池子刷量已停止，所有仓位已清空' 
        : '随机池子刷量已停止，部分仓位清空失败',
    };
  }

  /**
   * 获取当前活跃的池子信息
   */
  getActivePools(): ActivePoolInfo[] {
    if (!this.currentSession) {
      return [];
    }

    const activePoolsInfo = this.sessionManagement.getActivePools();

    // 填充累积金额
    return activePoolsInfo.map((pool) => ({
      ...pool,
      accumulatedAmount:
        this.databaseTradeRecordService.getPoolAccumulatedAmount(
          this.currentSession!.sessionId,
          pool.poolId,
        ),
    }));
  }

  /**
   * 获取交易历史记录
   */
  getTradeHistory(): TradeOperationRecord[] {
    if (!this.currentSession) {
      return [];
    }

    return this.statistics.getTradeHistory(this.currentSession.sessionId);
  }

  /**
   * 获取交易历史记录（带分页和过滤）
   */
  getTradeHistoryWithPagination(
    sessionId?: string,
    options?: {
      page?: number;
      pageSize?: number;
      startTime?: Date;
      endTime?: Date;
    },
  ): {
    records: TradeOperationRecord[];
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
    };
  } {
    const targetSessionId = sessionId || this.currentSession?.sessionId;
    return this.statistics.getTradeHistoryWithPagination(
      targetSessionId,
      options,
    );
  }

  /**
   * 获取可选择的池子列表（公开方法）
   */
  async getSelectablePools(): Promise<SelectablePool[]> {
    return await this.poolManager.getAvailablePools();
  }

  /**
   * 获取累积统计信息（不依赖当前会话）
   */
  getCumulativeStatistics(): {
    totalBuyVolume: number;
    totalSellVolume: number;
    totalVolume: number;
    averageBuyAmount: number;
    averageSellAmount: number;
  } {
    return this.statistics.getCumulativeStatistics();
  }

  /**
   * 获取统计信息
   */
  getStatistics(): RandomPoolTradeStatistics | null {
    if (!this.currentSession) {
      return null;
    }

    const activePools = this.getActivePools();
    return this.statistics.getSessionStatistics(
      this.currentSession,
      activePools,
    );
  }

  // ========================================
  // BaseSessionService 抽象方法实现
  // ========================================

  protected validateConfig(config: RandomPoolTradeConfig): void {
    this.sessionManagement.validateConfig(config);
  }

  protected getSessionStatus() {
    const status = this.sessionManagement.getSessionStatus();
    return {
      ...status,
      runningDuration: this.getRunningDuration(),
    };
  }

  protected async cleanup(): Promise<void> {
    if (this.currentSession) {
      await this.positionCleanup.clearAllPositions(
        this.currentSession.activePools,
        this.currentSession.sessionId,
      );
    }
  }

  protected createSession(
    config: RandomPoolTradeConfig,
  ): RandomPoolTradeSession {
    return this.sessionManagement.createSession(config);
  }

  protected async executeOperation(): Promise<void> {
    if (!this.currentSession?.isRunning) return;

    try {
      await this.processNextOperation();
    } catch (error) {
      // 如果是账户选择失败等致命错误，停止会话
      if (
        error instanceof Error &&
        error.message.includes('没有找到符合条件的交易账户')
      ) {
        this.logger.error('🛑 检测到致命错误，停止交易会话');
        this.currentSession.isRunning = false;
        this.emitSessionEvent('error_occurred', {
          error: error.message,
          fatal: true,
        });
        return;
      }
      // 其他错误由基类处理
      throw error;
    }
  }

  protected getOperationInterval(): number {
    if (!this.currentSession) return 5000;

    return RandomUtils.randomInterval(
      this.currentSession.config.minBuyIntervalMs,
      this.currentSession.config.maxBuyIntervalMs,
    );
  }

  protected createEvent(type: string, data: unknown): RandomPoolTradeEvent {
    return {
      type: type as RandomPoolTradeEvent['type'],
      timestamp: Date.now(),
      sessionId: this.currentSession?.sessionId || '',
      data: data as RandomPoolTradeEvent['data'],
    };
  }

  protected getEventName(): string {
    return 'random-pool-trade.event';
  }

  // ========================================
  // 通用辅助方法
  // ========================================

  /**
   * 获取当前会话（用于新的专职服务）
   */
  private getCurrentSession(): RandomPoolTradeSession | null {
    return this.currentSession;
  }

  /**
   * 创建池子信息对象
   */
  private createPoolInfo(poolState: PoolState): PoolInfo {
    return {
      poolId: poolState.poolId,
      symbol: poolState.symbol,
    };
  }

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 处理下一个操作
   */
  private async processNextOperation(): Promise<void> {
    if (!this.currentSession) return;

    // 处理卖出操作
    const currentSession = this.getCurrentSession();
    if (!currentSession) return;

    await this.tradingOperations.processSellOperations(
      currentSession,
      (poolState, success) => {
        // 更新统计
        this.sessionManagement.updateSessionStatistics({
          sellOperation: { success, amount: 0 },
        });

        // 发送事件 - 立即发送，不使用 process.nextTick
        if (success) {
          this.emitSessionEvent('sell_operation_completed', {
            pool: this.createPoolInfo(poolState),
            trade: { type: 'SELL', success: true },
          });
        } else {
          this.emitSessionEvent('sell_operation_failed', {
            pool: this.createPoolInfo(poolState),
            trade: { type: 'SELL', success: false },
            error: '卖出操作失败',
          });
        }
      },
    );

    // 处理买入操作
    await this.tradingOperations.processBuyOperations(
      currentSession,
      (poolState, success, buyAmount) => {
        // 更新统计
        this.sessionManagement.updateSessionStatistics({
          buyOperation: { success, amount: buyAmount },
        });

        // 发送事件 - 立即发送，不使用 process.nextTick
        if (success) {
          this.emitSessionEvent('buy_operation_completed', {
            pool: this.createPoolInfo(poolState),
            trade: {
              type: 'BUY',
              success: true,
              completedCount: poolState.completedBuyCount,
              targetCount: poolState.targetBuyCount,
            },
          });
        } else {
          this.emitSessionEvent('buy_operation_failed', {
            pool: this.createPoolInfo(poolState),
            error: '买入操作失败',
          });
        }
      },
    );
  }

}
