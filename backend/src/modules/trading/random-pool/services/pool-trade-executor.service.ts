import { Injectable, Logger } from '@nestjs/common';
import { BaseTradeService } from '../../../../core/shared/base-trade.service';
import { InnerDiskTradeService } from '../../inner-disk/services/inner-disk-trade.service';
import { AccountDataService } from '../../../../common/services/account-data.service';
import { InnerDiskTradeType } from '../../inner-disk/types/inner-disk.types';
import { UserWithBalance } from '../../../../core/shared/common.types';

/**
 * 交易执行结果
 */
export interface TradeExecutionResult {
  success: boolean;
  tokenAmount?: number;
  stableCoinAmount?: number;
  transactionId?: string;
  error?: string;
}

/**
 * API 响应接口
 */
interface ApiResponse {
  code: number;
  msg?: string;
  data?: Array<{
    data?: {
      data?: {
        tokenAmount?: number;
        stableCoinAmount?: number;
        tradeTimestamp?: number;
      };
    };
  }>;
}

/**
 * 池子交易执行器
 * 负责具体的买入和卖出操作
 */
@Injectable()
export class PoolTradeExecutorService extends BaseTradeService {
  protected readonly logger = new Logger(PoolTradeExecutorService.name);

  constructor(
    protected readonly innerDiskTradeService: InnerDiskTradeService,
    private readonly accountDataService: AccountDataService,
  ) {
    super(innerDiskTradeService, PoolTradeExecutorService.name);
  }

  /**
   * 执行买入操作
   */
  async executeBuy(
    account: UserWithBalance,
    poolId: number,
    amount: number,
  ): Promise<TradeExecutionResult> {
    try {
      const result = await this.executeTradeWithAccount(
        account,
        poolId,
        amount.toString(),
        'BUY',
      );

      return this.parseTradeResult(result, 'BUY');
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`买入操作失败: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 执行卖出操作
   */
  async executeSell(
    account: UserWithBalance,
    poolId: number,
    tokenAmount: number,
  ): Promise<TradeExecutionResult> {
    try {
      const result = await this.executeTradeWithAccount(
        account,
        poolId,
        tokenAmount.toString(),
        'SELL',
      );

      return this.parseTradeResult(result, 'SELL');
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`卖出操作失败: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 使用账户信息执行交易
   */
  private async executeTradeWithAccount(
    account: UserWithBalance,
    poolId: number,
    amount: string,
    tradeType: InnerDiskTradeType,
  ) {
    // 获取完整账户信息
    const fullAccount = this.accountDataService.findAccountByApiKey(
      account.apiKey,
    );

    if (!fullAccount) {
      throw new Error(`未找到账户信息: ${account.apiKey.substring(0, 8)}...`);
    }

    // 构建交易请求
    const requestBody = [
      {
        userBean: {
          apiKey: fullAccount.apiKey,
          secret: fullAccount.secret,
        },
        innerTradeBean: [
          {
            poolId,
            amount,
            tradeType,
          },
        ],
      },
    ];

    return await this.innerDiskTradeService.executeInnerDiskTrade(requestBody);
  }

  /**
   * 解析交易结果
   */
  private parseTradeResult(
    result: ApiResponse,
    _tradeType: InnerDiskTradeType,
  ): TradeExecutionResult {
    if (result.code !== 200 || !result.data?.length) {
      return {
        success: false,
        error: result.msg || '交易失败',
      };
    }

    const tradeData = result.data[0]?.data?.data;
    if (!tradeData) {
      return {
        success: false,
        error: '交易数据格式错误',
      };
    }

    return {
      success: true,
      tokenAmount: tradeData.tokenAmount,
      stableCoinAmount: tradeData.stableCoinAmount,
      transactionId: tradeData.tradeTimestamp?.toString(),
    };
  }
}
