import { Injectable, Logger } from '@nestjs/common';
import { UserAccountSelectionService } from '../../../users/services/user-account-selection.service';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import { RandomUtils } from '../../../../core/shared/random.utils';
import { UserWithBalance } from '../../../../core/shared/common.types';
import {
  RandomPoolTradeConfig,
  PoolState,
} from '../types/random-pool-trade.types';
import { PoolTradeExecutorService } from './pool-trade-executor.service';
import { DatabaseTradeRecordService } from './database-trade-record.service';

interface TradeResult {
  success: boolean;
  tokenAmount?: number;
  stableCoinAmount?: number;
  transactionId?: string;
  error?: string;
}

/**
 * 交易策略服务
 * 负责交易决策和执行逻辑
 */
@Injectable()
export class TradeStrategyService {
  private readonly logger = new Logger(TradeStrategyService.name);

  constructor(
    private readonly accountSelectionService: UserAccountSelectionService,
    private readonly tradeExecutor: PoolTradeExecutorService,
    private readonly databaseService: DatabaseTradeRecordService,
    private readonly businessLogger: BusinessLoggerService,
  ) {}

  /**
   * 执行买入策略 - 使用池子的固定账户
   */
  async executeBuyStrategy(
    sessionId: string,
    poolState: PoolState,
    config: RandomPoolTradeConfig,
  ): Promise<{ success: boolean; buyAmount?: number; tokenAmount?: number }> {
    try {
      // 1. 生成随机买入金额
      const buyAmount = RandomUtils.randomBuyAmount(
        config.minBuyAmount,
        config.maxBuyAmount,
      );

      // 2. 使用池子的固定账户
      if (!poolState.assignedAccount) {
        this.logger.error('池子没有分配固定账户');
        return { success: false };
      }

      const account: UserWithBalance = {
        apiKey: poolState.assignedAccount.apiKey,
        email: poolState.assignedAccount.email,
        secret: poolState.assignedAccount.secret,
        password: '', // 买入操作不需要密码
        balance: 0, // 这里不需要余额信息
      };

      // 3. 执行买入交易
      const result = await this.tradeExecutor.executeBuy(
        account,
        poolState.poolId,
        buyAmount,
      );

      // 4. 处理交易结果
      if (result.success) {
        this.handleBuySuccess(sessionId, poolState, buyAmount, account, result);

        // 更新池子的累积token数量
        if (result.tokenAmount) {
          poolState.accumulatedTokenAmount += result.tokenAmount;
        }

        return {
          success: true,
          buyAmount,
          tokenAmount: result.tokenAmount,
        };
      } else {
        this.logger.error(`买入失败: ${result.error}`);
        return { success: false };
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`买入策略执行失败: ${errorMessage}`);
      return { success: false };
    }
  }

  /**
   * 执行卖出策略 - 简化版：使用池子的固定账户和累积token数量
   */
  async executeSellStrategy(
    sessionId: string,
    poolState: PoolState,
  ): Promise<{ success: boolean; sellAmount?: number }> {
    try {
      // 1. 检查池子是否有固定账户
      if (!poolState.assignedAccount) {
        this.logger.error(`池子 ${poolState.symbol} 没有分配固定账户`);
        return { success: false };
      }

      // 2. 检查是否有token需要卖出
      if (poolState.accumulatedTokenAmount <= 0) {
        this.logger.warn(`池子 ${poolState.symbol} 没有token需要卖出`);
        return { success: true, sellAmount: 0 };
      }

      // 3. 使用固定账户执行卖出
      const account: UserWithBalance = {
        apiKey: poolState.assignedAccount.apiKey,
        email: poolState.assignedAccount.email,
        secret: poolState.assignedAccount.secret,
        password: '', // 卖出操作不需要密码
        balance: 0,
      };

      this.logger.log(
        `🔄 池子 ${poolState.symbol} 开始卖出 ${poolState.accumulatedTokenAmount} tokens`,
      );

      const result = await this.tradeExecutor.executeSell(
        account,
        poolState.poolId,
        poolState.accumulatedTokenAmount,
      );

      if (result.success) {
        // 获取累积的买入总量
        const totalBuyAmount = this.databaseService.getPoolAccumulatedAmount(
          sessionId,
          poolState.poolId,
        );

        // 累积交易量 = 买入总量 + 卖出金额（总交易流水）
        const totalTransactionVolume =
          totalBuyAmount + (result.stableCoinAmount || 0);

        // 记录卖出成功
        const totalOperations = poolState.targetBuyCount + 1;
        this.databaseService.saveTradeRecord({
          sessionId,
          poolId: poolState.poolId,
          symbol: poolState.symbol,
          tradeType: 'SELL',
          amount: result.stableCoinAmount || 0,
          tokenAmount: poolState.accumulatedTokenAmount,
          success: true,
          transactionId: result.transactionId,
          traderEmail: account.email,
          traderApiKey: account.apiKey,
          accumulatedAmount: totalTransactionVolume, // 累积总交易量（买入+卖出）
          completedBuyCount: totalOperations, // 完成的总操作数
          targetBuyCount: totalOperations, // 总操作数
        });

        // 使用业务日志记录卖出成功
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: result.stableCoinAmount || 0,
          success: true,
          traderEmail: account.email,
        });

        this.logger.log(
          `✅ 池子 ${poolState.symbol} 卖出成功，获得 ${result.stableCoinAmount} USDT`,
        );

        // 重置累积token数量
        poolState.accumulatedTokenAmount = 0;

        return { success: true, sellAmount: result.stableCoinAmount || 0 };
      } else {
        // 使用业务日志记录卖出失败
        this.businessLogger.logTradeOperation({
          type: 'SELL',
          symbol: poolState.symbol,
          amount: 0,
          success: false,
          traderEmail: account.email,
          error: result.error,
        });

        this.logger.error(
          `❌ 池子 ${poolState.symbol} 卖出失败: ${result.error}`,
        );
        return { success: false };
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`卖出策略执行失败: ${errorMessage}`);
      return { success: false };
    }
  }

  /**
   * 选择交易账户
   */
  async selectTradingAccount(): Promise<UserWithBalance | null> {
    try {
      const result = await this.accountSelectionService.selectAccounts({
        tokenType: 'USDT',
        minBalance: 3,
        maxAccounts: 1,
        selectionMode: 'random',
        excludeRecent: false,
      });

      return result.selectedAccounts[0] || null;
    } catch (error) {
      this.logger.error('账户选择失败:', error);
      return null;
    }
  }

  /**
   * 处理买入成功
   */
  private handleBuySuccess(
    sessionId: string,
    poolState: PoolState,
    buyAmount: number,
    account: UserWithBalance,
    result: TradeResult,
  ): void {
    // 计算累积金额：获取当前池子的已有买入金额 + 本次买入金额
    const currentAccumulatedAmount =
      this.databaseService.getPoolAccumulatedAmount(
        sessionId,
        poolState.poolId,
      );
    const newAccumulatedAmount = currentAccumulatedAmount + buyAmount;

    // 保存交易记录 - 总操作数 = 买入次数 + 1（卖出）
    const totalOperations = poolState.targetBuyCount + 1;
    this.databaseService.saveTradeRecord({
      sessionId,
      poolId: poolState.poolId,
      symbol: poolState.symbol,
      tradeType: 'BUY',
      amount: buyAmount,
      tokenAmount: result.tokenAmount,
      success: true,
      transactionId: result.transactionId,
      traderEmail: account.email,
      traderApiKey: account.apiKey,
      accumulatedAmount: newAccumulatedAmount, // 正确计算累积金额
      completedBuyCount: poolState.completedBuyCount + 1, // 当前买入进度
      targetBuyCount: totalOperations, // 总操作数（买入+卖出）
    });

    // 使用业务日志记录买入成功
    this.businessLogger.logTradeOperation({
      type: 'BUY',
      symbol: poolState.symbol,
      amount: buyAmount,
      success: true,
      traderEmail: account.email,
    });

    this.logger.log(
      `✅ 池子 ${poolState.symbol} 买入成功: ${buyAmount} USDT, 累积: ${newAccumulatedAmount} USDT`,
    );
  }
}
