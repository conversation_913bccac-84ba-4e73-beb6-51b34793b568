import { Injectable, Logger } from '@nestjs/common';
import { InnerDiskService } from '../../inner-disk/services/inner-disk.service';
import { RandomUtils } from '../../../../core/shared/random.utils';
import { PoolAvailabilityUtil } from '../../../../core/shared/pool-availability.util';
import { PoolState, SelectablePool } from '../types/random-pool-trade.types';
import { RANDOM_POOL_TRADE_CONSTANTS } from '../constants/random-pool-trade.constants';

/**
 * 池子管理器
 * 负责池子的选择、状态管理和可用性检查
 */
@Injectable()
export class PoolManagerService {
  private readonly logger = new Logger(PoolManagerService.name);
  private poolsCache: SelectablePool[] = [];
  private lastCacheUpdate = 0;
  private readonly CACHE_TTL = RANDOM_POOL_TRADE_CONSTANTS.POOL_CACHE_TTL;

  constructor(private readonly innerDiskService: InnerDiskService) {}

  /**
   * 获取可用池子列表
   */
  async getAvailablePools(): Promise<SelectablePool[]> {
    const now = Date.now();

    // 检查缓存是否有效
    if (
      this.poolsCache.length > 0 &&
      now - this.lastCacheUpdate < this.CACHE_TTL
    ) {
      // 移除缓存使用的调试日志
      return this.poolsCache;
    }

    try {
      // 获取内盘池子数据
      const innerDiskPools = await this.innerDiskService.getAllInnerDiskData();
      // 移除获取池子数量的日志

      // 转换为可选择的池子格式
      const selectablePools: SelectablePool[] = innerDiskPools
        .filter((pool) => PoolAvailabilityUtil.isPoolAvailable(pool))
        .map((pool) => ({
          poolId: pool.id,
          symbol: pool.tokenSymbol,
          stableCoinSymbol: pool.stableCoinSymbol,
          price: pool.price,
          liquidity: pool.liquidity,
          volume24h: pool.volume24h,
          poolStatus: pool.poolStatus,
          isStop: pool.isStop,
        }));

      // 更新缓存
      this.poolsCache = selectablePools;
      this.lastCacheUpdate = now;

      // 移除过滤后池子数量的日志
      return selectablePools;
    } catch (error) {
      this.logger.error('获取可用池子失败:', error);
      return [];
    }
  }

  /**
   * 随机选择一个池子
   */
  async selectRandomPool(): Promise<SelectablePool | null> {
    try {
      const availablePools = await this.getAvailablePools();
      if (availablePools.length === 0) {
        this.logger.warn('没有可用的池子');
        return null;
      }

      const selectedPool = RandomUtils.randomChoice(availablePools);
      // 移除随机选择池子的详细日志
      return selectedPool;
    } catch (error) {
      this.logger.error('选择随机池子失败:', error);
      return null;
    }
  }

  /**
   * 创建新的池子状态
   */
  createPoolState(pool: SelectablePool, targetBuyCount: number): PoolState {
    const now = Date.now();
    return {
      poolId: pool.poolId,
      symbol: pool.symbol,
      completedBuyCount: 0,
      targetBuyCount,
      status: 'buying',
      lastOperationTime: now,
      createdTime: now,
      accumulatedTokenAmount: 0,
    };
  }

  /**
   * 检查池子是否完成买入目标
   */
  isPoolBuyComplete(poolState: PoolState): boolean {
    return poolState.completedBuyCount >= poolState.targetBuyCount;
  }

  /**
   * 更新池子买入计数
   */
  updatePoolBuyCount(poolState: PoolState): void {
    poolState.completedBuyCount++;
    poolState.lastOperationTime = Date.now();

    if (this.isPoolBuyComplete(poolState)) {
      poolState.status = 'waiting_to_sell';
    }
  }

  /**
   * 检查池子是否可以卖出
   */
  canPoolSell(poolState: PoolState, sellDelayMs: number): boolean {
    if (!this.isPoolBuyComplete(poolState)) {
      return false;
    }

    const timeSinceLastOperation = Date.now() - poolState.lastOperationTime;
    return timeSinceLastOperation >= sellDelayMs;
  }

  /**
   * 标记池子为卖出状态
   */
  markPoolAsSelling(poolState: PoolState): void {
    poolState.status = 'selling';
    poolState.lastOperationTime = Date.now();
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.poolsCache = [];
    this.lastCacheUpdate = 0;
    // 移除缓存清除的调试日志
  }
}
