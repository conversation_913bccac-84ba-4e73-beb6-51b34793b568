import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { BusinessLoggerService } from '../../../../core/infrastructure/business-logger.service';
import { ValidationUtils } from '../../../../core/shared/validation.utils';
import {
  RandomPoolTradeConfig,
  RandomPoolTradeSession,
  ActivePoolInfo,
} from '../types/random-pool-trade.types';

/**
 * 会话管理服务
 * 负责交易会话的生命周期管理
 */
@Injectable()
export class SessionManagementService {
  private readonly logger = new Logger(SessionManagementService.name);
  private currentSession: RandomPoolTradeSession | null = null;

  constructor(private readonly businessLogger: BusinessLoggerService) {}

  /**
   * 创建新的交易会话
   */
  createSession(config: RandomPoolTradeConfig): RandomPoolTradeSession {
    const sessionId = uuidv4();
    const now = Date.now();

    this.currentSession = {
      sessionId,
      config,
      isRunning: true,
      startTime: now,
      activePools: new Map(),
      poolCooldowns: new Map(),
      statistics: {
        totalBuyOperations: 0,
        totalSellOperations: 0,
        successfulBuyOperations: 0,
        successfulSellOperations: 0,
        totalVolume: 0,
        activePoolsCount: 0,
      },
    };

    this.logger.log(`创建新的交易会话: ${sessionId}`);
    this.businessLogger.logServiceStart('SessionManagement');

    return this.currentSession;
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): RandomPoolTradeSession | null {
    return this.currentSession;
  }

  /**
   * 停止当前会话
   */
  stopSession(): void {
    if (!this.currentSession?.isRunning) {
      throw new BadRequestException('随机池子刷量未在运行');
    }

    this.currentSession.isRunning = false;
    this.currentSession.endTime = Date.now();

    this.businessLogger.logServiceStop('SessionManagement', '用户手动停止');
    this.logger.log(`会话 ${this.currentSession.sessionId} 已停止`);
  }

  /**
   * 清理会话
   */
  clearSession(): void {
    this.currentSession = null;
  }

  /**
   * 检查会话是否运行中
   */
  isSessionRunning(): boolean {
    return this.currentSession?.isRunning || false;
  }

  /**
   * 获取会话状态
   */
  getSessionStatus() {
    return {
      isRunning: this.currentSession?.isRunning || false,
      sessionId: this.currentSession?.sessionId,
      startTime: this.currentSession?.startTime,
      endTime: this.currentSession?.endTime,
    };
  }

  /**
   * 获取当前活跃的池子信息
   */
  getActivePools(): ActivePoolInfo[] {
    if (!this.currentSession) {
      return [];
    }

    const activePoolsInfo: ActivePoolInfo[] = [];

    for (const pool of this.currentSession.activePools.values()) {
      activePoolsInfo.push({
        poolId: pool.poolId,
        symbol: pool.symbol,
        accumulatedAmount: 0, // 将由调用方填充
        completedBuyCount: pool.completedBuyCount,
        targetBuyCount: pool.targetBuyCount,
        status: pool.status,
        lastOperationTime: pool.lastOperationTime,
      });
    }

    return activePoolsInfo;
  }

  /**
   * 验证配置
   */
  validateConfig(config: RandomPoolTradeConfig): void {
    ValidationUtils.validateBasicTradeConfig(config);
  }

  /**
   * 更新会话统计
   */
  updateSessionStatistics(updates: {
    buyOperation?: { success: boolean; amount?: number };
    sellOperation?: { success: boolean; amount?: number };
  }): void {
    if (!this.currentSession) return;

    const { buyOperation, sellOperation } = updates;

    if (buyOperation) {
      this.currentSession.statistics.totalBuyOperations++;
      if (buyOperation.success) {
        this.currentSession.statistics.successfulBuyOperations++;
        if (buyOperation.amount) {
          this.currentSession.statistics.totalVolume += buyOperation.amount;
        }
      }
    }

    if (sellOperation) {
      this.currentSession.statistics.totalSellOperations++;
      if (sellOperation.success) {
        this.currentSession.statistics.successfulSellOperations++;
        if (sellOperation.amount) {
          this.currentSession.statistics.totalVolume += sellOperation.amount;
        }
      }
    }

    // 更新活跃池子计数
    this.currentSession.statistics.activePoolsCount = this.currentSession.activePools.size;
  }
}
