import { Injectable } from '@nestjs/common';
import {
  RandomPoolTradeSession,
  RandomPoolTradeStatistics,
  TradeOperationRecord,
  ActivePoolInfo,
} from '../types/random-pool-trade.types';
import { DatabaseTradeRecordService } from './database-trade-record.service';

interface TradeRecord {
  status: string;
  buyAmount: number;
  account?: {
    email: string;
    apiKey: string;
  };
}

/**
 * 统计服务
 * 负责交易统计数据的计算和聚合
 */
@Injectable()
export class StatisticsService {
  constructor(
    private readonly databaseTradeRecordService: DatabaseTradeRecordService,
  ) {}

  /**
   * 获取交易历史记录
   */
  getTradeHistory(sessionId: string): TradeOperationRecord[] {
    const historyResult =
      this.databaseTradeRecordService.getSessionTradeHistory(sessionId, {
        pageSize: 1000, // 获取大量记录用于显示
      });

    return historyResult.records;
  }

  /**
   * 获取交易历史记录（带分页和过滤）
   */
  getTradeHistoryWithPagination(
    sessionId?: string,
    options?: {
      page?: number;
      pageSize?: number;
      startTime?: Date;
      endTime?: Date;
    },
  ): {
    records: TradeOperationRecord[];
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
    };
  } {
    if (!sessionId) {
      return {
        records: [],
        pagination: {
          total: 0,
          page: options?.page || 1,
          pageSize: options?.pageSize || 20,
          totalPages: 0,
        },
      };
    }

    return this.databaseTradeRecordService.getSessionTradeHistory(
      sessionId,
      options,
    );
  }

  /**
   * 获取累积统计信息（不依赖当前会话）
   */
  getCumulativeStatistics(): {
    totalBuyVolume: number;
    totalSellVolume: number;
    totalVolume: number;
    averageBuyAmount: number;
    averageSellAmount: number;
  } {
    // 获取所有历史交易记录来计算累积统计（不依赖会话）
    const allHistoricalRecords =
      this.databaseTradeRecordService.getAllTradeRecordsWithoutSession();

    // 使用通用方法计算统计
    const statistics = this.calculateTradeStatistics(allHistoricalRecords);

    return {
      totalBuyVolume: statistics.totalBuyVolume,
      totalSellVolume: statistics.totalSellVolume,
      totalVolume: statistics.totalVolume,
      averageBuyAmount: statistics.averageBuyAmount,
      averageSellAmount: statistics.averageSellAmount,
    };
  }

  /**
   * 获取会话统计信息
   */
  getSessionStatistics(
    session: RandomPoolTradeSession,
    activePools: ActivePoolInfo[],
  ): RandomPoolTradeStatistics {
    // 获取当前会话的交易记录
    const sessionRecords = this.databaseTradeRecordService.getSessionRecords(
      session.sessionId,
    ) as TradeRecord[];

    // 获取所有历史交易记录来计算累积统计（不依赖会话）
    const allHistoricalRecords =
      this.databaseTradeRecordService.getAllTradeRecordsWithoutSession();

    const completedPoolsCount = sessionRecords.filter(
      (r: TradeRecord) => r.status === 'sold',
    ).length;

    // 使用通用方法计算历史统计
    const historicalStats = this.calculateTradeStatistics(allHistoricalRecords);

    return {
      session: {
        sessionId: session.sessionId,
        isRunning: session.isRunning,
        startTime: session.startTime,
        endTime: session.endTime,
        runningDuration: session.isRunning
          ? Date.now() - session.startTime
          : (session.endTime || Date.now()) - session.startTime,
        clearingCompleted: session.clearingCompleted || false,
      },
      operations: {
        totalBuyOperations: session.statistics.totalBuyOperations,
        totalSellOperations: session.statistics.totalSellOperations,
        successfulBuyOperations: session.statistics.successfulBuyOperations,
        successfulSellOperations: session.statistics.successfulSellOperations,
        buySuccessRate:
          session.statistics.totalBuyOperations > 0
            ? (session.statistics.successfulBuyOperations /
                session.statistics.totalBuyOperations) *
              100
            : 0,
        sellSuccessRate:
          session.statistics.totalSellOperations > 0
            ? (session.statistics.successfulSellOperations /
                session.statistics.totalSellOperations) *
              100
            : 0,
      },
      pools: {
        activePoolsCount: activePools.length,
        completedPoolsCount,
        totalPoolsProcessed: activePools.length + completedPoolsCount,
      },
      volume: {
        totalBuyVolume: historicalStats.totalBuyVolume,
        totalSellVolume: historicalStats.totalSellVolume,
        totalVolume: historicalStats.totalVolume,
        averageBuyAmount: historicalStats.averageBuyAmount,
        averageSellAmount: historicalStats.averageSellAmount,
      },
    };
  }

  /**
   * 计算交易记录统计
   */
  private calculateTradeStatistics(records: any[]): {
    buyRecords: any[];
    sellRecords: any[];
    totalBuyVolume: number;
    totalSellVolume: number;
    totalVolume: number;
    averageBuyAmount: number;
    averageSellAmount: number;
  } {
    const buyRecords = records.filter((r: any) => {
      const isSuccess = r.success === 1 || r.success === true;
      const hasAmount = r.amount && r.amount > 0;
      const matchesType = r.trade_type === 'BUY' || r.tradeType === 'BUY';
      return isSuccess && hasAmount && matchesType;
    });

    const sellRecords = records.filter((r: any) => {
      const isSuccess = r.success === 1 || r.success === true;
      const hasAmount = r.amount && r.amount > 0;
      const matchesType = r.trade_type === 'SELL' || r.tradeType === 'SELL';
      return isSuccess && hasAmount && matchesType;
    });

    const totalBuyVolume = buyRecords.reduce(
      (sum: number, record: any) => sum + (record.amount || 0),
      0,
    );

    const totalSellVolume = sellRecords.reduce(
      (sum: number, record: any) => sum + (record.amount || 0),
      0,
    );

    const totalVolume = totalBuyVolume + totalSellVolume;
    const averageBuyAmount =
      buyRecords.length > 0 ? totalBuyVolume / buyRecords.length : 0;
    const averageSellAmount =
      sellRecords.length > 0 ? totalSellVolume / sellRecords.length : 0;

    return {
      buyRecords,
      sellRecords,
      totalBuyVolume,
      totalSellVolume,
      totalVolume,
      averageBuyAmount,
      averageSellAmount,
    };
  }
}
