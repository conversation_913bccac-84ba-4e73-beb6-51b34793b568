import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { RandomPoolTradeController } from './controllers/random-pool-trade.controller';
import { RandomPoolTradeService } from './services/random-pool-trade.service';

import { DatabaseTradeRecordService } from './services/database-trade-record.service';

import { PoolManagerService } from './services/pool-manager.service';
import { TradeStrategyService } from './services/trade-strategy.service';
import { PoolTradeExecutorService } from './services/pool-trade-executor.service';
import { SessionManagementService } from './services/session-management.service';
import { PositionCleanupService } from './services/position-cleanup.service';
import { TradingOperationsService } from './services/trading-operations.service';
import { StatisticsService } from './services/statistics.service';
import { InnerDiskModule } from '../inner-disk/inner-disk.module';
import { AuthModule } from '../../auth/auth.module';
import { CommonModule } from '../../../common/common.module';
import { UsersModule } from '../../users/users.module';

/**
 * 随机池子刷量模块
 *
 * 功能特性：
 * - 随机选择池子进行买入卖出操作
 * - 支持买入累积逻辑和配置化参数
 * - 实时状态推送和历史记录管理
 * - 集成现有的内盘交易服务
 */
@Module({
  imports: [
    // 导入事件模块用于实时推送
    EventEmitterModule,
    // 导入内盘模块以复用现有服务
    InnerDiskModule,
    // 导入认证模块用于SSE认证
    AuthModule,
    // 导入通用模块用于基础服务
    CommonModule,
    // 导入用户模块用于账户选择服务
    UsersModule,
  ],
  controllers: [RandomPoolTradeController],
  providers: [
    RandomPoolTradeService,
    DatabaseTradeRecordService,
    PoolManagerService,
    TradeStrategyService,
    PoolTradeExecutorService,
    // 重构后新增的专职服务
    SessionManagementService,
    PositionCleanupService,
    TradingOperationsService,
    StatisticsService,
  ],
  exports: [RandomPoolTradeService, DatabaseTradeRecordService],
})
export class RandomPoolTradeModule {}
