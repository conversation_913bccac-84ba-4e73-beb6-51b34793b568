/**
 * 随机池子刷量功能类型定义
 */

import { InnerDiskTradeType } from '../../inner-disk/types/inner-disk.types';
import {
  PoolTradeStatusType,
  OperationTypeType,
} from '../../../../core/shared/trade-status.types';

/**
 * 随机池子刷量配置
 */
export interface RandomPoolTradeConfig {
  /** 买入量随机区间 - 最小值 */
  minBuyAmount: number;
  /** 买入量随机区间 - 最大值 */
  maxBuyAmount: number;
  /** 买入操作间隔时间随机区间 - 最小值（毫秒） */
  minBuyIntervalMs: number;
  /** 买入操作间隔时间随机区间 - 最大值（毫秒） */
  maxBuyIntervalMs: number;
  /** 买入完成到卖出的延迟时间随机区间 - 最小值（毫秒） */
  minSellDelayMs: number;
  /** 买入完成到卖出的延迟时间随机区间 - 最大值（毫秒） */
  maxSellDelayMs: number;
  /** 每个池子的买入次数 */
  buyCount: number;
  /** 池子选择冷却时间（毫秒），避免重复选择同一池子 */
  poolCooldownMs: number;
}

/**
 * 交易记录（JSON文件存储）
 */
export interface TradeRecord {
  /** 记录ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 池子ID */
  poolId: number;
  /** 池子符号 */
  symbol: string;
  /** 买入金额（USDT） */
  buyAmount: number;
  /** 获得的Token数量 */
  tokenAmount: number;
  /** 买入时间 */
  buyTime: number;
  /** 交易账户 */
  account: {
    apiKey: string;
    email: string;
  };
  /** 买入交易ID */
  buyTransactionId?: string;
  /** 状态：bought（已买入）、sold（已卖出） */
  status: 'bought' | 'sold';
  /** 卖出时间（如果已卖出） */
  sellTime?: number;
  /** 卖出交易ID（如果已卖出） */
  sellTransactionId?: string;
  /** 卖出金额（USDT，如果已卖出） */
  sellAmount?: number;
}

/**
 * 池子状态
 */
export interface PoolState {
  /** 池子ID */
  poolId: number;
  /** 池子符号 */
  symbol: string;
  /** 已完成买入次数 */
  completedBuyCount: number;
  /** 目标买入次数 */
  targetBuyCount: number;
  /** 状态 */
  status: PoolTradeStatusType;
  /** 最后操作时间 */
  lastOperationTime: number;
  /** 预定卖出时间（当完成买入目标时确定） */
  scheduledSellTime?: number;
  /** 池子创建时间（用于冷却时间计算） */
  createdTime: number;
  /** 固定的交易账户（一轮制：一个账户负责一个池子的完整流程） */
  assignedAccount?: {
    apiKey: string;
    email: string;
    secret: string;
  };
  /** 累积的token数量 */
  accumulatedTokenAmount: number;
}

/**
 * 活跃池子信息（用于API响应）
 */
export interface ActivePoolInfo {
  /** 池子ID */
  poolId: number;
  /** 池子符号 */
  symbol: string;
  /** 状态 */
  status: PoolTradeStatusType;
  /** 当前累积买入量（从交易记录计算） */
  accumulatedAmount: number;
  /** 已完成买入次数 */
  completedBuyCount: number;
  /** 目标买入次数 */
  targetBuyCount: number;
  /** 最后操作时间 */
  lastOperationTime: number;
}

/**
 * 随机池子刷量会话状态
 */
export interface RandomPoolTradeSession {
  /** 会话ID */
  sessionId: string;
  /** 配置参数 */
  config: RandomPoolTradeConfig;
  /** 运行状态 */
  isRunning: boolean;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 清仓完成状态（停止后是否已完成所有清仓操作） */
  clearingCompleted?: boolean;
  /** 当前活跃的池子状态 */
  activePools: Map<number, PoolState>;
  /** 池子冷却记录 */
  poolCooldowns: Map<number, number>;
  /** 统计信息 */
  statistics: {
    totalBuyOperations: number;
    totalSellOperations: number;
    successfulBuyOperations: number;
    successfulSellOperations: number;
    totalVolume: number;
    activePoolsCount: number;
  };
  /** 主循环定时器ID */
  mainLoopTimerId?: NodeJS.Timeout;
}

/**
 * 交易操作记录
 */
export interface TradeOperationRecord {
  /** 记录ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 池子ID */
  poolId: number;
  /** 池子符号 */
  symbol: string;
  /** 交易类型 */
  tradeType: OperationTypeType;
  /** 交易数量 */
  amount: number;
  /** 交易时间 */
  timestamp: number;
  /** 交易结果 */
  success: boolean;
  /** 错误信息（如果失败） */
  error?: string;
  /** 交易ID（如果成功） */
  transactionId?: string;
  /** 交易者信息 */
  trader?: {
    /** 交易账户邮箱 */
    email: string;
    /** 账户API Key */
    apiKey: string;
  };
  /** 累积状态快照 */
  accumulationSnapshot?: {
    accumulatedAmount: number;
    completedBuyCount: number;
    targetBuyCount: number;
  };
}

/**
 * 随机池子刷量统计信息
 */
export interface RandomPoolTradeStatistics {
  /** 会话信息 */
  session: {
    sessionId: string;
    isRunning: boolean;
    startTime: number;
    endTime?: number;
    runningDuration: number;
    clearingCompleted?: boolean;
  };
  /** 操作统计 */
  operations: {
    totalBuyOperations: number;
    totalSellOperations: number;
    successfulBuyOperations: number;
    successfulSellOperations: number;
    buySuccessRate: number;
    sellSuccessRate: number;
  };
  /** 池子统计 */
  pools: {
    activePoolsCount: number;
    completedPoolsCount: number;
    totalPoolsProcessed: number;
  };
  /** 交易量统计 */
  volume: {
    totalBuyVolume: number;
    totalSellVolume: number;
    totalVolume: number;
    averageBuyAmount: number;
    averageSellAmount: number;
  };
}

/**
 * 实时事件类型
 */
export type RandomPoolTradeEventType =
  | 'session_started'
  | 'session_stopped'
  | 'pool_selected'
  | 'buy_operation_started'
  | 'buy_operation_completed'
  | 'buy_operation_failed'
  | 'sell_operation_started'
  | 'sell_operation_completed'
  | 'sell_operation_failed'
  | 'pool_completed'
  | 'statistics_updated'
  | 'account_selection_failed'
  | 'error_occurred';

/**
 * 实时事件数据
 */
export interface RandomPoolTradeEvent {
  /** 事件类型 */
  type: RandomPoolTradeEventType;
  /** 事件时间戳 */
  timestamp: number;
  /** 会话ID */
  sessionId: string;
  /** 事件数据 */
  data: {
    /** 池子信息（如果相关） */
    pool?: {
      poolId: number;
      symbol: string;
    };
    /** 交易信息（如果相关） */
    trade?: {
      type: InnerDiskTradeType;
      amount: number;
      success: boolean;
      error?: string;
    };
    /** 累积状态（如果相关） */
    accumulation?: {
      accumulatedAmount: number;
      completedBuyCount: number;
      targetBuyCount: number;
    };
    /** 统计信息（如果相关） */
    statistics?: RandomPoolTradeStatistics;
    /** 错误信息（如果相关） */
    error?: string;
  };
}

/**
 * 可选择的池子信息
 */
export interface SelectablePool {
  /** 池子ID */
  poolId: number;
  /** 代币符号 */
  symbol: string;
  /** 稳定币符号 */
  stableCoinSymbol: string;
  /** 当前价格 */
  price: number;
  /** 流动性 */
  liquidity: number;
  /** 24小时交易量 */
  volume24h: number;
  /** 池子状态 */
  poolStatus: string;
  /** 是否停止 */
  isStop: number;
  /** 最后选择时间（用于冷却） */
  lastSelectedAt?: number;
}
