import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Logger,
  UseGuards,
  Sse,
  MessageEvent,
  UnauthorizedException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Observable, fromEvent, map, interval, merge } from 'rxjs';
import { JwtService } from '@nestjs/jwt';

import { JwtAuthGuard } from '../../../../modules/auth/guards/jwt-auth.guard';
import { Public } from '../../../../modules/auth/decorators/public.decorator';
import { RandomPoolTradeService } from '../services/random-pool-trade.service';
import {
  StartRandomPoolTradeDto,
  GetTradeHistoryDto,
  ApiResponseDto,
  RandomPoolTradeStatusResponseDto,
  TradeHistoryResponseDto,
  SelectablePoolsResponseDto,
} from '../dto/random-pool-trade.dto';
import { RandomPoolTradeEvent } from '../types/random-pool-trade.types';

/**
 * 随机池子刷量控制器
 *
 * 提供随机池子刷量功能的RESTful API接口和实时事件推送
 */
@Controller('random-pool-trade')
export class RandomPoolTradeController {
  private readonly logger = new Logger(RandomPoolTradeController.name);

  constructor(
    private readonly randomPoolTradeService: RandomPoolTradeService,
    private readonly eventEmitter: EventEmitter2,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 启动随机池子刷量
   * POST /random-pool-trade/start
   */
  @Post('start')
  @UseGuards(JwtAuthGuard)
  async startRandomPoolTrade(
    @Body() startDto: StartRandomPoolTradeDto,
  ): Promise<ApiResponseDto> {
    this.logger.log('收到启动随机池子刷量请求');

    try {
      const config = {
        minBuyAmount: startDto.minBuyAmount,
        maxBuyAmount: startDto.maxBuyAmount,
        minBuyIntervalMs: startDto.minBuyIntervalMs,
        maxBuyIntervalMs: startDto.maxBuyIntervalMs,
        minSellDelayMs: startDto.minSellDelayMs,
        maxSellDelayMs: startDto.maxSellDelayMs,
        buyCount: startDto.buyCount,
        poolCooldownMs: startDto.poolCooldownMs || 300000,
      };

      const result =
        await this.randomPoolTradeService.startRandomPoolTrade(config);

      return {
        success: result.success,
        code: 200,
        message: result.message,
        data: {
          sessionId: result.sessionId,
        },
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error('启动随机池子刷量失败:', error);
      return {
        success: false,
        code: 400,
        message: error instanceof Error ? error.message : '启动失败',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 停止随机池子刷量
   * POST /random-pool-trade/stop
   */
  @Post('stop')
  @UseGuards(JwtAuthGuard)
  async stopRandomPoolTrade(): Promise<ApiResponseDto> {
    this.logger.log('收到停止随机池子刷量请求');

    try {
      const result = await this.randomPoolTradeService.stopRandomPoolTrade();

      return {
        success: result.success,
        code: 200,
        message: result.message,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error('停止随机池子刷量失败:', error);
      return {
        success: false,
        code: 400,
        message: error instanceof Error ? error.message : '停止失败',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 获取当前状态
   * GET /random-pool-trade/status
   */
  @Get('status')
  @UseGuards(JwtAuthGuard)
  getStatus(): ApiResponseDto<RandomPoolTradeStatusResponseDto | null> {
    this.logger.log('收到获取状态请求');

    try {
      const statistics = this.randomPoolTradeService.getStatistics();

      if (!statistics) {
        // 即使没有运行中的会话，也返回累积统计信息
        const cumulativeStats =
          this.randomPoolTradeService.getCumulativeStatistics();

        return {
          success: true,
          code: 200,
          message: '当前没有运行中的会话',
          data: {
            session: {
              sessionId: '',
              isRunning: false,
              startTime: 0,
              runningDuration: 0,
              config: {
                minBuyAmount: 0,
                maxBuyAmount: 0,
                buyIntervalMs: 0,
                sellDelayMs: 0,
                buyCount: 0,
                poolCooldownMs: 0,
              },
            },
            statistics: {
              operations: {
                totalBuyOperations: 0,
                totalSellOperations: 0,
                successfulBuyOperations: 0,
                successfulSellOperations: 0,
                buySuccessRate: 0,
                sellSuccessRate: 0,
              },
              pools: {
                activePoolsCount: 0,
                completedPoolsCount: 0,
                totalPoolsProcessed: 0,
              },
              volume: {
                totalBuyVolume: cumulativeStats.totalBuyVolume,
                totalSellVolume: cumulativeStats.totalSellVolume,
                totalVolume: cumulativeStats.totalVolume,
                averageBuyAmount: cumulativeStats.averageBuyAmount,
                averageSellAmount: cumulativeStats.averageSellAmount,
              },
            },
            activePools: [],
          },
          timestamp: Date.now(),
        };
      }

      // 获取活跃池子信息
      const activePools = this.randomPoolTradeService.getActivePools();

      const response: RandomPoolTradeStatusResponseDto = {
        session: {
          sessionId: statistics.session.sessionId,
          isRunning: statistics.session.isRunning,
          startTime: statistics.session.startTime,
          endTime: statistics.session.endTime,
          runningDuration: statistics.session.runningDuration,
          config: {
            minBuyAmount: 0, // 这些值需要从服务中获取
            maxBuyAmount: 0,
            buyIntervalMs: 0,
            sellDelayMs: 0,
            buyCount: 0,
            poolCooldownMs: 0,
          },
        },
        activePools: activePools.map((pool) => ({
          poolId: pool.poolId,
          symbol: pool.symbol,
          accumulatedAmount: pool.accumulatedAmount,
          completedBuyCount: pool.completedBuyCount,
          targetBuyCount: pool.targetBuyCount,
          status: pool.status,
          createdAt: pool.lastOperationTime,
          updatedAt: pool.lastOperationTime,
        })),
        statistics: {
          operations: statistics.operations,
          pools: statistics.pools,
          volume: statistics.volume,
        },
      };

      return {
        success: true,
        code: 200,
        message: '获取状态成功',
        data: response,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error('获取状态失败:', error);
      return {
        success: false,
        code: 500,
        message: error instanceof Error ? error.message : '获取状态失败',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 获取交易历史记录
   * GET /random-pool-trade/history
   */
  @Get('history')
  getTradeHistory(
    @Query() queryDto: GetTradeHistoryDto,
  ): ApiResponseDto<TradeHistoryResponseDto> {
    this.logger.log('收到获取交易历史请求', {
      sessionId: queryDto.sessionId,
      page: queryDto.page,
      pageSize: queryDto.pageSize,
    });

    try {
      const historyResult =
        this.randomPoolTradeService.getTradeHistoryWithPagination(
          queryDto.sessionId,
          {
            page: queryDto.page,
            pageSize: queryDto.pageSize,
            startTime: queryDto.startTime
              ? new Date(queryDto.startTime)
              : undefined,
            endTime: queryDto.endTime ? new Date(queryDto.endTime) : undefined,
          },
        );

      const response: TradeHistoryResponseDto = {
        records: historyResult.records.map((record) => ({
          id: record.id,
          sessionId: record.sessionId,
          poolId: record.poolId,
          symbol: record.symbol,
          tradeType: record.tradeType,
          amount: record.amount,
          timestamp: record.timestamp,
          success: record.success,
          error: record.error,
          transactionId: record.transactionId,
          trader: record.trader,
          accumulationSnapshot: record.accumulationSnapshot,
        })),
        pagination: historyResult.pagination,
      };

      return {
        success: true,
        code: 200,
        message: '获取交易历史成功',
        data: response,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error('获取交易历史失败:', error);
      return {
        success: false,
        code: 500,
        message: error instanceof Error ? error.message : '获取交易历史失败',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 获取可选择的池子列表
   * GET /random-pool-trade/pools
   */
  @Get('pools')
  async getSelectablePools(): Promise<
    ApiResponseDto<SelectablePoolsResponseDto>
  > {
    this.logger.log('收到获取可选择池子请求');

    try {
      // 获取真实的可选择池子数据
      const pools = await this.randomPoolTradeService.getSelectablePools();

      const response: SelectablePoolsResponseDto = {
        pools: pools.map((pool) => ({
          poolId: pool.poolId,
          symbol: pool.symbol,
          stableCoinSymbol: pool.stableCoinSymbol,
          price: pool.price,
          liquidity: pool.liquidity,
          volume24h: pool.volume24h,
          poolStatus: pool.poolStatus,
          isStop: pool.isStop,
          lastSelectedAt: undefined,
        })),
        total: pools.length,
        filters: {
          activeOnly: true,
          minLiquidity: 1000,
          excludeCooldown: false,
        },
      };

      return {
        success: true,
        code: 200,
        message: '获取可选择池子成功',
        data: response,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error('获取可选择池子失败:', error);
      return {
        success: false,
        code: 500,
        message: error instanceof Error ? error.message : '获取可选择池子失败',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 实时事件推送
   * SSE /random-pool-trade/events
   */
  @Public()
  @Sse('events')
  getEvents(@Query('token') token?: string): Observable<MessageEvent> {
    this.logger.log('🔥 SSE端点被调用开始');
    this.logger.log(
      '随机池子刷量SSE端点被调用，token:',
      token ? '存在' : '不存在',
    );

    // 验证token
    if (!token) {
      this.logger.warn('随机池子刷量SSE缺少token');
      throw new UnauthorizedException('缺少认证token');
    }

    try {
      this.jwtService.verify(token);
      this.logger.log('随机池子刷量SSE token验证成功');
    } catch (error) {
      this.logger.warn('随机池子刷量SSE认证失败:', error);
      throw new UnauthorizedException('无效的认证token');
    }

    this.logger.log('🎯 客户端连接到随机池子刷量事件流');

    // 创建心跳流
    const heartbeat = interval(5000).pipe(
      map((index) => {
        this.logger.debug(`💓 发送心跳 #${index}`);
        return {
          type: 'message',
          data: JSON.stringify({
            type: 'heartbeat',
            timestamp: Date.now(),
            sequence: index,
          }),
        } as MessageEvent;
      }),
    );

    // 创建事件流
    const eventStream = fromEvent<RandomPoolTradeEvent>(
      this.eventEmitter,
      'random-pool-trade.event',
    ).pipe(
      map(
        (event: RandomPoolTradeEvent) => {
          this.logger.log(`📡 发送事件: ${event.type}`);
          return {
            type: 'message',
            data: JSON.stringify(event),
          } as MessageEvent;
        },
      ),
    );

    this.logger.log('🚀 返回合并的心跳和事件流');
    // 合并心跳和事件流
    return merge(heartbeat, eventStream);
  }

  @Get('debug/statistics')
  getDebugStatistics() {
    this.logger.log('收到调试统计信息请求');
    // 临时创建一个假会话来触发统计计算
    const tempSession = {
      sessionId: 'debug-session',
      config: {},
      isRunning: false,
      startTime: Date.now(),
      activePools: new Map(),
      poolCooldowns: new Map(),
      statistics: {
        totalBuyOperations: 0,
        totalSellOperations: 0,
        successfulBuyOperations: 0,
        successfulSellOperations: 0,
        totalVolume: 0,
        activePoolsCount: 0,
      },
    };

    // 临时设置会话
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const service = this.randomPoolTradeService as any;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    service.currentSession = tempSession;
    const stats = this.randomPoolTradeService.getStatistics();
    // 清除临时会话
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    service.currentSession = null;

    return {
      success: true,
      data: stats,
      message: '调试统计信息获取成功',
    };
  }

  @Get('debug/create-test-data')
  createTestData() {
    this.logger.log('创建测试交易数据');

    // 创建一些测试交易记录
    const testRecords = [
      {
        sessionId: 'test-session-1',
        poolId: 1,
        symbol: 'BTCUSDT',
        tradeType: 'BUY' as const,
        amount: 1.5,
        success: true,
      },
      {
        sessionId: 'test-session-1',
        poolId: 2,
        symbol: 'ETHUSDT',
        tradeType: 'BUY' as const,
        amount: 2.3,
        success: true,
      },
      {
        sessionId: 'test-session-2',
        poolId: 3,
        symbol: 'ADAUSDT',
        tradeType: 'BUY' as const,
        amount: 0.8,
        success: true,
      },
    ];

    // 保存测试记录
    testRecords.forEach((record) => {
      this.randomPoolTradeService['databaseTradeRecordService'].saveTradeRecord(
        record,
      );
    });

    return {
      success: true,
      data: {
        message: '测试数据创建成功',
        recordsCreated: testRecords.length,
        totalTestVolume: testRecords.reduce((sum, r) => sum + r.amount, 0),
      },
    };
  }
}
