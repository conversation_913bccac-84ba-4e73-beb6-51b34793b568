import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../../core/database/database.service';
import { AccountDataService } from '../../../common/services/account-data.service';
import { BaseBusinessService } from '../../../core/shared/base-business.service';
import { NumberUtils } from '../../../core/shared/number.utils';

// 明确定义类型，避免循环依赖
export interface NineBalanceInfo {
  userId: number;
  apiKey: string;
  email: string;
  totalBalance: number;
  hasBalance: boolean;
}

export interface NineBalanceQueryResult {
  totalAccounts: number;
  accountsWithBalance: number;
  accountsWithoutBalance: number;
  totalNineBalance: number;
  averageBalance: number;
  balanceDetails: NineBalanceInfo[];
  executionTimeMs: number;
}

/**
 * 数据库查询结果接口
 */
interface NineBalanceDbResult {
  user_id: number;
  api_key: string;
  email: string | null;
  total_balance: number;
  has_balance: number;
}

/**
 * 查询配置常量
 */
const NINE_BALANCE_CONFIG = {
  NINE_ASSET_ID: 1,
  NINE_ASSET_NAME: 'NINE',
  DEFAULT_BATCH_SIZE: 1000,
  BALANCE_PRECISION: 6, // 余额保留小数位数
} as const;

/**
 * 通用代币配置
 */
const TOKEN_CONFIGS = {
  NINE: { ASSET_ID: 1, ASSET_NAME: 'NINE' },
  USDT: { ASSET_ID: 2, ASSET_NAME: 'USDT' },
  // 可以根据需要添加更多代币
} as const;

type SupportedToken = keyof typeof TOKEN_CONFIGS;

/**
 * NINE 代币余额服务
 *
 * 职责：
 * - 专门处理 NINE 代币余额查询和管理
 * - 提供高性能的批量余额查询功能
 * - 支持单个账户和批量账户的余额操作
 *
 * 设计原则：
 * - 单一职责：只处理 NINE 代币余额相关业务
 * - 高内聚：所有余额相关方法都在同一个服务中
 * - 低耦合：不依赖其他业务服务，只依赖数据库服务
 * - 可测试：方法职责明确，易于单元测试
 */
@Injectable()
export class NineBalanceService extends BaseBusinessService {
  constructor(
    private readonly db: DatabaseService,
    private readonly accountDataService: AccountDataService,
  ) {
    super(NineBalanceService.name);
  }

  /**
   * 查询所有账户的 NINE 代币余额信息
   * 高性能批量查询，支持 1100+ 账户记录
   *
   * @returns NINE代币余额查询结果
   */
  async queryAllNineBalances(): Promise<NineBalanceQueryResult> {
    const startTime = Date.now();
    const accounts = this.accountDataService.getAccounts();
    this.logger.log(`开始查询 NINE 代币余额，总账户数: ${accounts.length}`);

    try {
      // 1. 批量查询所有账户的 NINE 余额信息
      const balanceDetails: NineBalanceInfo[] =
        await this.batchQueryNineBalances();

      // 2. 计算统计信息
      const result: NineBalanceQueryResult = this.buildNineBalanceResult(
        balanceDetails,
        Date.now() - startTime,
      );

      this.logQueryCompletion(result);
      return result;
    } catch (error) {
      this.logger.error('NINE代币余额查询失败:', error);
      throw new Error(`NINE代币余额查询失败: ${(error as Error).message}`);
    }
  }

  /**
   * 获取指定账户的 NINE 代币余额
   * 提供单个账户的快速查询功能
   *
   * @param apiKey API密钥
   * @returns NINE余额信息，如果账户不存在则返回null
   */
  async getNineBalanceByApiKey(
    apiKey: string,
  ): Promise<NineBalanceInfo | null> {
    try {
      const query = `
        SELECT 
          uak.user_id,
          uak.api_key,
          ubi.email,
          COALESCE(acc.total_balance, 0) as total_balance,
          CASE WHEN acc.total_balance > 0 THEN 1 ELSE 0 END as has_balance,
          COALESCE(acc.active, 0) as account_active
        FROM user_api_key uak
        LEFT JOIN user_base_info ubi ON uak.user_id = ubi.id
        LEFT JOIN account acc ON uak.user_id = acc.user_id
          AND acc.asset_id = ${NINE_BALANCE_CONFIG.NINE_ASSET_ID}
        WHERE uak.api_key = ?
          AND uak.enabled = 1
        LIMIT 1
      `;

      const results = await this.db.$queryRawUnsafe<NineBalanceDbResult[]>(
        query,
        apiKey,
      );

      if (results.length === 0) {
        return null;
      }

      const formatted = this.formatNineBalanceResults(results);
      return formatted[0];
    } catch (error) {
      this.logger.error(`查询单个账户NINE余额失败 (${apiKey}):`, error);
      return null;
    }
  }

  /**
   * 获取有余额的账户列表
   * 筛选出有 NINE 代币余额的账户，用于后续操作
   *
   * @param minBalance 最小余额阈值，默认为0（大于0即可）
   * @returns 有余额的账户信息数组
   */
  async getAccountsWithNineBalance(
    minBalance: number = 0,
  ): Promise<NineBalanceInfo[]> {
    const allBalances: NineBalanceQueryResult =
      await this.queryAllNineBalances();

    return allBalances.balanceDetails.filter(
      (account: NineBalanceInfo) => account.totalBalance > minBalance,
    );
  }

  /**
   * 获取余额统计信息
   * 提供快速的统计数据，不返回详细的账户列表
   *
   * @returns 余额统计摘要
   */
  async getNineBalanceStatistics(): Promise<{
    totalAccounts: number;
    accountsWithBalance: number;
    accountsWithoutBalance: number;
    totalNineBalance: number;
    averageBalance: number;
    maxBalance: number;
    minBalance: number;
  }> {
    const result = await this.queryAllNineBalances();
    const balances = result.balanceDetails
      .map((account) => account.totalBalance)
      .filter((balance) => balance > 0);

    return {
      totalAccounts: result.totalAccounts,
      accountsWithBalance: result.accountsWithBalance,
      accountsWithoutBalance: result.accountsWithoutBalance,
      totalNineBalance: result.totalNineBalance,
      averageBalance: result.averageBalance,
      maxBalance: balances.length > 0 ? Math.max(...balances) : 0,
      minBalance: balances.length > 0 ? Math.min(...balances) : 0,
    };
  }

  /**
   * 验证账户是否有足够的 NINE 余额
   * 用于交易前的余额验证
   *
   * @param apiKey API密钥
   * @param requiredAmount 需要的最小余额
   * @returns 是否有足够余额
   */
  async hasEnoughNineBalance(
    apiKey: string,
    requiredAmount: number,
  ): Promise<boolean> {
    const balance = await this.getNineBalanceByApiKey(apiKey);
    return balance !== null && balance.totalBalance >= requiredAmount;
  }

  /**
   * 批量验证账户余额
   * 用于批量操作前的余额验证
   *
   * @param apiKeys API密钥数组
   * @param requiredAmount 需要的最小余额
   * @returns 有足够余额的账户信息
   */
  async batchValidateNineBalance(
    apiKeys: string[],
    requiredAmount: number,
  ): Promise<NineBalanceInfo[]> {
    const allBalances = await this.queryAllNineBalances();
    const targetAccounts = allBalances.balanceDetails.filter((account) =>
      apiKeys.includes(account.apiKey),
    );

    return targetAccounts.filter(
      (account) => account.totalBalance >= requiredAmount,
    );
  }

  /**
   * 通用代币余额查询
   * 支持查询不同类型的代币余额
   *
   * @param tokenSymbol 代币符号 (NINE, USDT, 等)
   * @param apiKey 可选的单个API密钥
   * @returns 代币余额信息
   */
  async getTokenBalance(
    tokenSymbol: SupportedToken,
    apiKey?: string,
  ): Promise<NineBalanceInfo[]> {
    const config = TOKEN_CONFIGS[tokenSymbol];
    if (!config) {
      throw new Error(`不支持的代币类型: ${tokenSymbol}`);
    }

    this.logger.log(
      `查询${tokenSymbol}余额: 仅使用 ASSET_ID=${config.ASSET_ID} (已移除asset字段条件)`,
    );

    const accounts = this.accountDataService.getAccounts();
    const targetApiKeys = apiKey ? [apiKey] : accounts.map((acc) => acc.apiKey);

    if (targetApiKeys.length === 0) {
      this.logger.warn(`没有找到任何账户数据`);
      return [];
    }

    this.logger.log(
      `准备查询 ${targetApiKeys.length} 个账户的${tokenSymbol}余额`,
    );

    // 分批查询以避免SQL参数过多的问题
    const batchSize = 500;
    const allResults: NineBalanceDbResult[] = [];

    for (let i = 0; i < targetApiKeys.length; i += batchSize) {
      const batchApiKeys = targetApiKeys.slice(i, i + batchSize);

      const query = `
        SELECT
          uak.user_id,
          uak.api_key,
          COALESCE(ubi.email, 'N/A') as email,
          COALESCE(acc.total_balance, 0) as total_balance,
          CASE
            WHEN acc.total_balance IS NOT NULL AND acc.total_balance > 0 THEN 1
            ELSE 0
          END as has_balance
        FROM user_api_key uak
        LEFT JOIN user_base_info ubi ON uak.user_id = ubi.id
        LEFT JOIN account acc ON uak.user_id = acc.user_id
          AND acc.asset_id = ${config.ASSET_ID}
        WHERE uak.api_key IN (${batchApiKeys.map(() => '?').join(',')})
          AND uak.enabled = 1
        ORDER BY uak.user_id
      `;

      const batchResults = await this.db.$queryRawUnsafe<NineBalanceDbResult[]>(
        query,
        ...batchApiKeys,
      );

      this.logger.debug(
        `批次 ${i / batchSize + 1}: 查询 ${batchApiKeys.length} 个账户，返回 ${batchResults.length} 条结果`,
      );

      allResults.push(...batchResults);
    }

    this.logger.log(
      `${tokenSymbol}余额查询完成: 总共查询到 ${allResults.length} 条原始结果`,
    );

    const formattedResults = this.formatNineBalanceResults(allResults);
    this.logger.log(
      `${tokenSymbol}余额格式化完成: ${formattedResults.length} 个账户，其中 ${formattedResults.filter((r) => r.hasBalance).length} 个有余额`,
    );

    return formattedResults;
  }

  /**
   * 获取USDT余额（专门用于内盘交易）
   */
  async getUsdtBalances(): Promise<NineBalanceInfo[]> {
    this.logger.log('开始获取USDT余额...');

    // 添加调试日志
    const config = TOKEN_CONFIGS['USDT'];
    this.logger.log(
      `USDT配置: 仅使用 ASSET_ID=${config.ASSET_ID} (已移除asset字段条件)`,
    );

    const result = await this.getTokenBalance('USDT');
    this.logger.log(`USDT余额查询结果: 找到 ${result.length} 个账户`);

    // 记录前几个结果用于调试
    if (result.length > 0) {
      this.logger.log(
        '前3个USDT余额结果:',
        result.slice(0, 3).map((r) => ({
          apiKey: r.apiKey.substring(0, 8) + '...',
          balance: r.totalBalance,
          hasBalance: r.hasBalance,
        })),
      );
    } else {
      this.logger.warn('没有找到任何USDT余额数据');
    }

    return result;
  }

  // ========================================
  // 私有方法 - 内部实现细节
  // ========================================

  /**
   * 批量查询 NINE 代币余额
   * 使用高性能的 JOIN 查询一次性获取所有数据
   *
   * @returns NINE余额信息数组
   */
  private async batchQueryNineBalances(): Promise<NineBalanceInfo[]> {
    const accounts = this.accountDataService.getAccounts();
    const apiKeys = accounts.map((account) => account.apiKey);

    if (apiKeys.length === 0) {
      return [];
    }

    // 分批查询以避免SQL参数过多的问题
    const batchSize = 500;
    const allResults: NineBalanceDbResult[] = [];

    for (let i = 0; i < apiKeys.length; i += batchSize) {
      const batchApiKeys = apiKeys.slice(i, i + batchSize);

      // 使用原生 SQL 进行高性能批量查询
      // 修复逻辑：确保正确处理NULL值和余额计算
      const query = `
        SELECT
          uak.user_id,
          uak.api_key,
          COALESCE(ubi.email, 'N/A') as email,
          COALESCE(acc.total_balance, 0) as total_balance,
          CASE
            WHEN acc.total_balance IS NOT NULL AND acc.total_balance > 0 THEN 1
            ELSE 0
          END as has_balance
        FROM user_api_key uak
        LEFT JOIN user_base_info ubi ON uak.user_id = ubi.id
        LEFT JOIN account acc ON uak.user_id = acc.user_id
          AND acc.asset_id = ${NINE_BALANCE_CONFIG.NINE_ASSET_ID}
        WHERE uak.api_key IN (${batchApiKeys.map(() => '?').join(',')})
          AND uak.enabled = 1
        ORDER BY uak.user_id
      `;

      const batchResults = await this.db.$queryRawUnsafe<NineBalanceDbResult[]>(
        query,
        ...batchApiKeys,
      );

      allResults.push(...batchResults);
    }

    // 处理账户文件中存在但数据库中不存在的API keys
    const dbApiKeys = new Set(allResults.map((r) => r.api_key));
    const allAccounts = this.accountDataService.getAccounts();
    const missingAccounts = allAccounts.filter(
      (acc) => !dbApiKeys.has(acc.apiKey),
    );

    if (missingAccounts.length > 0) {
      this.logger.warn(
        `发现 ${missingAccounts.length} 个账户文件中的API key在数据库中不存在`,
      );

      // 为缺失的账户添加默认记录
      // 账户文件中的账户默认余额为0
      const missingResults: NineBalanceDbResult[] = missingAccounts.map(
        (acc) => ({
          user_id: 0, // 使用0表示不存在的用户
          api_key: acc.apiKey,
          email: acc.email,
          total_balance: 0,
          has_balance: 0,
        }),
      );

      allResults.push(...missingResults);
    }

    return this.formatNineBalanceResults(allResults);
  }

  /**
   * 格式化 NINE 余额查询结果
   * 将数据库查询结果转换为服务层接口格式
   *
   * @param dbResults 数据库查询结果
   * @returns 格式化后的余额信息
   */
  private formatNineBalanceResults(
    dbResults: NineBalanceDbResult[],
  ): NineBalanceInfo[] {
    return dbResults.map((row) => {
      const totalBalance = this.roundBalance(Number(row.total_balance));
      // 修复逻辑：基于实际余额值判断是否有余额，而不是依赖数据库的计算字段
      const hasBalance = totalBalance > 0;

      return {
        userId: row.user_id,
        apiKey: row.api_key,
        email: row.email || 'N/A',
        totalBalance,
        hasBalance,
      };
    });
  }

  /**
   * 构建 NINE 余额查询结果
   * 计算统计信息并组装完整的返回数据
   *
   * @param balanceDetails 余额详情数组
   * @param executionTime 执行时间
   * @returns 完整的查询结果
   */
  private buildNineBalanceResult(
    balanceDetails: NineBalanceInfo[],
    executionTime: number,
  ): NineBalanceQueryResult {
    const accountsWithBalance = balanceDetails.filter(
      (account) => account.hasBalance,
    ).length;
    const accountsWithoutBalance = balanceDetails.length - accountsWithBalance;
    const totalNineBalance = balanceDetails.reduce(
      (sum, account) => sum + account.totalBalance,
      0,
    );
    const averageBalance =
      balanceDetails.length > 0 ? totalNineBalance / balanceDetails.length : 0;

    return {
      totalAccounts: balanceDetails.length,
      accountsWithBalance,
      accountsWithoutBalance,
      totalNineBalance: this.roundBalance(totalNineBalance),
      averageBalance: this.roundBalance(averageBalance),
      balanceDetails,
      executionTimeMs: executionTime,
    };
  }

  /**
   * 四舍五入余额到指定精度
   *
   * @param balance 原始余额
   * @returns 四舍五入后的余额
   */
  private roundBalance(balance: number): number {
    return NumberUtils.formatBalance(
      balance,
      NINE_BALANCE_CONFIG.BALANCE_PRECISION,
    );
  }

  /**
   * 记录查询完成日志
   * 统一的日志格式，便于监控和调试
   *
   * @param result 查询结果
   */
  private logQueryCompletion(result: NineBalanceQueryResult): void {
    this.logger.log(
      `NINE余额查询完成: 总账户${result.totalAccounts}, 有余额${result.accountsWithBalance}, 无余额${result.accountsWithoutBalance}, 总余额${result.totalNineBalance}, 平均余额${result.averageBalance}, 耗时${result.executionTimeMs}ms`,
    );
  }
}
