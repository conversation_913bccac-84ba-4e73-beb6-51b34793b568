{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@prisma/client": "^6.10.1", "@types/uuid": "^10.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "mysql2": "^3.14.1", "p-limit": "^6.2.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prisma": "^6.10.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sql.js": "^1.13.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/jest": "^30.0.0", "@types/node": "^22.15.34", "@types/passport-jwt": "^4.0.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "transformIgnorePatterns": ["node_modules/(?!(p-limit|yocto-queue)/)"]}}