# 多阶段构建 - 构建阶段
FROM node:20-alpine AS builder

# 配置 npm 镜像源（解决网络问题）
RUN npm config set registry https://registry.npmmirror.com/

# 设置工作目录
WORKDIR /app

# 复制 package 文件（优化缓存层）
COPY package*.json yarn.lock ./
COPY prisma ./prisma/

# 安装所有依赖（包括 devDependencies，构建时需要）
RUN yarn install --frozen-lockfile && yarn cache clean

# 复制源代码（放在依赖安装之后，优化缓存）
COPY src ./src/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# 生成 Prisma 客户端
RUN yarn prisma generate

# 构建应用
RUN yarn build

# 依赖阶段 - 只安装生产依赖
FROM node:20-alpine AS deps

# 配置 npm 镜像源（解决网络问题）
RUN npm config set registry https://registry.npmmirror.com/

# 不再需要构建工具（sql.js是纯JavaScript）

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json yarn.lock ./

# 安装生产依赖
RUN yarn install --production --frozen-lockfile && yarn cache clean

# 无需清理构建工具（因为没有安装）

# 生产阶段
FROM node:20-alpine AS production

# 安装运行时必要工具（移除 sqlite，因为使用 better-sqlite3）
RUN apk add --no-cache curl

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 设置工作目录
WORKDIR /app

# 从依赖阶段复制生产依赖
COPY --from=deps --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=deps --chown=nestjs:nodejs /app/package*.json ./
COPY --from=deps --chown=nestjs:nodejs /app/yarn.lock ./

# 从构建阶段复制构建产物（放在依赖复制之后，避免被覆盖）
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder --chown=nestjs:nodejs /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder --chown=nestjs:nodejs /app/prisma ./prisma

# 复制数据文件（accounts.json等）
COPY --chown=nestjs:nodejs assets ./assets

# 不再需要启动脚本，直接使用 node 命令

# 创建日志和数据目录
RUN mkdir -p /app/logs /app/data/persistence && \
    chown -R nestjs:nodejs /app/logs /app/data

# 切换到非 root 用户
USER nestjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/auth/health || exit 1

# 启动命令
CMD ["node", "dist/main.js"]
