#!/bin/bash

# 获取认证token
echo "=== 登录获取Token ==="
TOKEN=$(curl --noproxy localhost -s -X POST "http://localhost:3001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "VXR&y3QX&by75w"}' | jq -r '.data.access_token')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
  echo "❌ 登录失败"
  exit 1
fi

echo "✅ 登录成功"
echo ""

# 测试用户API
echo "=== 测试用户相关API ==="
echo "1. 用户数量:"
curl --noproxy localhost -s "http://localhost:3001/api/users/count" \
  -H "Authorization: Bearer $TOKEN" | jq '.data'

echo -e "\n2. 用户统计:"
curl --noproxy localhost -s "http://localhost:3001/api/users/statistics" \
  -H "Authorization: Bearer $TOKEN" | jq '.data.data'

echo -e "\n3. 带余额的账户数量:"
curl --noproxy localhost -s "http://localhost:3001/api/users/accounts-with-balance" \
  -H "Authorization: Bearer $TOKEN" | jq '.data | length'

# 测试内盘API
echo -e "\n\n=== 测试内盘相关API ==="
echo "1. 内盘健康检查:"
curl --noproxy localhost -s "http://localhost:3001/api/inner-disk/health" \
  -H "Authorization: Bearer $TOKEN" | jq '.data'

echo -e "\n2. 内盘统计信息:"
curl --noproxy localhost -s "http://localhost:3001/api/inner-disk/statistics" \
  -H "Authorization: Bearer $TOKEN" | jq '.data'

echo -e "\n3. 活跃池子数量:"
curl --noproxy localhost -s "http://localhost:3001/api/inner-disk/active" \
  -H "Authorization: Bearer $TOKEN" | jq '.data | length'

# 测试随机池交易API
echo -e "\n\n=== 测试随机池交易API ==="
echo "1. 交易状态:"
curl --noproxy localhost -s "http://localhost:3001/api/random-pool-trade/status" \
  -H "Authorization: Bearer $TOKEN" | jq '.data'

echo -e "\n2. 交易历史总数:"
curl --noproxy localhost -s "http://localhost:3001/api/random-pool-trade/history?limit=1" \
  -H "Authorization: Bearer $TOKEN" | jq '.data.total'

echo -e "\n3. 可选池子数量:"
curl --noproxy localhost -s "http://localhost:3001/api/random-pool-trade/pools" \
  -H "Authorization: Bearer $TOKEN" | jq '.data | length'

# 测试批处理API
echo -e "\n\n=== 测试批处理API ==="
echo "1. 批处理历史:"
curl --noproxy localhost -s "http://localhost:3001/api/batch/history" \
  -H "Authorization: Bearer $TOKEN" | jq '{success: .success, records: (.data | length)}'

echo -e "\n✅ API测试完成"