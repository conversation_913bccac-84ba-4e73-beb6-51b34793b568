# 交易状态持久化系统

## 概述

由于使用外部只读数据库，我们实现了基于JSON文件的持久化存储系统，确保内盘打新和随机池子交易的状态在服务重启后能够恢复。

## 目录结构

```
data/persistence/
├── README.md                           # 本文档
├── user_1_random_pool_trade.json      # 用户1的随机池子交易状态
├── user_1_inner_disk_new_coin.json    # 用户1的内盘打新状态
├── user_2_random_pool_trade.json      # 用户2的随机池子交易状态
└── ...                                 # 其他用户数据
```

## 数据文件格式

### 随机池子交易状态文件

```json
{
  "session": {
    "sessionId": "session-uuid",
    "config": { /* 交易配置 */ },
    "isRunning": false,
    "startTime": 1640995200000,
    "endTime": 1640998800000,
    "activePools": [
      [123, { /* 池子状态 */ }],
      [456, { /* 池子状态 */ }]
    ],
    "poolCooldowns": [
      [123, 1640995800000],
      [456, 1640996400000]
    ],
    "statistics": {
      "totalBuyOperations": 10,
      "totalSellOperations": 8,
      "successfulBuyOperations": 9,
      "successfulSellOperations": 7,
      "totalVolume": 1000.5,
      "activePoolsCount": 2
    }
  },
  "tradeHistory": [
    {
      "id": "trade-uuid",
      "sessionId": "session-uuid",
      "poolId": 123,
      "symbol": "TOKEN",
      "tradeType": "BUY",
      "amount": 100,
      "timestamp": 1640995500000,
      "success": true,
      "transactionId": "tx-123",
      "accumulationSnapshot": {
        "accumulatedAmount": 100,
        "completedBuyCount": 1,
        "targetBuyCount": 5
      }
    }
  ],
  "lastSaved": 1640995800000
}
```

### 内盘打新状态文件

```json
{
  "config": {
    "id": 1,
    "userId": 1,
    "isEnabled": true,
    "apiKey": "api-key",
    "secret": "secret",
    "purchaseAmount": 100,
    "purchaseType": "FIXED",
    "profitRate": 0.1,
    "monitorInterval": 5000,
    "maxPositions": 5,
    "riskLevel": "MEDIUM"
  },
  "sessionId": "session-uuid",
  "isRunning": false,
  "startTime": 1640995200000,
  "lastKnownPools": [123, 456, 789],
  "detectionHistory": [
    {
      "poolId": 123,
      "tokenSymbol": "NEWTOKEN",
      "tokenName": "New Token",
      "stableCoinSymbol": "USDT",
      "initialPrice": 0.001,
      "marketCap": 1000000,
      "detectedAt": "2024-01-01T00:00:00.000Z",
      "isTraded": false,
      "tradeReason": "市值过小"
    }
  ],
  "monitoringStats": {
    "totalDetected": 5,
    "totalTrades": 2,
    "successfulTrades": 1,
    "currentPositions": 1,
    "totalProfit": 50.5,
    "errorCount": 0,
    "lastDetection": 1640995500000,
    "lastTrade": 1640995600000
  },
  "lastSaved": 1640995800000
}
```

## 核心服务

### PersistenceService

基础持久化服务，提供文件操作功能：

- `saveData()` - 保存数据到JSON文件
- `loadData()` - 从JSON文件加载数据
- `atomicSave()` - 原子性保存（先备份再保存）
- `safeLoad()` - 安全加载（损坏时从备份恢复）
- `deleteData()` - 删除数据文件
- `backupData()` - 备份数据文件
- `cleanupBackups()` - 清理旧备份

### RandomPoolTradePersistenceService

随机池子交易持久化服务：

- `saveSessionState()` - 保存交易会话状态
- `loadSessionState()` - 加载交易会话状态
- `appendTradeRecord()` - 追加交易记录
- `getTradeHistory()` - 获取交易历史
- `cleanupExpiredSessions()` - 清理过期会话

### InnerDiskNewCoinPersistenceService

内盘打新持久化服务：

- `saveMonitoringState()` - 保存监控状态
- `loadMonitoringState()` - 加载监控状态
- `appendDetectionRecord()` - 追加检测记录
- `updateMonitoringStats()` - 更新监控统计
- `getDetectionHistory()` - 获取检测历史

## 使用指南

### 1. 服务启动时恢复状态

```typescript
// 在服务启动时
const savedState = await this.persistenceService.loadSessionState(userId);
if (savedState) {
  // 恢复内存状态
  this.currentSession = savedState.session;
  this.tradeHistory = savedState.tradeHistory;
  
  // 检查是否需要继续之前的会话
  if (savedState.session.isRunning) {
    // 询问用户或自动恢复
  }
}
```

### 2. 运行时保存状态

```typescript
// 在关键状态变更后
const currentData = {
  session: this.currentSession,
  poolStates: this.currentSession.activePools,
  poolCooldowns: this.currentSession.poolCooldowns,
  tradeHistory: this.tradeHistory,
  lastSaved: Date.now(),
};
await this.persistenceService.saveSessionState(userId, currentData);
```

### 3. 实时记录追加

```typescript
// 交易完成后
await this.persistenceService.appendTradeRecord(userId, tradeRecord);

// 检测到新币后
await this.persistenceService.appendDetectionRecord(userId, detection);
```

## 数据安全

### 备份机制

- 每次保存前自动创建备份
- 保留最近5个备份文件
- 自动清理过期备份

### 恢复机制

- 主文件损坏时自动从最新备份恢复
- 支持手动指定备份文件恢复
- 提供数据完整性检查

### 清理策略

- 随机池子交易：7天后清理停止的会话
- 内盘打新：30天后清理停止的监控
- 备份文件：保留最近5个版本

## 性能考虑

### 写入优化

- 使用原子性写入避免数据损坏
- 批量操作减少I/O频率
- 异步操作不阻塞主业务

### 存储优化

- JSON格式便于调试和手动修改
- 压缩历史记录避免文件过大
- 定期清理过期数据

### 监控指标

- 文件大小监控
- 写入频率统计
- 错误率监控
- 恢复成功率

## 故障处理

### 常见问题

1. **文件损坏**：自动从备份恢复
2. **磁盘空间不足**：清理过期数据
3. **权限问题**：检查目录权限
4. **并发写入**：使用文件锁机制

### 手动恢复

```bash
# 查看备份文件
ls data/persistence/*.backup.*.json

# 手动恢复（将备份文件重命名为主文件）
cp user_1_random_pool_trade.backup.1640995800000.json user_1_random_pool_trade.json
```

## 维护命令

```typescript
// 清理过期数据
await persistenceService.cleanupExpiredSessions();
await persistenceService.cleanupExpiredMonitoring();

// 获取统计信息
const stats = await persistenceService.getActiveSessionsStats();

// 手动备份
await persistenceService.backupData('user_1_random_pool_trade');
```

## 注意事项

1. **定期备份**：重要数据建议额外备份到其他位置
2. **磁盘监控**：监控磁盘空间使用情况
3. **权限管理**：确保应用有足够的文件读写权限
4. **并发安全**：避免多个进程同时写入同一文件
5. **数据验证**：加载数据时进行格式验证
