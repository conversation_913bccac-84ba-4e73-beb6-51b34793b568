import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright 配置文件
 * 用于测试 Nine Light 应用的端到端功能
 */
export default defineConfig({
  // 测试目录
  testDir: './tests/e2e',
  
  // 并行运行测试
  fullyParallel: true,
  
  // 禁止在CI中运行时使用workers
  forbidOnly: !!process.env.CI,
  
  // 重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行worker数量
  workers: process.env.CI ? 1 : undefined,
  
  // 报告配置
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  
  // 全局测试配置
  use: {
    // 基础URL
    baseURL: 'http://localhost:3000',
    
    // 跟踪配置
    trace: 'on-first-retry',
    
    // 截图配置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 请求超时
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },

  // 项目配置
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    // 移动端测试
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  // 本地开发服务器配置
  webServer: [
    {
      // 后端服务
      command: 'docker compose -f docker-compose.local.yml up',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 120000,
    },
    {
      // 前端服务
      command: 'echo "Frontend should be running via Docker"',
      port: 3000,
      reuseExistingServer: !process.env.CI,
    }
  ],
  
  // 输出目录
  outputDir: 'test-results/',
  
  // 全局设置
  globalSetup: './tests/global-setup.ts',
  globalTeardown: './tests/global-teardown.ts',
});