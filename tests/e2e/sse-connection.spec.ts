import { test, expect } from '@playwright/test';

/**
 * SSE 连接功能专项测试
 * 验证我们修复的实时连接功能
 */

const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:3001/api';

test.describe('SSE 实时连接测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 监听控制台消息以捕获实时数据
    page.on('console', msg => {
      if (msg.text().includes('heartbeat') || msg.text().includes('SSE')) {
        console.log('页面接收到:', msg.text());
      }
    });
    
    // 导航到页面
    await page.goto(`${BASE_URL}/random-pool-trade`);
    await page.waitForLoadState('networkidle');
  });

  test('应该建立 SSE 连接并接收心跳', async ({ page }) => {
    // 等待页面加载
    await page.waitForTimeout(3000);
    
    // 监听网络请求，查找 SSE 连接
    const sseRequests: any[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/random-pool-trade/events')) {
        sseRequests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
        console.log('检测到 SSE 请求:', request.url());
      }
    });

    // 等待足够的时间让 SSE 连接建立
    await page.waitForTimeout(8000);
    
    // 验证是否有 SSE 请求
    expect(sseRequests.length).toBeGreaterThan(0);
    console.log(`捕获到 ${sseRequests.length} 个 SSE 请求`);
  });

  test('后端健康检查应该正常', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/auth/health`);
    expect(response.ok()).toBeTruthy();
    console.log('✅ 后端健康检查通过');
  });

  test('应该能获取交易状态', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/random-pool-trade/status`);
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBeTruthy();
    expect(data.data).toHaveProperty('session');
    expect(data.data).toHaveProperty('statistics');
    
    console.log('✅ 交易状态获取成功');
  });

  test('应该能创建和查询测试数据', async ({ request }) => {
    // 创建测试数据
    const createResponse = await request.get(`${API_BASE_URL}/random-pool-trade/debug/create-test-data`);
    expect(createResponse.ok()).toBeTruthy();
    
    const createData = await createResponse.json();
    expect(createData.success).toBeTruthy();
    console.log('✅ 测试数据创建成功');
    
    // 查询测试数据
    const queryResponse = await request.get(
      `${API_BASE_URL}/random-pool-trade/history?sessionId=test-session-1&page=1&pageSize=10`
    );
    expect(queryResponse.ok()).toBeTruthy();
    
    const queryData = await queryResponse.json();
    expect(queryData.success).toBeTruthy();
    expect(queryData.data.records.length).toBeGreaterThan(0);
    
    console.log('✅ 测试数据查询成功');
    console.log(`查询到 ${queryData.data.records.length} 条交易记录`);
  });

  test('应该能获取可选择的池子列表', async ({ request }) => {
    const response = await request.get(`${API_BASE_URL}/random-pool-trade/pools`);
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.success).toBeTruthy();
    expect(data.data).toHaveProperty('pools');
    expect(data.data).toHaveProperty('total');
    
    console.log('✅ 池子列表获取成功');
    console.log(`可用池子数量: ${data.data.total}`);
  });
});