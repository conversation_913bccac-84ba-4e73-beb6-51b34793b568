import { chromium, FullConfig } from '@playwright/test';

/**
 * 全局测试设置
 * 在所有测试运行前执行
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 开始全局测试设置...');
  
  // 检查服务是否正在运行
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // 等待后端服务启动
    console.log('⏳ 等待后端服务启动...');
    await page.goto('http://localhost:3001/api/auth/health', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    console.log('✅ 后端服务已启动');
    
    // 等待前端服务启动
    console.log('⏳ 等待前端服务启动...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    console.log('✅ 前端服务已启动');
    
    // 清理测试数据（如果需要）
    console.log('🧹 清理旧的测试数据...');
    
    // 创建基础测试数据
    console.log('📊 创建基础测试数据...');
    const response = await page.request.get('http://localhost:3001/api/random-pool-trade/debug/create-test-data');
    if (response.ok()) {
      console.log('✅ 基础测试数据创建成功');
    } else {
      console.warn('⚠️  基础测试数据创建失败，继续执行测试');
    }
    
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ 全局测试设置完成');
}

export default globalSetup;