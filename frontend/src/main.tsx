import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// 添加全局错误处理
window.addEventListener('error', (event) => {
  // 忽略 Service Worker 相关的错误
  if (event.error && event.error.message && 
      (event.error.message.includes('chrome-extension') || 
       event.error.message.includes('Service Worker'))) {
    console.warn('忽略 Service Worker 相关错误:', event.error.message);
    event.preventDefault();
    return;
  }
});

// 处理 Promise 拒绝错误
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && 
      (event.reason.message.includes('chrome-extension') || 
       event.reason.message.includes('Service Worker'))) {
    console.warn('忽略 Service Worker 相关的 Promise 错误:', event.reason.message);
    event.preventDefault();
    return;
  }
});

// 清理可能存在的 Service Worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(const registration of registrations) {
      registration.unregister().then(function(success) {
        console.log('Service Worker unregistered successfully:', success);
      }).catch(function(error) {
        console.log('Service Worker unregistration failed:', error);
      });
    }
  }).catch(function(error) {
    console.log('Error getting service worker registrations:', error);
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
