import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,

  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Descriptions,
  Tooltip,
  message,
  DatePicker,
  Select,
  Spin,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  CalendarOutlined,

} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { BatchService } from '../../services/batchService';
import { EnhancedBatchLightService } from '../../services/enhancedBatchLightService';
import type { HistoryRecord } from '../../services/batchService';
import type {
  EnhancedBatchLightResult,
  BatchLightExecutionHistoryDto
} from '../../services/enhancedBatchLightService';
import { BatchIdUtils, TimeUtils } from '../../utils/commonUtils';

const { Text, Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 批次记录详情接口
interface BatchExecutionDetails extends Record<string, unknown> {
  isBatchExecution?: boolean;
  isBatchSummary?: boolean;
  accountEmail?: string;
  accountApiKey?: string;
  assetSymbol?: string;
  assetName?: string;
  amount?: number;
  lightNumBefore?: number;
  lightNumAfter?: number;
  duration?: number;
  batchIndex?: number;
  status?: string;
  message?: string;
  overallStatus?: string;
  batches?: Array<{
    batchIndex: number;
    batchSize: number;
    status: string;
    startTime?: Date | string;
    endTime?: Date | string;
    successCount: number;
    failedCount: number;
    results: Array<{
      apiKey: string;
      status: string;
      message: string;
      amount?: number;
      recordId?: number;
    }>;
  }>;
  endTime?: string;
  executionHistory?: BatchLightExecutionHistoryDto[];
  totalAccountsInput?: number;
}

interface BatchRecordDetail extends HistoryRecord {
  batchId?: string;
  assetId?: number;
  tokenSymbol?: string;
  firstLightAccounts?: number;
  alreadyLightedAccounts?: number;
  batchConfig?: {
    batchSize: number;
    intervalMs: number;
  };
  details?: BatchExecutionDetails;
}

/**
 * 批量点亮历史记录管理组件
 * 提供详细的点亮成功记录展示界面，包含批次ID、执行时间、成功数量、失败数量等关键信息
 * 支持分页显示、搜索功能和记录详情查看
 */
const BatchHistorySection: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<BatchRecordDetail[]>([]);
  const [filteredData, setFilteredData] = useState<BatchRecordDetail[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedRecord, setSelectedRecord] = useState<BatchRecordDetail | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [statistics, setStatistics] = useState({
    totalRecords: 0,
    totalSuccess: 0,
    totalFailed: 0,
    totalAccounts: 0,
  });

  // 加载历史记录数据
  const loadHistoryData = async () => {
    setLoading(true);
    try {
      // 并行获取所有类型的历史记录
      const [historyResponse, activeBatches, batchExecutionHistory] = await Promise.all([
        BatchService.getHistory(),
        EnhancedBatchLightService.getAllActiveBatches(),
        EnhancedBatchLightService.getBatchExecutionHistory(100), // 获取最近100条执行历史
      ]);

      // 将执行历史按批次ID分组（只包含真正的批次ID）
      const batchGroups = new Map<string, BatchLightExecutionHistoryDto[]>();
      batchExecutionHistory.forEach((execution: BatchLightExecutionHistoryDto) => {
        const batchId = execution.batchId;
        // 更严格的过滤：只处理格式为"batch_数字_字符串"的批次ID
        if (batchId && /^batch_\d+_[a-z0-9]+$/i.test(batchId)) {
          if (!batchGroups.has(batchId)) {
            batchGroups.set(batchId, []);
          }
          batchGroups.get(batchId)!.push(execution);
        }
      });

      // 合并数据 - 每个批次只显示一条记录
      const combinedData: BatchRecordDetail[] = [
        // 增强批量点亮记录（批次级别汇总）
        ...activeBatches
          .filter((batch: EnhancedBatchLightResult) => {
            // 只显示已开始处理的批次（有batches数据或已完成的批次）
            return batch.batches && batch.batches.length > 0 && 
                   (batch.overallStatus === 'completed' || batch.overallStatus === 'failed' || batch.overallStatus === 'processing');
          })
          .map((batch: EnhancedBatchLightResult) => {
            // 计算实际处理的账户数（从batches中统计）
            const actualAccountCount = batch.batches.reduce((total, batchItem) => total + batchItem.batchSize, 0);
            
            return {
              id: batch.batchId,
              type: 'trade' as const,
              timestamp: batch.startTime || new Date().toISOString(),
              accountCount: actualAccountCount, // 使用实际处理的账户数
              successCount: batch.summary?.totalSuccess || 0,
              failedCount: batch.summary?.totalFailed || 0,
              batchId: batch.batchId,
              firstLightAccounts: batch.firstLightAccounts,
              alreadyLightedAccounts: batch.alreadyLightedAccounts,
              details: {
                overallStatus: batch.overallStatus,
                batches: batch.batches,
                endTime: batch.endTime,
                isBatchSummary: true, // 标记为批次汇总
                executionHistory: batchGroups.get(batch.batchId) || [], // 包含执行历史用于详情显示
                totalAccountsInput: batch.totalAccounts, // 保留原始输入的总账户数用于显示
              },
            };
          }),
        // 分组后的批量点亮记录（每个批次一条记录）
        // 只处理已完成的批次（不在活跃批次中的）
        ...[...batchGroups.entries()]
          .filter(([batchId, executions]) => {
            // 过滤条件：
            // 1. 不在活跃批次中（避免重复）
            // 2. 是真正的批次ID（格式为batch_数字_字符串）
            // 3. 有执行记录
            const existsInActiveBatches = activeBatches.some(batch => batch.batchId === batchId);
            return !existsInActiveBatches && 
                   /^batch_\d+_[a-z0-9]+$/i.test(batchId) && 
                   executions.length > 0;
          })
          .map(([batchId, executions]) => {
            const firstExecution = executions[0];
            const successCount = executions.filter(e => e.status === 'success').length;
            const failedCount = executions.length - successCount;
            
            return {
              id: batchId,
              type: 'trade' as const,
              timestamp: firstExecution.timestamp,
              accountCount: executions.length,
              successCount,
              failedCount,
              batchId,
              details: {
                isBatchSummary: true, // 标记为批次汇总
                executionHistory: executions, // 包含所有执行详情
                assetSymbol: firstExecution.assetSymbol,
                assetName: firstExecution.assetName,
              },
            };
          }),
        // 传统批量操作记录（过滤掉非批次ID的记录）
        ...(historyResponse.success ? 
          historyResponse.data.filter((record: HistoryRecord) => {
            // 只包含真正的批次ID格式
            const recordId = (record as BatchRecordDetail).batchId || record.id;
            return recordId && /^batch_\d+_[a-z0-9]+$/i.test(recordId);
          }) : []),
      ];

      setHistoryData(combinedData);
      setFilteredData(combinedData);
      
      // 计算统计信息
      const stats = {
        totalRecords: combinedData.length,
        totalSuccess: combinedData.reduce((sum, record) => sum + record.successCount, 0),
        totalFailed: combinedData.reduce((sum, record) => sum + record.failedCount, 0),
        totalAccounts: combinedData.reduce((sum, record) => sum + record.accountCount, 0),
      };
      setStatistics(stats);
      
    } catch (error) {
      console.error('加载历史记录失败:', error);
      message.error('加载历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据和定时刷新
  useEffect(() => {
    loadHistoryData();
    
    // 设置定时刷新（每30秒刷新一次）
    const interval = setInterval(() => {
      loadHistoryData();
    }, 30000);
    
    // 监听页面可见性变化，当页面重新可见时刷新数据
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadHistoryData();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 搜索和筛选逻辑
  useEffect(() => {
    let filtered = [...historyData];

    // 文本搜索
    if (searchText) {
      filtered = filtered.filter(record =>
        record.id.toLowerCase().includes(searchText.toLowerCase()) ||
        record.batchId?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(record => {
        if (statusFilter === 'success') {
          return record.successCount > 0 && record.failedCount === 0;
        } else if (statusFilter === 'failed') {
          return record.failedCount > 0;
        } else if (statusFilter === 'partial') {
          return record.successCount > 0 && record.failedCount > 0;
        }
        return true;
      });
    }

    // 日期范围筛选
    if (dateRange) {
      const [start, end] = dateRange;
      filtered = filtered.filter(record => {
        const recordDate = TimeUtils.toUTC8Date(record.timestamp);
        const startOfDay = TimeUtils.toUTC8Date(start.startOf('day').toISOString());
        const endOfDay = TimeUtils.toUTC8Date(end.endOf('day').toISOString());
        return recordDate >= startOfDay && recordDate <= endOfDay;
      });
    }

    setFilteredData(filtered);
  }, [historyData, searchText, statusFilter, dateRange]);

  // 批次ID显示组件
  const BatchIdDisplay: React.FC<{ batchId: string }> = ({ batchId }) => {
    const displayId = BatchIdUtils.formatDisplay(batchId);

    return (
      <Tooltip title={`完整ID: ${batchId}`}>
        <Text
          copyable={{ text: batchId }}
          style={{ fontFamily: 'monospace', fontSize: '12px' }}
        >
          {displayId}
        </Text>
      </Tooltip>
    );
  };

  // 状态标签组件
  const StatusTag: React.FC<{ record: BatchRecordDetail }> = ({ record }) => {
    if (record.successCount > 0 && record.failedCount === 0) {
      return <Tag color="green">全部成功</Tag>;
    } else if (record.failedCount > 0 && record.successCount === 0) {
      return <Tag color="red">全部失败</Tag>;
    } else if (record.successCount > 0 && record.failedCount > 0) {
      return <Tag color="orange">部分成功</Tag>;
    } else {
      return <Tag color="default">处理中</Tag>;
    }
  };

  // 表格列定义
  const columns: ColumnsType<BatchRecordDetail> = [
    {
      title: '批次ID',
      dataIndex: 'batchId',
      key: 'batchId',
      width: 120,
      render: (batchId: string, record) => (
        <BatchIdDisplay batchId={batchId || record.id} />
      ),
      responsive: ['sm'],
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string, record) => {
        // 判断是否为批量执行详情
        if (record.details?.isBatchExecution) {
          return <Tag color="purple">批量执行</Tag>;
        }
        // 判断是否为批次汇总
        if (record.details?.isBatchSummary) {
          return <Tag color="blue">批次汇总</Tag>;
        }
        // 传统类型
        return (
          <Tag color={type === 'trade' ? 'cyan' : 'green'}>
            {type === 'trade' ? '传统点亮' : '登录'}
          </Tag>
        );
      },
      responsive: ['md'],
    },
    {
      title: '执行时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp: string) => (
        <div>
          <div>{TimeUtils.formatUTC8Date(timestamp)}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {TimeUtils.formatUTC8Time(timestamp)}
          </Text>
        </div>
      ),
      sorter: (a, b) => TimeUtils.getUTC8Unix(a.timestamp) - TimeUtils.getUTC8Unix(b.timestamp),
      defaultSortOrder: 'descend',
    },
    {
      title: '账户数',
      dataIndex: 'accountCount',
      key: 'accountCount',
      width: 80,
      align: 'center',
      sorter: (a, b) => a.accountCount - b.accountCount,
    },
    {
      title: '成功',
      dataIndex: 'successCount',
      key: 'successCount',
      width: 70,
      align: 'center',
      render: (count: number) => (
        <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>{count}</Text>
      ),
      sorter: (a, b) => a.successCount - b.successCount,
    },
    {
      title: '失败',
      dataIndex: 'failedCount',
      key: 'failedCount',
      width: 70,
      align: 'center',
      render: (count: number) => (
        <Text style={{ color: count > 0 ? '#ff4d4f' : '#d9d9d9', fontWeight: 'bold' }}>
          {count}
        </Text>
      ),
      sorter: (a, b) => a.failedCount - b.failedCount,
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: (_, record) => <StatusTag record={record} />,
      filters: [
        { text: '全部成功', value: 'success' },
        { text: '部分成功', value: 'partial' },
        { text: '全部失败', value: 'failed' },
      ],
      onFilter: (value, record) => {
        if (value === 'success') return record.successCount > 0 && record.failedCount === 0;
        if (value === 'partial') return record.successCount > 0 && record.failedCount > 0;
        if (value === 'failed') return record.failedCount > 0 && record.successCount === 0;
        return true;
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => {
            setSelectedRecord(record);
            setDetailModalVisible(true);
          }}
          size="small"
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <Card title="批量点亮历史记录" style={{ minHeight: '600px' }}>
      {/* 统计信息 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Statistic
            title="总记录数"
            value={statistics.totalRecords}
            prefix={<CalendarOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="总成功数"
            value={statistics.totalSuccess}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="总失败数"
            value={statistics.totalFailed}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="总账户数"
            value={statistics.totalAccounts}
            valueStyle={{ color: '#722ed1' }}
          />
        </Col>
      </Row>

      {/* 搜索和筛选工具栏 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Input
            placeholder="搜索批次ID..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </Col>
        <Col xs={24} sm={6}>
          <Select
            placeholder="状态筛选"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: '100%' }}
          >
            <Option value="all">全部状态</Option>
            <Option value="success">全部成功</Option>
            <Option value="partial">部分成功</Option>
            <Option value="failed">全部失败</Option>
          </Select>
        </Col>
        <Col xs={24} sm={8}>
          <RangePicker
            value={dateRange}
            onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Col>
        <Col xs={24} sm={2}>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadHistoryData}
            loading={loading}
            title="刷新数据"
          />
        </Col>
      </Row>

      {/* 历史记录表格 */}
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
          size="middle"
        />
      </Spin>

      {/* 记录详情模态框 */}
      <Modal
        title="批次执行详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={1000}
        style={{ top: 20 }}
        styles={{ body: { maxHeight: '70vh', overflow: 'auto' } }}
      >
        {selectedRecord && (
          <div>
            <Descriptions bordered column={2} size="small">
              <Descriptions.Item label="批次ID" span={2}>
                <BatchIdDisplay batchId={selectedRecord.batchId || selectedRecord.id} />
              </Descriptions.Item>
              <Descriptions.Item label="执行时间">
                {TimeUtils.formatUTC8DateTime(selectedRecord.timestamp)}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                {selectedRecord.details?.isBatchSummary ? (
                  <Tag color="blue">批次汇总</Tag>
                ) : (
                  <Tag color={selectedRecord.type === 'trade' ? 'cyan' : 'green'}>
                    {selectedRecord.type === 'trade' ? '传统批量点亮' : '批量登录'}
                  </Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="实际处理账户数">
                {selectedRecord.accountCount}
              </Descriptions.Item>
              {selectedRecord.details?.totalAccountsInput && selectedRecord.details.totalAccountsInput !== selectedRecord.accountCount && (
                <Descriptions.Item label="输入总账户数">
                  {selectedRecord.details.totalAccountsInput}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="执行状态">
                <StatusTag record={selectedRecord} />
              </Descriptions.Item>
              <Descriptions.Item label="成功数量">
                <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  {selectedRecord.successCount}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="失败数量">
                <Text style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  {selectedRecord.failedCount}
                </Text>
              </Descriptions.Item>
              {selectedRecord.details?.assetSymbol && (
                <Descriptions.Item label="资产信息" span={2}>
                  {selectedRecord.details.assetSymbol} ({selectedRecord.details.assetName})
                </Descriptions.Item>
              )}
            </Descriptions>

            {/* 账户执行详情列表 */}
            {selectedRecord.details?.executionHistory && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>账户执行详情 ({(selectedRecord.details.executionHistory as BatchLightExecutionHistoryDto[])?.length || 0}个账户)</Title>
                <Table
                  size="small"
                  dataSource={selectedRecord.details.executionHistory as BatchLightExecutionHistoryDto[] || []}
                  rowKey="id"
                  pagination={{ pageSize: 10, size: 'small' }}
                  scroll={{ x: 800 }}
                  columns={[
                    {
                      title: '账户邮箱',
                      dataIndex: 'accountEmail',
                      key: 'accountEmail',
                      width: 200,
                      ellipsis: true,
                      render: (email: string) => email || '-',
                    },
                    {
                      title: 'API Key',
                      dataIndex: 'accountApiKey',
                      key: 'accountApiKey',
                      width: 120,
                      render: (text: string) => (
                        <Text style={{ fontFamily: 'monospace', fontSize: '12px' }}>
                          {text}
                        </Text>
                      ),
                    },
                    {
                      title: '数量',
                      dataIndex: 'amount',
                      key: 'amount',
                      width: 80,
                      align: 'center',
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      width: 80,
                      align: 'center',
                      render: (status: string) => (
                        <Tag color={status === 'success' ? 'green' : 'red'}>
                          {status === 'success' ? '成功' : '失败'}
                        </Tag>
                      ),
                    },
                    {
                      title: '点亮数变化',
                      key: 'lightChange',
                      width: 120,
                      align: 'center',
                      render: (record: BatchLightExecutionHistoryDto) => (
                        <Text style={{ fontSize: '12px' }}>
                          {record.lightNumBefore || 0} → {record.lightNumAfter || '失败'}
                        </Text>
                      ),
                    },
                    {
                      title: '耗时(ms)',
                      dataIndex: 'duration',
                      key: 'duration',
                      width: 80,
                      align: 'center',
                      render: (duration: number) => duration || '-',
                    },
                    {
                      title: '结果信息',
                      dataIndex: 'message',
                      key: 'message',
                      ellipsis: {
                        showTitle: false,
                      },
                      render: (message: string) => (
                        <Tooltip placement="topLeft" title={message || ''}>
                          <Text 
                            style={{ 
                              fontSize: '12px',
                              maxWidth: '200px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              display: 'inline-block'
                            }}
                          >
                            {message || '-'}
                          </Text>
                        </Tooltip>
                      ),
                    },
                  ]}
                />
              </div>
            )}

            {/* 批次配置信息 */}
            {selectedRecord.details?.batches && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>批次配置信息</Title>
                <div style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  maxHeight: '200px',
                  overflow: 'auto',
                  wordWrap: 'break-word',
                  whiteSpace: 'pre-wrap'
                }}>
                  {JSON.stringify({
                    overallStatus: selectedRecord.details.overallStatus,
                    startTime: selectedRecord.timestamp,
                    endTime: selectedRecord.details.endTime,
                    batchCount: selectedRecord.details.batches?.length || 0,
                  }, null, 2)}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default BatchHistorySection;
