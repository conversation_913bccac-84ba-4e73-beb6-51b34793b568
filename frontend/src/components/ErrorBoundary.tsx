import React, { Component, type ReactNode } from 'react';
import { Result, Button, Alert, Typography, Collapse } from 'antd';

const { Text } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // 记录错误到状态中以便显示详细信息
    this.setState({
      error,
      errorInfo,
    });

    // 可以在这里添加错误报告逻辑
    // 例如发送到错误监控服务
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div style={{ padding: '24px' }}>
          <Result
            status="500"
            title="页面出现错误"
            subTitle="抱歉，页面运行时出现了错误。您可以尝试刷新页面或重置状态。"
            extra={[
              <Button type="primary" onClick={this.handleReload} key="reload">
                刷新页面
              </Button>,
              <Button onClick={this.handleReset} key="reset">
                重置状态
              </Button>,
            ]}
          />

          {/* 开发环境下显示错误详情 */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Alert
              message="错误详情（仅开发环境显示）"
              type="error"
              style={{ marginTop: '16px' }}
              description={
                <Collapse ghost>
                  <Panel header="查看错误堆栈" key="1">
                    <div style={{ marginBottom: '8px' }}>
                      <Text strong>错误消息：</Text>
                      <br />
                      <Text code>{this.state.error.message}</Text>
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <Text strong>错误堆栈：</Text>
                      <pre style={{
                        fontSize: '12px',
                        background: '#f5f5f5',
                        padding: '8px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        maxHeight: '200px'
                      }}>
                        {this.state.error.stack}
                      </pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <Text strong>组件堆栈：</Text>
                        <pre style={{
                          fontSize: '12px',
                          background: '#f5f5f5',
                          padding: '8px',
                          borderRadius: '4px',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </Panel>
                </Collapse>
              }
            />
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
