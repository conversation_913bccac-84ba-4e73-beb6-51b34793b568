import React from 'react';
import {
  Card,
  Typography,
  Tag,
  Tooltip,
  Space,
  Alert,
  Button,
} from 'antd';
import { TimeUtils } from '../../utils/commonUtils';
import {
  InfoCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { StatCard, ResponsiveGrid } from '../base';

const { Text } = Typography;

// 余额状态卡片属性接口
interface BalanceStatusCardProps {
  balances: Map<string, number>;
  loading: boolean;
  lastUpdated: Date | null;
  estimatedCount: number;
  realCount: number;
  onRefresh: () => void;
  style?: React.CSSProperties;
}

/**
 * 余额状态显示卡片组件
 * 显示余额缓存状态、统计信息和刷新控制
 */
const BalanceStatusCard: React.FC<BalanceStatusCardProps> = ({
  balances,
  loading,
  lastUpdated,
  estimatedCount,
  realCount,
  onRefresh,
  style,
}) => {
  const totalAccounts = balances.size;
  const totalBalance = Array.from(balances.values()).reduce((sum, balance) => sum + balance, 0);

  return (
    <Card
      title={
        <Space>
          <span>余额状态</span>
          <Tooltip title="显示账户余额的实时状态和缓存信息">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      }
      extra={
        <Button
          type="text"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={onRefresh}
          size="small"
        >
          刷新
        </Button>
      }
      size="small"
      style={style}
    >
      {/* 统计信息 */}
      <ResponsiveGrid columns={{ xs: 2, sm: 2, md: 4, lg: 4 }} gutter={[16, 16]}>
        <StatCard
          title="总账户数"
          value={totalAccounts}
          color="#1890ff"
          loading={loading}
        />
        <StatCard
          title="总余额"
          value={totalBalance.toFixed(2)}
          color="#52c41a"
          loading={loading}
        />
        <StatCard
          title="实时数据"
          value={realCount}
          color="#1890ff"
          suffix={
            <Tag color="blue">
              <CheckCircleOutlined /> 已同步
            </Tag>
          }
          loading={loading}
        />
        <StatCard
          title="估算数据"
          value={estimatedCount}
          color="#faad14"
          suffix={
            <Tag color="orange">
              <ClockCircleOutlined /> 待同步
            </Tag>
          }
          loading={loading}
        />
      </ResponsiveGrid>

      {/* 更新时间和状态提示 */}
      <div style={{
        marginTop: 16,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '8px'
      }}>
        <div>
          {lastUpdated && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              最后更新: {TimeUtils.formatUTC8Time(lastUpdated)}
            </Text>
          )}
        </div>
        <div>
          <Space size="small">
            {estimatedCount > 0 && (
              <Tag color="orange">
                {estimatedCount} 个估算值
              </Tag>
            )}
            {realCount > 0 && (
              <Tag color="green">
                {realCount} 个实时值
              </Tag>
            )}
          </Space>
        </div>
      </div>

      {/* 状态提示 */}
      {estimatedCount > 0 && (
        <Alert
          message="余额优化提示"
          description={
            <div>
              <p>• 系统正在使用本地缓存优化余额查询性能</p>
              <p>• 橙色标签表示基于点亮操作的估算值</p>
              <p>• 绿色标签表示从服务器同步的实时值</p>
              <p>• 系统会自动定期同步最新余额</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginTop: 12 }}
          closable
        />
      )}

      {loading && (
        <Alert
          message="正在更新余额数据..."
          type="info"
          showIcon
          style={{ marginTop: 12 }}
        />
      )}
    </Card>
  );
};

export default BalanceStatusCard;
