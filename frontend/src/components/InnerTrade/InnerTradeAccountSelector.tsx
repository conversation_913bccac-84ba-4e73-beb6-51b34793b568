import React, { useState, useCallback } from 'react';
import { Form, Input, Button, Row, Col, Alert, Card, Statistic, Switch, InputNumber } from 'antd';
import { UserOutlined, DollarOutlined, FilterOutlined } from '@ant-design/icons';
import { UserService, type UserWithBalance } from '../../services/userService';

interface InnerTradeAccountSelectorProps {
  /** 表单实例 */
  form: {
    getFieldsValue: () => Record<string, unknown>;
    setFieldsValue: (values: Record<string, unknown>) => void;
    getFieldValue: (name: string) => unknown;
  };
  /** 解析后的账户缓存 */
  parsedAccountsCache?: {
    accounts: Array<{ apiKey: string; secret: string }>;
    count: number;
  } | null;
  /** 错误显示函数 */
  showError: (title: string | null, message: string) => void;
  /** 成功显示函数 */
  showSuccess: (message: string) => void;
}

interface UsdtFilterResult {
  totalAccounts: number;
  accountsWithBalance: number;
  filteredAccounts: string;
}

/**
 * 内盘交易账户选择器
 * 
 * 功能：
 * - 支持手动输入账户
 * - 支持USDT余额智能筛选
 * - 支持随机模式和手动模式切换
 * - 复用批量点亮的设计模式
 * 
 * 设计原则：
 * - 复用 AccountConfigSection 的设计
 * - 适配内盘交易的USDT余额需求
 * - 保持与现有组件的一致性
 */
const InnerTradeAccountSelector: React.FC<InnerTradeAccountSelectorProps> = ({
  form,
  parsedAccountsCache,
  showError,
  showSuccess,
}) => {
  const [usdtFilterLoading, setUsdtFilterLoading] = useState(false);
  const [usdtFilterResult, setUsdtFilterResult] = useState<UsdtFilterResult | null>(null);
  const [isRandomMode, setIsRandomMode] = useState(false);

  // USDT余额筛选处理
  const handleUsdtFilter = useCallback(async () => {
    setUsdtFilterLoading(true);
    try {
      // 获取当前表单中的配置
      const values = form.getFieldsValue();
      const minUsdtBalance = Number(values.minUsdtBalance) || 10; // 默认最小10 USDT
      const maxAccounts = Number(values.customAccountCount) || 500; // 默认最大500个账户

      console.log(`USDT余额筛选：最小余额 ${minUsdtBalance} USDT，最大账户数限制为 ${maxAccounts}`);

      const response = await UserService.getAccountsWithBalance('USDT');
      console.log('📊 内盘交易USDT余额筛选API响应:', response);

      if (!response.success) {
        showError(null, '获取账户USDT余额信息失败');
        return;
      }

      console.log('📊 API响应数据结构:', response);

      // 检查响应数据格式 - 根据实际API响应结构调整
      let accountsData: UserWithBalance[];
      if (response.data && Array.isArray(response.data)) {
        accountsData = response.data;
      } else if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        // 处理嵌套的data结构
        const nestedData = (response.data as { data: unknown }).data;
        if (Array.isArray(nestedData)) {
          accountsData = nestedData as UserWithBalance[];
        } else {
          console.error('嵌套数据不是数组:', nestedData);
          showError(null, '账户数据格式错误，请联系管理员');
          return;
        }
      } else {
        console.error('账户数据格式错误:', response);
        showError(null, '账户数据格式错误，请联系管理员');
        return;
      }

      // 过滤有USDT余额且满足最小余额要求的账户
      const accountsWithSufficientBalance = accountsData.filter(
        (account: UserWithBalance) =>
          account.hasBalance &&
          (account.balance || 0) >= minUsdtBalance
      );

      // 应用数量限制
      const limitedAccounts = accountsWithSufficientBalance.slice(0, maxAccounts);

      // 构建账户信息字符串
      const filteredAccountsText = limitedAccounts
        .map((account: UserWithBalance) => `${account.apiKey},${account.secret}`)
        .join('\n');

      // 更新表单中的账户信息
      if (!isRandomMode) {
        form.setFieldsValue({ accounts: filteredAccountsText });
      }

      // 设置筛选结果统计
      setUsdtFilterResult({
        totalAccounts: accountsData.length,
        accountsWithBalance: limitedAccounts.length,
        filteredAccounts: filteredAccountsText,
      });

      const totalWithBalance = accountsWithSufficientBalance.length;
      const actualUsed = limitedAccounts.length;

      if (totalWithBalance > maxAccounts) {
        showSuccess(
          `USDT余额筛选完成：从 ${accountsData.length} 个账户中找到 ${totalWithBalance} 个满足条件账户，` +
          `已选择前 ${actualUsed} 个（受限制影响）`
        );
      } else {
        showSuccess(
          `USDT余额筛选完成：从 ${accountsData.length} 个账户中筛选出 ${actualUsed} 个满足条件账户`
        );
      }

    } catch (error) {
      console.error('USDT余额筛选失败:', error);
      showError(null, 'USDT余额筛选失败，请重试');
    } finally {
      setUsdtFilterLoading(false);
    }
  }, [form, showError, showSuccess, isRandomMode]);

  // 随机模式切换处理
  const handleRandomModeChange = useCallback((checked: boolean) => {
    setIsRandomMode(checked);
    
    if (checked) {
      // 切换到随机模式，清空手动输入的账户
      form.setFieldsValue({ accounts: '' });
      showSuccess('已切换到随机模式，系统将自动从有USDT余额的账户中随机选择');
    } else {
      showSuccess('已切换到手动模式，请手动输入账户或使用USDT余额筛选');
    }
  }, [form, showSuccess]);

  return (
    <>
      {/* 性能警告 */}
      {parsedAccountsCache && parsedAccountsCache.count > 500 && !isRandomMode && (
        <Alert
          message="性能提醒"
          description={
            <div>
              <p>检测到您输入了 {parsedAccountsCache.count} 个账户，数量较大可能影响性能。</p>
              <p>建议：</p>
              <ul>
                <li>启用"自定义账户数量"功能，限制单次处理数量</li>
                <li>或将账户分批处理，每批不超过200个</li>
                <li>使用"USDT余额筛选"功能预先过滤有效账户</li>
                <li>考虑使用"随机模式"进行小批量测试</li>
              </ul>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          closable
        />
      )}

      {/* 模式选择 */}
      <Card title="账户选择模式" style={{ marginBottom: 16 }}>
        <Row gutter={[24, 16]}>
          <Col xs={24}>
            <Form.Item
              label="选择模式"
              tooltip="随机模式：系统自动从有USDT余额的账户中随机选择；手动模式：需要手动输入账户信息"
            >
              <Switch
                checked={isRandomMode}
                onChange={handleRandomModeChange}
                checkedChildren="随机模式"
                unCheckedChildren="手动模式"
              />
            </Form.Item>
          </Col>
        </Row>

        {isRandomMode && (
          <Alert
            message="随机模式说明"
            description="在随机模式下，系统将自动从accounts.json中筛选有USDT余额的账户进行交易，无需手动输入账户信息。"
            type="info"
            showIcon
            style={{ marginTop: 8 }}
          />
        )}
      </Card>

      {/* USDT余额配置 */}
      <Card title="USDT余额配置" style={{ marginBottom: 16 }}>
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <Form.Item
              label="最小USDT余额"
              name="minUsdtBalance"
              initialValue={10}
              rules={[{ required: true, message: '请输入最小USDT余额' }]}
              tooltip="只有USDT余额大于等于此值的账户才会被选择进行交易"
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={2}
                style={{ width: '100%' }}
                placeholder="请输入最小USDT余额"
                addonAfter="USDT"
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              label="最大账户数量"
              name="customAccountCount"
              initialValue={500}
              rules={[{ required: true, message: '请输入最大账户数量' }]}
              tooltip="限制最大使用的账户数量，避免性能问题"
            >
              <InputNumber
                min={1}
                max={1000}
                style={{ width: '100%' }}
                placeholder="请输入最大账户数量"
              />
            </Form.Item>
          </Col>
        </Row>

        {/* USDT余额筛选按钮 */}
        <Row gutter={[16, 16]}>
          <Col>
            <Button
              type="primary"
              icon={<FilterOutlined />}
              loading={usdtFilterLoading}
              onClick={handleUsdtFilter}
              disabled={isRandomMode}
            >
              USDT余额筛选
            </Button>
          </Col>
        </Row>

        {/* 筛选结果统计 */}
        {usdtFilterResult && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={8}>
              <Statistic
                title="总账户数"
                value={usdtFilterResult.totalAccounts}
                prefix={<UserOutlined />}
              />
            </Col>
            <Col xs={8}>
              <Statistic
                title="满足条件账户"
                value={usdtFilterResult.accountsWithBalance}
                prefix={<DollarOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col xs={8}>
              <Statistic
                title="筛选成功率"
                value={
                  usdtFilterResult.totalAccounts > 0
                    ? ((usdtFilterResult.accountsWithBalance / usdtFilterResult.totalAccounts) * 100).toFixed(1)
                    : 0
                }
                suffix="%"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
          </Row>
        )}
      </Card>

      {/* 手动账户输入 */}
      {!isRandomMode && (
        <Card title="账户信息" style={{ marginBottom: 16 }}>
          <Row gutter={[24, 24]}>
            <Col xs={24}>
              <Form.Item
                label="账户信息"
                name="accounts"
                rules={[{ required: true, message: '请输入账户信息或使用USDT余额筛选' }]}
                tooltip="每行一个账户，格式：apiKey,secret"
              >
                <Input.TextArea
                  rows={8}
                  placeholder={`请输入账户信息，每行一个账户，格式：apiKey,secret
或使用上方的"USDT余额筛选"功能自动填充`}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      )}
    </>
  );
};

export default InnerTradeAccountSelector;
