import React from 'react';
import { Row, Col, Form, Input, Button, Alert, Tag, Space, Typography, Progress } from 'antd';
import { SearchOutlined, SettingOutlined, ReloadOutlined } from '@ant-design/icons';
import type { SymbolConversionResult } from '../../services/symbolService';
import type { LightableToken } from '../../services/enhancedBatchLightService';

const { Text } = Typography;

interface BalanceFilterResult {
  totalAccounts: number;
  accountsWithBalance: number;
  filteredAccounts: string;
}

interface TokenSearchSectionProps {
  symbolLoading: boolean;
  smartFilterLoading: boolean;
  lightableTokensLoading: boolean;
  symbolResult: SymbolConversionResult | null;
  lightableTokens: LightableToken[] | null;
  smartFilterResult: BalanceFilterResult | null;
  onSymbolSearch: (symbol: string) => void;
  onSmartFilter: () => void;
  onGetLightableTokens: () => void;
  onSelectLightableToken: (token: LightableToken) => void;
  onSelectAssetId: (assetId: number) => void;
  onSetLightableTokens: (tokens: LightableToken[] | null) => void;
  showSuccess: (message: string) => void;
  // 新增：页面刷新相关
  pageRefreshing?: boolean;
  refreshProgress?: { step: string; progress: number; total: number; isComplete: boolean };
  onManualRefresh?: () => void;
}

const TokenSearchSection: React.FC<TokenSearchSectionProps> = ({
  symbolLoading,
  smartFilterLoading,
  lightableTokensLoading,
  symbolResult,
  lightableTokens,
  smartFilterResult,
  onSymbolSearch,
  onSmartFilter,
  onGetLightableTokens,
  onSelectLightableToken,
  onSelectAssetId,
  onSetLightableTokens,
  showSuccess,
  // 新增：页面刷新相关
  pageRefreshing = false,
  refreshProgress,
  onManualRefresh,
}) => {
  return (
    <>
      {/* Token搜索和资产ID配置 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={8}>
          <Form.Item
            label="Token符号"
            name="symbol"
            tooltip="输入Token符号进行搜索，如：愤怒蜡笔、BTC等"
          >
            <Input.Search
              placeholder="请输入Token符号，如：愤怒蜡笔、BTC等"
              enterButton={<SearchOutlined />}
              loading={symbolLoading}
              onSearch={onSymbolSearch}
              size="large"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item
            label="资产ID"
            name="assetId"
            tooltip="可以直接输入资产ID，或通过Token符号搜索获取"
          >
            <Input
              placeholder="资产ID"
              size="large"
              onChange={(e) => {
                const value = e.target.value;
                if (value) {
                  onSelectAssetId(parseInt(value));
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Form.Item
            label="余额筛选"
            tooltip="自动筛选有NINE余额的账户（首次点亮分析将在执行时进行）"
          >
            <Button
              type="primary"
              icon={<SettingOutlined />}
              loading={smartFilterLoading}
              onClick={onSmartFilter}
              size="large"
              block
            >
              筛选有余额账户
            </Button>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Form.Item
            label="可点亮Token"
            tooltip="获取当前可点亮的Token列表，快速选择有效的资产ID"
          >
            <Button
              type="dashed"
              icon={<SearchOutlined />}
              loading={lightableTokensLoading}
              onClick={onGetLightableTokens}
              size="large"
              block
            >
              获取可点亮Token
            </Button>
          </Form.Item>
        </Col>

        {/* 手动刷新按钮 */}
        {onManualRefresh && (
          <Col xs={24} sm={12} md={4}>
            <Form.Item
              label="页面刷新"
              tooltip="手动刷新账户余额和状态信息"
            >
              <Button
                type="default"
                icon={<ReloadOutlined />}
                loading={pageRefreshing}
                onClick={onManualRefresh}
                size="large"
                block
              >
                {pageRefreshing ? '刷新中...' : '手动刷新'}
              </Button>
            </Form.Item>
          </Col>
        )}
      </Row>

      {/* 刷新进度指示器 */}
      {pageRefreshing && refreshProgress && refreshProgress.step && (
        <Alert
          message={`正在${refreshProgress.step}...`}
          description={
            <Progress
              percent={Math.round((refreshProgress.progress / refreshProgress.total) * 100)}
              size="small"
              status={refreshProgress.isComplete ? 'success' : 'active'}
              showInfo={false}
            />
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Symbol搜索结果显示 */}
      {symbolResult && (
        <Alert
          message={
            <div>
              <Text strong>找到Token: {symbolResult.symbol}</Text>
              <br />
              <Text>可点亮选项: {symbolResult.lightableOptions}/{symbolResult.totalOptions}</Text>
              {symbolResult.recommendedOption && (
                <div style={{ marginTop: 8 }}>
                  <Text>推荐选项: </Text>
                  <Tag color="blue">
                    ID: {symbolResult.recommendedOption.assetId} | 已点亮: {symbolResult.recommendedOption.lightNum}
                  </Tag>
                </div>
              )}
              {symbolResult.allOptions.length > 1 && (
                <div style={{ marginTop: 8 }}>
                  <Text>所有选项: </Text>
                  <div style={{ marginTop: 8 }}>
                    <Space wrap>
                      {/* 限制显示数量以提高性能 */}
                      {symbolResult.allOptions.slice(0, 10).map((option) => (
                        <Tag
                          key={option.assetId}
                          color={option.canLight ? 'blue' : 'red'}
                          style={{ cursor: 'pointer' }}
                          onClick={() => {
                            if (option.canLight) {
                              onSelectAssetId(option.assetId);
                              showSuccess(`已选择资产ID: ${option.assetId}`);
                            }
                          }}
                        >
                          ID: {option.assetId} | 点亮: {option.lightNum}
                          {option.canLight ? '' : ' (不可点亮)'}
                        </Tag>
                      ))}
                    </Space>
                    {symbolResult.allOptions.length > 10 && (
                      <div style={{ marginTop: 8 }}>
                        <Text type="secondary">
                          显示前10个选项，共{symbolResult.allOptions.length}个选项
                        </Text>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 可点亮Token列表显示 */}
      {lightableTokens && lightableTokens.length > 0 && (
        <Alert
          message="可点亮Token列表"
          description={
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">点击Token快速选择资产ID</Text>
              <div style={{ marginTop: 8 }}>
                <Space wrap>
                  {lightableTokens.map((token) => (
                    <Tag
                      key={token.assetId}
                      color="green"
                      style={{ cursor: 'pointer', marginBottom: 4 }}
                      onClick={() => onSelectLightableToken(token)}
                    >
                      {token.symbol} (ID: {token.assetId}, 点亮: {token.lightNum})
                    </Tag>
                  ))}
                </Space>
              </div>
            </div>
          }
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => onSetLightableTokens(null)}
        />
      )}

      {/* 余额筛选结果显示 */}
      {smartFilterResult && (
        <Alert
          message="余额筛选结果"
          description={
            <div>
              <Text>总账户: {smartFilterResult.totalAccounts}</Text><br />
              <Text>有余额账户: {smartFilterResult.accountsWithBalance}</Text><br />
              <Text type="secondary">注意：首次点亮分析将在执行时自动进行</Text>
            </div>
          }
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
    </>
  );
};

export default TokenSearchSection;
