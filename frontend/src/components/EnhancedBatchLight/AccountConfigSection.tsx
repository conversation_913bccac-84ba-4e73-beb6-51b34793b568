import React from 'react';
import { Row, Col, Form, Input, Alert } from 'antd';
import type { ParsedAccountsCache } from '../../hooks/useAccountParser';

interface AccountConfigSectionProps {
  parsedAccountsCache: ParsedAccountsCache | null;
}

const AccountConfigSection: React.FC<AccountConfigSectionProps> = ({
  parsedAccountsCache,
}) => {
  return (
    <>
      {/* 性能警告 */}
      {parsedAccountsCache && parsedAccountsCache.count > 1200 && (
        <Alert
          message="性能提醒"
          description={
            <div>
              <p>检测到您输入了 {parsedAccountsCache.count} 个账户，数量较大可能影响性能。</p>
              <p>建议：</p>
              <ul>
                <li>启用"自定义账户数量"功能，限制单次处理数量</li>
                <li>或将账户分批处理，每批不超过500个</li>
                <li>使用"余额筛选"功能预先过滤有效账户</li>
              </ul>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          closable
        />
      )}

      {/* 账户配置 */}
      <Row gutter={[24, 24]}>
        <Col xs={24}>
          <Form.Item
            label="账户信息"
            name="accounts"
            rules={[{ required: true, message: '请输入账户信息' }]}
            tooltip="每行一个账户，格式：apiKey,secret"
          >
            <Input.TextArea
              rows={8}
              placeholder="请输入账户信息，每行一个账户，格式：apiKey,secret"
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default AccountConfigSection;
