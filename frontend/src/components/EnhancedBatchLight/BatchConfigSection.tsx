import React from 'react';
import { Row, Col, Form, Switch, InputNumber, Card, Typography, Alert, Button } from 'antd';
import type { BatchConfigAnalysis } from '../../hooks/useLivePreview';

const { Text } = Typography;

interface BatchConfigSectionProps {
  batchConfigAnalysis: BatchConfigAnalysis | null;
  onApplyBatchConfigSuggestion: () => void;
}

const BatchConfigSection: React.FC<BatchConfigSectionProps> = ({
  batchConfigAnalysis,
  onApplyBatchConfigSuggestion,
}) => {
  return (
    <Col xs={24} md={12}>
      {/* 批次配置 */}
      <Form.Item
        label="启用批次处理"
        name="enableBatchConfig"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.enableBatchConfig !== currentValues.enableBatchConfig
        }
      >
        {({ getFieldValue }) => {
          const enableBatchConfig = getFieldValue('enableBatchConfig');

          if (enableBatchConfig) {
            return (
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    label="批次大小"
                    name="batchSize"
                    rules={[{ required: true, message: '请输入批次大小' }]}
                    tooltip="每批次处理的账户数量，建议设置为50-100以平衡效率和稳定性"
                  >
                    <InputNumber
                      min={1}
                      max={200}
                      placeholder="每批次账户数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="间隔时间(ms)"
                    name="intervalMs"
                    rules={[{ required: true, message: '请输入间隔时间' }]}
                    tooltip="批次之间的等待时间（毫秒），建议设置1000-5000ms以避免频率限制"
                  >
                    <InputNumber
                      min={0}
                      max={60000}
                      placeholder="批次间隔时间"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            );
          }
          return null;
        }}
      </Form.Item>

      {/* 自定义点亮账户数 */}
      <Form.Item
        label="启用自定义账户数"
        name="enableCustomLightCount"
        valuePropName="checked"
        tooltip="启用后将限制参与点亮的账户数量，系统会自动筛选首次点亮的账户"
      >
        <Switch />
      </Form.Item>

      <Form.Item dependencies={['enableCustomLightCount']}>
        {({ getFieldValue }) => {
          const enableCustomLightCount = getFieldValue('enableCustomLightCount');
          if (enableCustomLightCount) {
            return (
              <Form.Item
                label="目标账户数"
                name="customLightCount"
                rules={[
                  { required: true, message: '请输入目标账户数量' },
                  { type: 'number', min: 1, max: 1200, message: '账户数量必须在1-1200之间' }
                ]}
                tooltip="设置参与点亮操作的目标账户数量，系统将从首次点亮账户中自动选择"
              >
                <InputNumber
                  min={1}
                  max={1200}
                  placeholder="请输入目标账户数量"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            );
          }
          return null;
        }}
      </Form.Item>

      {/* 批次配置分析 */}
      {batchConfigAnalysis && (
        <Card size="small" style={{ marginTop: 16 }}>
          <Text strong>批次配置分析</Text>
          <div style={{ marginTop: 8 }}>
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <Text type="secondary">总账户数: </Text>
                <Text>{batchConfigAnalysis.totalAccounts}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">有效账户数: </Text>
                <Text>{batchConfigAnalysis.effectiveAccounts}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">批次大小: </Text>
                <Text>{batchConfigAnalysis.batchSize}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">批次数量: </Text>
                <Text>{batchConfigAnalysis.batchCount}</Text>
              </Col>
              {batchConfigAnalysis.warning && (
                <Col span={24}>
                  <Alert
                    message={batchConfigAnalysis.warning}
                    type="warning"
                    showIcon
                    style={{ fontSize: '12px' }}
                  />
                </Col>
              )}
              {batchConfigAnalysis.suggestion && (
                <Col span={24}>
                  <Alert
                    message={batchConfigAnalysis.suggestion}
                    type="info"
                    showIcon
                    style={{ fontSize: '12px' }}
                    action={
                      <Button
                        size="small"
                        type="link"
                        onClick={onApplyBatchConfigSuggestion}
                      >
                        应用建议
                      </Button>
                    }
                  />
                </Col>
              )}
            </Row>
          </div>
        </Card>
      )}
    </Col>
  );
};

export default BatchConfigSection;
