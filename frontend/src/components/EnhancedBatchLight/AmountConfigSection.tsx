import React from 'react';
import { Row, Col, Form, Switch, InputNumber, Card, Typography } from 'antd';
import type { LivePreviewSummary } from '../../hooks/useLivePreview';

const { Text } = Typography;

interface AmountConfigSectionProps {
  livePreviewSummary: LivePreviewSummary | null;
}

const AmountConfigSection: React.FC<AmountConfigSectionProps> = ({
  livePreviewSummary,
}) => {
  return (
    <Col xs={24} md={12}>
      <Form.Item
        label="随机金额模式"
        name="enableRandomAmount"
        valuePropName="checked"
        tooltip="启用后每个账户的点亮金额将在指定范围内随机生成"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.enableRandomAmount !== currentValues.enableRandomAmount
        }
      >
        {({ getFieldValue }) => {
          const enableRandomAmount = getFieldValue('enableRandomAmount');

          if (enableRandomAmount) {
            return (
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    label="最小金额"
                    name="randomMinAmount"
                    rules={[
                      { required: true, message: '请输入最小点亮金额' },
                      {
                        validator: (_, value) => {
                          if (value !== undefined && value !== null) {
                            if (!Number.isInteger(value)) {
                              return Promise.reject(new Error('随机数量模式下只能输入整数'));
                            }
                            if (value < 1) {
                              return Promise.reject(new Error('最小金额不能小于1'));
                            }
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    tooltip="每个账户点亮金额的最小值（NINE代币，仅限整数）"
                  >
                    <InputNumber
                      min={1}
                      max={1000}
                      step={1}
                      precision={0}
                      placeholder="最小点亮金额（整数）"
                      style={{ width: '100%' }}
                      parser={(value) => value ? Math.floor(Number(value)) : 0}
                      formatter={(value) => value ? Math.floor(Number(value)).toString() : ''}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="最大金额"
                    name="randomMaxAmount"
                    rules={[
                      { required: true, message: '请输入最大点亮金额' },
                      {
                        validator: (_, value) => {
                          if (value !== undefined && value !== null) {
                            if (!Number.isInteger(value)) {
                              return Promise.reject(new Error('随机数量模式下只能输入整数'));
                            }
                            if (value < 1) {
                              return Promise.reject(new Error('最大金额不能小于1'));
                            }
                          }
                          return Promise.resolve();
                        },
                      },
                      ({ getFieldValue }) => ({
                        validator: (_, value) => {
                          const minAmount = getFieldValue('randomMinAmount');
                          if (value && minAmount && value <= minAmount) {
                            return Promise.reject(new Error('最大金额必须大于最小金额'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                    tooltip="每个账户点亮金额的最大值（NINE代币，仅限整数）"
                  >
                    <InputNumber
                      min={1}
                      max={1000}
                      step={1}
                      precision={0}
                      placeholder="最大点亮金额（整数）"
                      style={{ width: '100%' }}
                      parser={(value) => value ? Math.floor(Number(value)) : 0}
                      formatter={(value) => value ? Math.floor(Number(value)).toString() : ''}
                    />
                  </Form.Item>
                </Col>
              </Row>
            );
          } else {
            return (
              <Form.Item
                label="固定金额"
                name="amount"
                rules={[{ required: true, message: '请输入点亮金额' }]}
                tooltip="每个账户统一使用的点亮金额（NINE代币）"
              >
                <InputNumber
                  min={0.01}
                  max={1000000}
                  step={0.01}
                  placeholder="每账户点亮金额"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            );
          }
        }}
      </Form.Item>

      {/* 实时预览摘要 */}
      {livePreviewSummary && (
        <Card size="small" style={{ marginTop: 16 }}>
          <Text strong>实时预览摘要</Text>
          <div style={{ marginTop: 8 }}>
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <Text type="secondary">总账户数: </Text>
                <Text>{livePreviewSummary.totalAccountCount}</Text>
              </Col>
              <Col span={24}>
                <Text type="secondary">有效账户数: </Text>
                <Text strong style={{ color: livePreviewSummary.isLimited ? '#1890ff' : 'inherit' }}>
                  {livePreviewSummary.effectiveAccountCount}
                </Text>
                {livePreviewSummary.isLimited && (
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    (已限制)
                  </Text>
                )}
              </Col>
              <Col span={24}>
                <Text type="secondary">预计总金额: </Text>
                <Text strong>{livePreviewSummary.estimatedTotalAmount.toFixed(2)} NINE</Text>
              </Col>
              <Col span={24}>
                <Text type="secondary">金额范围: </Text>
                <Text>{livePreviewSummary.amountRange}</Text>
              </Col>
              {/* 新增：当前点亮和预计点亮预估 */}
              {livePreviewSummary.currentLightedCount !== undefined && (
                <Col span={24} style={{ marginTop: 8, padding: '8px', backgroundColor: '#f6ffed', borderRadius: '4px' }}>
                  <Text type="secondary">点亮状态: </Text>
                  <Text strong style={{ color: '#52c41a' }}>
                    当前已点亮: {livePreviewSummary.currentLightedCount}
                  </Text>
                  <Text type="secondary"> → </Text>
                  <Text strong style={{ color: '#1890ff' }}>
                    预计完成后: {livePreviewSummary.estimatedAfterLightCount}
                  </Text>
                  {livePreviewSummary.lightingIncrement !== undefined && livePreviewSummary.lightingIncrement > 0 && (
                    <Text strong style={{ color: '#fa8c16', marginLeft: 4 }}>
                      (+{livePreviewSummary.lightingIncrement})
                    </Text>
                  )}
                </Col>
              )}
            </Row>
          </div>
        </Card>
      )}
    </Col>
  );
};

export default AmountConfigSection;
