import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Modal,
  Button,
  Row,
  Col,
  Progress,
  Alert,
  Table,
  Tag,
  Tooltip,
  Typography,
  message,
  Collapse,
  List,
  Descriptions,
} from 'antd';
import {
  CopyOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { EnhancedBatchLightResult, BatchExecutionStatus } from '../../services/enhancedBatchLightService';
import { EnhancedBatchLightService } from '../../services/enhancedBatchLightService';
import { ApiKeyUtils, StatusUtils, TimeUtils } from '../../utils/commonUtils';

const { Text } = Typography;

/**
 * 账户详情弹窗组件 - 显示单个账户的详细点亮状态和错误信息
 */
const AccountDetailModal: React.FC<{
  visible: boolean;
  account: {
    apiKey: string;
    status: string;
    message: string;
    amount?: number;
    recordId?: number;
  } | null;
  onClose: () => void;
}> = ({ visible, account, onClose }) => {
  if (!account) return null;

  const isSuccess = account.status === 'success';

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {isSuccess ? (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          ) : (
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
          )}
          <span>账户详情</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      width={600}
    >
      <Descriptions bordered column={1} size="small">
        <Descriptions.Item label="API Key">
          <Text copyable={{ text: account.apiKey }} code>
            {account.apiKey}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="执行状态">
          <Tag color={isSuccess ? 'green' : 'red'}>
            {isSuccess ? '成功' : '失败'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="执行结果">
          <Text type={isSuccess ? 'success' : 'danger'}>
            {account.message}
          </Text>
        </Descriptions.Item>
        {account.amount && (
          <Descriptions.Item label="点亮数量">
            <Text strong>{account.amount}</Text>
          </Descriptions.Item>
        )}
        {account.recordId && (
          <Descriptions.Item label="记录ID">
            <Text code>{account.recordId}</Text>
          </Descriptions.Item>
        )}
      </Descriptions>

      {!isSuccess && (
        <Alert
          message="错误处理建议"
          description={
            <div>
              <p>• 检查账户API Key和Secret是否正确</p>
              <p>• 确认账户余额是否充足</p>
              <p>• 验证网络连接是否正常</p>
              <p>• 如果问题持续，请联系技术支持</p>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Modal>
  );
};

/**
 * 批次详细结果组件 - 显示成功和失败的详细信息
 */
const BatchDetailResults: React.FC<{ batch: BatchExecutionStatus }> = ({ batch }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<{
    apiKey: string;
    status: string;
    message: string;
    amount?: number;
    recordId?: number;
  } | null>(null);
  const [accountDetailVisible, setAccountDetailVisible] = useState(false);

  const successResults = batch.results.filter(r => r.status === 'success');
  const failedResults = batch.results.filter(r => r.status !== 'success');

  const handleAccountClick = (account: typeof selectedAccount) => {
    setSelectedAccount(account);
    setAccountDetailVisible(true);
  };

  if (batch.results.length === 0) {
    return <Text type="secondary">暂无详细结果</Text>;
  }

  return (
    <div style={{ marginTop: 16 }}>
      <Button
        type="link"
        size="small"
        onClick={() => setShowDetails(!showDetails)}
        style={{ padding: 0 }}
      >
        {showDetails ? '隐藏详细结果' : `查看详细结果 (${batch.results.length}条)`}
      </Button>

      {showDetails && (
        <Collapse
          size="small"
          style={{ marginTop: 8 }}
          items={[
            ...(successResults.length > 0 ? [{
              key: 'success',
              label: (
                <span>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                  成功记录 ({successResults.length})
                </span>
              ),
              children: (
                <List
                  size="small"
                  dataSource={successResults}
                  renderItem={(item) => (
                    <List.Item
                      style={{ padding: '4px 0', cursor: 'pointer' }}
                      onClick={() => handleAccountClick(item)}
                      extra={
                        <Button
                          type="link"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAccountClick(item);
                          }}
                        />
                      }
                    >
                      <Text style={{ fontSize: '12px' }}>
                        <Text code>{ApiKeyUtils.formatDisplay(item.apiKey)}</Text>
                        <Text style={{ marginLeft: 8, color: '#52c41a' }}>{item.message}</Text>
                        {item.amount && <Text style={{ marginLeft: 8 }}>数量: {item.amount}</Text>}
                      </Text>
                    </List.Item>
                  )}
                />
              )
            }] : []),
            ...(failedResults.length > 0 ? [{
              key: 'failed',
              label: (
                <span>
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                  失败记录 ({failedResults.length})
                </span>
              ),
              children: (
                <List
                  size="small"
                  dataSource={failedResults}
                  renderItem={(item) => (
                    <List.Item
                      style={{ padding: '4px 0', cursor: 'pointer' }}
                      onClick={() => handleAccountClick(item)}
                      extra={
                        <Button
                          type="link"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAccountClick(item);
                          }}
                        />
                      }
                    >
                      <Text style={{ fontSize: '12px' }}>
                        <Text code>{ApiKeyUtils.formatDisplay(item.apiKey)}</Text>
                        <Text style={{ marginLeft: 8, color: '#ff4d4f' }}>{item.message}</Text>
                        {item.amount && <Text style={{ marginLeft: 8 }}>数量: {item.amount}</Text>}
                      </Text>
                    </List.Item>
                  )}
                />
              )
            }] : [])
          ]}
        />
      )}

      {/* 账户详情弹窗 */}
      <AccountDetailModal
        visible={accountDetailVisible}
        account={selectedAccount}
        onClose={() => setAccountDetailVisible(false)}
      />
    </div>
  );
};

/**
 * 移动端批次信息卡片组件 - 为小屏幕设备优化显示
 */
const MobileBatchCard: React.FC<{ batch: BatchExecutionStatus }> = ({ batch }) => {
  return (
    <div
      style={{
        border: '1px solid #f0f0f0',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '12px',
        backgroundColor: '#fafafa'
      }}
    >
      <Row gutter={[12, 8]}>
        <Col span={12}>
          <Text type="secondary" style={{ fontSize: '12px' }}>批次 #{batch.batchIndex}</Text>
          <br />
          <Text strong>大小: {batch.batchSize}</Text>
        </Col>
        <Col span={12} style={{ textAlign: 'right' }}>
          <Tag
            color={
              batch.status === 'completed' ? 'green' :
              batch.status === 'processing' ? 'blue' :
              batch.status === 'failed' ? 'red' : 'default'
            }
          >
            {StatusUtils.formatBatchStatus(batch.status)}
          </Tag>
        </Col>
        <Col span={24}>
          <Row gutter={[8, 4]}>
            <Col span={8}>
              <Text type="secondary" style={{ fontSize: '12px' }}>成功</Text>
              <br />
              <Text style={{ color: batch.successCount > 0 ? '#52c41a' : undefined }}>
                {batch.successCount}
              </Text>
            </Col>
            <Col span={8}>
              <Text type="secondary" style={{ fontSize: '12px' }}>失败</Text>
              <br />
              <Text style={{ color: batch.failedCount > 0 ? '#ff4d4f' : undefined }}>
                {batch.failedCount}
              </Text>
            </Col>
            <Col span={8}>
              <Text type="secondary" style={{ fontSize: '12px' }}>时间</Text>
              <br />
              <Text style={{ fontSize: '11px' }}>
                {batch.startTime && batch.endTime
                  ? TimeUtils.formatExecutionTime(batch.startTime, batch.endTime)
                  : batch.startTime
                    ? TimeUtils.formatExecutionTime(batch.startTime)
                    : '-'
                }
              </Text>
            </Col>
          </Row>
        </Col>
        <Col span={24}>
          <BatchDetailResults batch={batch} />
        </Col>
      </Row>
    </div>
  );
};

/**
 * 批次ID显示组件 - 处理长ID的截断显示和复制功能
 */
const BatchIdDisplay: React.FC<{ batchId: string }> = ({ batchId }) => {
  const [copied, setCopied] = useState(false);

  // 截断显示逻辑：显示前8位...后4位
  const truncateBatchId = (id: string): string => {
    if (id.length <= 20) return id;
    return `${id.substring(0, 8)}...${id.substring(id.length - 4)}`;
  };

  // 复制到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(batchId);
      setCopied(true);
      message.success('批次ID已复制到剪贴板');
      setTimeout(() => setCopied(false), 2000);
    } catch {
      message.error('复制失败，请手动复制');
    }
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      justifyContent: 'center',
      flexWrap: 'nowrap',
      minWidth: 0,
      maxWidth: '200px'
    }}>
      <Tooltip title={`完整批次ID: ${batchId}`} placement="top">
        <span
          style={{
            cursor: 'pointer',
            fontSize: '13px',
            fontWeight: 500,
            color: '#1890ff',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            padding: '2px 6px',
            backgroundColor: '#f0f8ff',
            borderRadius: '3px',
            border: '1px solid #d6e4ff',
            display: 'inline-block',
            maxWidth: '140px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            lineHeight: '1.3'
          }}
          onClick={handleCopy}
        >
          {truncateBatchId(batchId)}
        </span>
      </Tooltip>
      <Tooltip title={copied ? '已复制!' : '点击复制完整批次ID'}>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={handleCopy}
          style={{
            color: copied ? '#52c41a' : '#1890ff',
            padding: '2px',
            flexShrink: 0,
            fontSize: '12px',
            minWidth: '20px',
            height: '20px',
            lineHeight: 1
          }}
        />
      </Tooltip>
    </div>
  );
};

interface BatchStatusModalProps {
  visible: boolean;
  currentBatch: EnhancedBatchLightResult | null;
  onClose: () => void;
  onRefresh?: (batchId: string) => void; // 新增刷新回调
  onBatchComplete?: (batchResult: EnhancedBatchLightResult) => void; // 批次完成回调
}

const BatchStatusModal: React.FC<BatchStatusModalProps> = ({
  visible,
  currentBatch,
  onClose,
  onRefresh,
  onBatchComplete,
}) => {
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // 使用ref来跟踪组件状态和定时器
  const isMountedRef = useRef(true);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const refreshingRef = useRef(false);



  // 清理定时器的函数
  const clearRefreshTimer = useCallback(() => {
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }
  }, []);

  // 安全的刷新函数
  const safeRefresh = useCallback(async (batchId: string) => {
    if (!isMountedRef.current || isClosing || refreshingRef.current || !onRefresh) {
      return;
    }

    try {
      refreshingRef.current = true;
      onRefresh(batchId);
      if (isMountedRef.current && !isClosing) {
        setLastUpdateTime(new Date());
      }
    } catch (error) {
      console.error('刷新批次状态失败:', error);
    } finally {
      refreshingRef.current = false;
    }
  }, [onRefresh, isClosing]);

  // 自动刷新逻辑
  useEffect(() => {
    // 清理之前的定时器
    clearRefreshTimer();

    if (!visible || !currentBatch || !autoRefresh || isClosing) {
      return;
    }

    // 如果批次已完成或失败，不启动定时器
    if (currentBatch.overallStatus === 'completed' || currentBatch.overallStatus === 'failed') {
      return;
    }

    refreshTimerRef.current = setInterval(() => {
      safeRefresh(currentBatch.batchId);
    }, 3000); // 每3秒刷新一次

    return clearRefreshTimer;
  }, [visible, currentBatch, autoRefresh, isClosing, safeRefresh, clearRefreshTimer]);

  // 当模态框打开时重置自动刷新
  useEffect(() => {
    if (visible && currentBatch) {
      setAutoRefresh(true);
      setLastUpdateTime(new Date());
    }
  }, [visible, currentBatch]);

  // 监听批次状态变化，自动停止刷新并触发完成回调
  useEffect(() => {
    if (currentBatch && (currentBatch.overallStatus === 'completed' || currentBatch.overallStatus === 'failed')) {
      if (isMountedRef.current && !isClosing) {
        setAutoRefresh(false);

        // 如果批次完成且有回调函数，触发回调
        if (currentBatch.overallStatus === 'completed' && onBatchComplete) {
          console.log('BatchStatusModal: 检测到批次完成，触发回调');
          onBatchComplete(currentBatch);
        }
      }
    }
  }, [currentBatch, isClosing, onBatchComplete]);

  // 增强的关闭处理函数
  const handleClose = useCallback(() => {
    setIsClosing(true);
    clearRefreshTimer();

    // 立即调用原始关闭函数
    onClose();

    // 重置状态（延迟执行，确保模态框已关闭）
    setTimeout(() => {
      if (isMountedRef.current) {
        setIsClosing(false);
        setAutoRefresh(true);
        setLastUpdateTime(null);
      }
    }, 100);
  }, [onClose, clearRefreshTimer]);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      clearRefreshTimer();
    };
  }, [clearRefreshTimer]);
  // 添加响应式样式
  const responsiveStyles = `
    <style>
      @media (max-width: 768px) {
        .mobile-batch-cards {
          display: block !important;
        }
        .desktop-batch-table {
          display: none !important;
        }
      }
      @media (min-width: 769px) {
        .mobile-batch-cards {
          display: none !important;
        }
        .desktop-batch-table {
          display: block !important;
        }
      }
    </style>
  `;

  return (
    <>
      <div dangerouslySetInnerHTML={{ __html: responsiveStyles }} />
      <Modal
        title={
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
            minHeight: '36px',
            padding: '0',
            paddingRight: '40px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              flex: '1 1 auto',
              minWidth: 0,
              paddingRight: '16px'
            }}>
              <span style={{
                fontSize: '16px',
                fontWeight: 600,
                color: '#262626'
              }}>
                批次执行状态
              </span>
              {currentBatch && (
                <Tag
                  color={
                    currentBatch.overallStatus === 'completed' ? 'success' :
                    currentBatch.overallStatus === 'processing' ? 'processing' :
                    currentBatch.overallStatus === 'failed' ? 'error' : 'default'
                  }
                  style={{
                    margin: 0,
                    borderRadius: '6px',
                    fontWeight: 500
                  }}
                >
                  {StatusUtils.formatBatchStatus(currentBatch.overallStatus)}
                </Tag>
              )}
            </div>
            {currentBatch && lastUpdateTime && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                flex: '0 0 auto',
                minWidth: 'fit-content',
                maxWidth: '200px',
                paddingTop: '2px',
                marginLeft: '12px',
                marginRight: '8px'
              }}>
                <Text
                  type="secondary"
                  style={{
                    fontSize: '11px',
                    whiteSpace: 'nowrap',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    lineHeight: '16px'
                  }}
                >
                  <span
                    style={{
                      width: '5px',
                      height: '5px',
                      borderRadius: '50%',
                      backgroundColor: autoRefresh ? '#52c41a' : '#d9d9d9',
                      display: 'inline-block',
                      flexShrink: 0
                    }}
                  />
                  <span style={{ display: window.innerWidth > 768 ? 'inline' : 'none' }}>
                    更新:
                  </span>
                  <span style={{ fontWeight: '500', fontSize: '11px' }}>
                    {TimeUtils.formatUTC8Time(lastUpdateTime)}
                  </span>
                </Text>
              </div>
            )}
          </div>
        }
        open={visible}
        onCancel={handleClose}
        footer={null}
        width="90vw"
        style={{ maxWidth: 1200, minWidth: 320 }}
        styles={{
          header: {
            paddingBottom: '16px',
            borderBottom: '1px solid #f0f0f0'
          },
          body: {
            maxHeight: '70vh',
            overflowY: 'auto',
            padding: window.innerWidth <= 768 ? '16px' : '24px'
          }
        }}
      >
      {currentBatch && (
        <div>
          {/* 响应式统计信息布局 */}
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ 
                  fontSize: '14px', 
                  color: 'rgba(0, 0, 0, 0.45)', 
                  marginBottom: '8px',
                  fontWeight: 'normal'
                }}>
                  批次ID
                </div>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: 600, 
                  color: 'rgba(0, 0, 0, 0.85)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '32px'
                }}>
                  <BatchIdDisplay batchId={currentBatch.batchId} />
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '14px',
                  color: 'rgba(0, 0, 0, 0.45)',
                  marginBottom: '8px',
                  fontWeight: 'normal'
                }}>
                  目标账户数
                </div>
                <div style={{
                  fontSize: '24px',
                  fontWeight: 600,
                  color: 'rgba(0, 0, 0, 0.85)',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {(() => {
                    // 计算实际执行的账户数量
                    const totalProcessed = currentBatch.summary?.totalProcessed || 0;
                    const totalInBatches = currentBatch.batches?.reduce((sum, batch) => sum + batch.batchSize, 0) || 0;
                    const actualTargetCount = Math.max(totalProcessed, totalInBatches);
                    return actualTargetCount > 0 ? actualTargetCount.toLocaleString() : (currentBatch.totalAccounts || 0).toLocaleString();
                  })()}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ 
                  fontSize: '14px', 
                  color: 'rgba(0, 0, 0, 0.45)', 
                  marginBottom: '8px',
                  fontWeight: 'normal'
                }}>
                  整体状态
                </div>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: 600, 
                  color: currentBatch.overallStatus === 'completed' ? '#52c41a' :
                         currentBatch.overallStatus === 'failed' ? '#ff4d4f' : '#1890ff',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {StatusUtils.formatBatchStatus(currentBatch.overallStatus)}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ 
                  fontSize: '14px', 
                  color: 'rgba(0, 0, 0, 0.45)', 
                  marginBottom: '8px',
                  fontWeight: 'normal'
                }}>
                  执行进度
                </div>
                <div style={{ 
                  fontSize: '24px', 
                  fontWeight: 600, 
                  color: 'rgba(0, 0, 0, 0.85)',
                  minHeight: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {EnhancedBatchLightService.calculateProgress(currentBatch)}%
                </div>
              </div>
            </Col>
          </Row>

          {/* 进度条 */}
          <div style={{ marginBottom: 24 }}>
            <Progress
              percent={EnhancedBatchLightService.calculateProgress(currentBatch)}
              status={currentBatch.overallStatus === 'failed' ? 'exception' : 'active'}
              strokeWidth={8}
              showInfo={true}
              format={(percent) => `${percent}%`}
            />
          </div>

          {/* 移除首次点亮分析结果显示 - 根据用户反馈，此信息显示不必要 */}

          {/* 批次详情表格 */}
          <div style={{ marginTop: 24 }}>
            <Text strong style={{ fontSize: '16px', marginBottom: 16, display: 'block' }}>
              批次执行详情
            </Text>

            {/* 移动端卡片视图 */}
            <div className="mobile-batch-cards" style={{ display: 'none' }}>
              {currentBatch.batches.map((batch, index) => (
                <MobileBatchCard key={batch.batchIndex || index} batch={batch} />
              ))}
            </div>

            {/* 桌面端表格视图 */}
            <div className="desktop-batch-table">
              <Table
              dataSource={currentBatch.batches}
              columns={[
                {
                  title: '批次',
                  dataIndex: 'batchIndex',
                  key: 'batchIndex',
                  width: 80,
                  align: 'center',
                  responsive: ['sm'],
                },
                {
                  title: '大小',
                  dataIndex: 'batchSize',
                  key: 'batchSize',
                  width: 80,
                  align: 'center',
                  responsive: ['sm'],
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  align: 'center',
                  render: (status) => (
                    <Tag
                      color={
                        status === 'completed' ? 'green' :
                        status === 'processing' ? 'blue' :
                        status === 'failed' ? 'red' : 'default'
                      }
                      style={{ margin: 0 }}
                    >
                      {StatusUtils.formatBatchStatus(status)}
                    </Tag>
                  ),
                },
                {
                  title: '成功',
                  dataIndex: 'successCount',
                  key: 'successCount',
                  width: 80,
                  align: 'center',
                  render: (count) => (
                    <Text style={{ color: count > 0 ? '#52c41a' : undefined }}>
                      {count}
                    </Text>
                  ),
                },
                {
                  title: '失败',
                  dataIndex: 'failedCount',
                  key: 'failedCount',
                  width: 80,
                  align: 'center',
                  render: (count) => (
                    <Text style={{ color: count > 0 ? '#ff4d4f' : undefined }}>
                      {count}
                    </Text>
                  ),
                },
                {
                  title: '执行时间',
                  key: 'executionTime',
                  width: 120,
                  align: 'center',
                  responsive: ['md'],
                  render: (_, record) => {
                    if (record.startTime && record.endTime) {
                      return (
                        <Text style={{ fontSize: '12px' }}>
                          {TimeUtils.formatExecutionTime(
                            record.startTime,
                            record.endTime
                          )}
                        </Text>
                      );
                    } else if (record.startTime) {
                      return (
                        <Text style={{ fontSize: '12px' }}>
                          {TimeUtils.formatExecutionTime(record.startTime)}
                        </Text>
                      );
                    }
                    return <Text type="secondary">-</Text>;
                  },
                },
                {
                  title: '详细结果',
                  key: 'details',
                  width: 120,
                  align: 'center',
                  responsive: ['lg'],
                  render: (_, record) => (
                    <BatchDetailResults batch={record} />
                  ),
                },
              ]}
              pagination={false}
              size="small"
              scroll={{ x: 600 }}
              style={{
                border: '1px solid #f0f0f0',
                borderRadius: '6px'
              }}
            />
            </div>
          </div>
        </div>
      )}
    </Modal>
    </>
  );
};

export default BatchStatusModal;
