import React, { useState, useEffect } from 'react';
import { Tag, Tooltip } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import api from '../services/api';
import { TimeUtils } from '../utils/commonUtils';

interface ApiStatusProps {
  showText?: boolean;
}

const ApiStatus: React.FC<ApiStatusProps> = ({ showText = true }) => {
  const [status, setStatus] = useState<'online' | 'offline' | 'checking'>('checking');
  const [lastCheck, setLastCheck] = useState<Date>(new Date());

  const checkApiStatus = async () => {
    try {
      setStatus('checking');
      const response = await api.get('/auth/health');
      if (response.status === 200) {
        setStatus('online');
      } else {
        setStatus('offline');
      }
    } catch {
      setStatus('offline');
    } finally {
      setLastCheck(new Date());
    }
  };

  useEffect(() => {
    checkApiStatus();
    
    // 每30秒检查一次API状态
    const interval = setInterval(checkApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          color: 'success',
          icon: <CheckCircleOutlined />,
          text: '在线',
          tooltip: `API服务正常 (最后检查: ${TimeUtils.formatUTC8Time(lastCheck)})`
        };
      case 'offline':
        return {
          color: 'error',
          icon: <ExclamationCircleOutlined />,
          text: '离线',
          tooltip: `API服务异常 (最后检查: ${TimeUtils.formatUTC8Time(lastCheck)})`
        };
      case 'checking':
        return {
          color: 'processing',
          icon: null,
          text: '检查中',
          tooltip: '正在检查API服务状态...'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Tooltip title={config.tooltip}>
      <Tag 
        color={config.color} 
        icon={config.icon}
        style={{ cursor: 'pointer' }}
        onClick={checkApiStatus}
      >
        {showText ? `API ${config.text}` : config.text}
      </Tag>
    </Tooltip>
  );
};

export default ApiStatus;
