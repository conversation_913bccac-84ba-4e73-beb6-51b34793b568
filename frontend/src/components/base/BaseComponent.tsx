import React from 'react';
import { Spin, Alert, Empty, Button, Space } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

/**
 * 基础组件属性接口
 */
export interface BaseComponentProps {
  loading?: boolean;
  error?: string | null;
  empty?: boolean;
  emptyDescription?: string;
  showRefresh?: boolean;
  onRefresh?: () => void;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

/**
 * 加载状态组件
 */
export const LoadingWrapper: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  tip?: string;
}> = ({ loading, children, tip = '加载中...' }) => {
  return (
    <Spin spinning={loading} tip={tip}>
      {children}
    </Spin>
  );
};

/**
 * 错误状态组件
 */
export const ErrorDisplay: React.FC<{
  error: string;
  onRetry?: () => void;
  showRetry?: boolean;
}> = ({ error, onRetry, showRetry = true }) => {
  return (
    <Alert
      message="操作失败"
      description={error}
      type="error"
      showIcon
      action={
        showRetry && onRetry ? (
          <Button size="small" danger onClick={onRetry}>
            重试
          </Button>
        ) : undefined
      }
      style={{ margin: '16px 0' }}
    />
  );
};

/**
 * 空状态组件
 */
export const EmptyDisplay: React.FC<{
  description?: string;
  onRefresh?: () => void;
  showRefresh?: boolean;
}> = ({ 
  description = '暂无数据', 
  onRefresh, 
  showRefresh = true 
}) => {
  return (
    <Empty
      description={description}
      image={Empty.PRESENTED_IMAGE_SIMPLE}
    >
      {showRefresh && onRefresh && (
        <Button type="primary" icon={<ReloadOutlined />} onClick={onRefresh}>
          刷新数据
        </Button>
      )}
    </Empty>
  );
};

/**
 * 基础容器组件
 * 提供统一的加载、错误、空状态处理
 */
export const BaseContainer: React.FC<BaseComponentProps> = ({
  loading = false,
  error = null,
  empty = false,
  emptyDescription,
  showRefresh = true,
  onRefresh,
  className,
  style,
  children,
}) => {
  // 错误状态
  if (error) {
    return (
      <div className={className} style={style}>
        <ErrorDisplay 
          error={error} 
          onRetry={onRefresh} 
          showRetry={showRefresh && !!onRefresh} 
        />
      </div>
    );
  }

  // 空状态
  if (empty && !loading) {
    return (
      <div className={className} style={style}>
        <EmptyDisplay 
          description={emptyDescription}
          onRefresh={onRefresh}
          showRefresh={showRefresh && !!onRefresh}
        />
      </div>
    );
  }

  // 正常状态（包含加载状态）
  return (
    <div className={className} style={style}>
      <LoadingWrapper loading={loading}>
        {children}
      </LoadingWrapper>
    </div>
  );
};

/**
 * 页面头部组件
 */
export const PageHeader: React.FC<{
  title: string;
  description?: string;
  extra?: React.ReactNode;
  onRefresh?: () => void;
  loading?: boolean;
}> = ({ title, description, extra, onRefresh, loading = false }) => {
  return (
    <div style={{ 
      marginBottom: 24, 
      padding: '16px 0',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'flex-start' 
      }}>
        <div>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
            {title}
          </h2>
          {description && (
            <p style={{ 
              margin: '8px 0 0 0', 
              color: '#666', 
              fontSize: '14px' 
            }}>
              {description}
            </p>
          )}
        </div>
        <Space>
          {onRefresh && (
            <Button
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              loading={loading}
              type="text"
            >
              刷新
            </Button>
          )}
          {extra}
        </Space>
      </div>
    </div>
  );
};

/**
 * 操作栏组件
 */
export const ActionBar: React.FC<{
  left?: React.ReactNode;
  right?: React.ReactNode;
  style?: React.CSSProperties;
}> = ({ left, right, style }) => {
  return (
    <div 
      style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 16,
        ...style 
      }}
    >
      <div>{left}</div>
      <div>{right}</div>
    </div>
  );
};

/**
 * 统计卡片组件
 */
export const StatCard: React.FC<{
  title: string;
  value: string | number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  color?: string;
  loading?: boolean;
}> = ({ title, value, prefix, suffix, color, loading = false }) => {
  return (
    <div style={{
      padding: '16px',
      background: '#fff',
      borderRadius: '6px',
      border: '1px solid #f0f0f0',
      textAlign: 'center',
    }}>
      <div style={{ 
        fontSize: '14px', 
        color: '#666', 
        marginBottom: '8px' 
      }}>
        {title}
      </div>
      <Spin spinning={loading}>
        <div style={{ 
          fontSize: '24px', 
          fontWeight: 'bold',
          color: color || '#1890ff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px'
        }}>
          {prefix}
          {value}
          {suffix}
        </div>
      </Spin>
    </div>
  );
};

/**
 * 响应式网格组件
 */
export const ResponsiveGrid: React.FC<{
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gutter?: number | [number, number];
}> = ({
  children,
  gutter = [16, 16]
}) => {
  const childrenArray = React.Children.toArray(children);
  
  return (
    <div style={{ 
      display: 'grid',
      gap: Array.isArray(gutter) ? `${gutter[1]}px ${gutter[0]}px` : `${gutter}px`,
      gridTemplateColumns: `repeat(auto-fit, minmax(250px, 1fr))`,
    }}>
      {childrenArray}
    </div>
  );
};

export default BaseContainer;
