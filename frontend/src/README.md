# Nine Light Frontend 代码结构

## 📁 目录结构

```
src/
├── components/          # 组件目录
│   ├── base/           # 基础组件（通用UI组件）
│   ├── Layout/         # 布局组件
│   ├── EnhancedBatchLight/  # 增强批量点亮功能组件
│   ├── AccountStatus/  # 账户状态管理组件
│   ├── BalanceStatus/  # 余额状态显示组件
│   └── BatchHistory/   # 批量操作历史组件
├── hooks/              # 自定义Hook
│   ├── base/          # 基础Hook（通用逻辑）
│   └── *.ts           # 功能特定Hook
├── services/           # 服务层
│   ├── base/          # 基础服务类
│   └── *.ts           # 具体业务服务
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── contexts/           # React上下文
├── pages/              # 页面组件
└── assets/             # 静态资源
```

## 🏗️ 架构设计原则

### 1. 高内聚低耦合
- **组件层**：每个组件职责单一，功能内聚
- **服务层**：业务逻辑封装，接口清晰
- **Hook层**：状态逻辑复用，减少重复代码

### 2. 分层架构
```
Pages (页面层)
  ↓
Components (组件层)
  ↓
Hooks (逻辑层)
  ↓
Services (服务层)
  ↓
Utils (工具层)
```

### 3. 依赖方向
- 上层依赖下层，下层不依赖上层
- 通过接口和抽象减少直接依赖
- 使用依赖注入模式

## 📦 模块说明

### 基础模块 (base/)
提供通用的基础功能，被其他模块复用：
- `BaseService`: 服务基类，统一错误处理和日志
- `BaseComponent`: 组件基类，统一UI模式
- `useBaseHook`: Hook基类，通用状态管理

### 业务模块
每个业务模块包含完整的功能实现：
- **EnhancedBatchLight**: 增强批量点亮功能
- **AccountStatus**: 账户状态管理
- **BalanceStatus**: 余额状态显示
- **BatchHistory**: 操作历史记录

### 工具模块 (utils/)
提供纯函数工具，无副作用：
- `ApiKeyUtils`: API Key格式化工具
- `StatusUtils`: 状态格式化工具
- `TimeUtils`: 时间处理工具
- `ValidationUtils`: 验证工具

## 🔧 开发规范

### 1. 命名规范
- **组件**: PascalCase (如: `BatchStatusModal`)
- **Hook**: camelCase with `use` prefix (如: `useBalanceAutoUpdate`)
- **服务**: PascalCase with `Service` suffix (如: `EnhancedBatchLightService`)
- **工具**: PascalCase with `Utils` suffix (如: `ApiKeyUtils`)

### 2. 文件组织
- 每个模块都有自己的目录
- 每个目录包含 `index.ts` 文件统一导出
- 相关文件放在同一目录下

### 3. 导入规范
```typescript
// 优先使用相对路径
import { BaseService } from '../base/BaseService';

// 使用统一入口导入
import { ApiKeyUtils } from '../../utils/commonUtils';

// 避免循环依赖
```

### 4. 类型定义
- 所有接口和类型都有明确定义
- 使用 TypeScript 严格模式
- 避免使用 `any` 类型

## 🚀 使用指南

### 创建新组件
1. 在对应目录创建组件文件
2. 继承或使用基础组件
3. 在 `index.ts` 中导出
4. 编写类型定义

### 创建新服务
1. 继承 `BaseService` 或 `BaseApiService`
2. 实现具体业务逻辑
3. 添加错误处理和日志
4. 编写接口定义

### 创建新Hook
1. 使用 `useBaseHook` 提供的基础功能
2. 实现特定业务逻辑
3. 返回清晰的接口
4. 处理生命周期

## 📈 性能优化

### 1. 代码分割
- 使用 React.lazy 进行组件懒加载
- 按路由分割代码
- 按功能模块分割

### 2. 缓存策略
- 使用 `BalanceCacheService` 进行数据缓存
- 实现本地存储缓存
- 合理设置缓存过期时间

### 3. 渲染优化
- 使用 React.memo 避免不必要的重渲染
- 合理使用 useCallback 和 useMemo
- 虚拟化长列表

## 🔍 调试和测试

### 1. 日志系统
- 使用 `BaseService` 的统一日志
- 区分不同级别的日志
- 生产环境控制日志输出

### 2. 错误处理
- 使用 `ErrorUtils` 统一错误格式化
- 实现错误边界组件
- 提供用户友好的错误提示

### 3. 开发工具
- 使用 React DevTools
- 使用 Redux DevTools (如果使用)
- 浏览器开发者工具

---

**维护者**: Nine Light Team  
**更新时间**: 2025年7月2日
