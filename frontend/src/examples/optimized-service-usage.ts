/**
 * 优化后的服务使用示例
 * 展示如何使用通用的余额获取方法
 */

import { UserService, type UserWithBalance } from '../services/userService';
import { BalanceCacheService, type AssetType } from '../services/balanceCacheService';

// 获取服务实例
const balanceService = BalanceCacheService.getInstance();

// 示例API密钥
const apiKey = 'example-api-key';
const apiKeys = ['api-key-1', 'api-key-2', 'api-key-3'];

/**
 * UserService 使用示例
 */
export async function userServiceExamples() {
  console.log('=== UserService 优化示例 ===');

  // 1. 获取NINE余额账户（默认）
  const nineAccounts = await UserService.getAccountsWithBalance();
  console.log('NINE余额账户:', nineAccounts);

  // 2. 获取NINE余额账户（显式指定）
  const nineAccountsExplicit = await UserService.getAccountsWithBalance('NINE');
  console.log('NINE余额账户（显式）:', nineAccountsExplicit);

  // 3. 获取USDT余额账户
  const usdtAccounts = await UserService.getAccountsWithBalance('USDT');
  console.log('USDT余额账户:', usdtAccounts);

  // 4. 向后兼容 - 使用旧方法
  const legacyUsdtAccounts = await UserService.getAccountsWithUsdtBalance();
  console.log('旧方法USDT余额账户:', legacyUsdtAccounts);
}

/**
 * BalanceCacheService 使用示例
 */
export async function balanceCacheServiceExamples() {
  console.log('=== BalanceCacheService 优化示例 ===');

  // 1. 获取单个账户余额
  const nineBalance = await balanceService.getBalanceByAssetType(apiKey, 'NINE');
  console.log('NINE余额:', nineBalance);

  const usdtBalance = await balanceService.getBalanceByAssetType(apiKey, 'USDT');
  console.log('USDT余额:', usdtBalance);

  // 2. 批量获取余额
  const nineBalances = await balanceService.getBatchBalancesByAssetType(apiKeys, 'NINE');
  console.log('批量NINE余额:', nineBalances);

  const usdtBalances = await balanceService.getBatchBalancesByAssetType(apiKeys, 'USDT');
  console.log('批量USDT余额:', usdtBalances);

  // 3. 强制刷新
  const freshNineBalance = await balanceService.getBalanceByAssetType(apiKey, 'NINE', true);
  console.log('刷新后的NINE余额:', freshNineBalance);

  const freshUsdtBalance = await balanceService.getBalanceByAssetType(apiKey, 'USDT', true);
  console.log('刷新后的USDT余额:', freshUsdtBalance);
}

/**
 * 动态代币类型处理示例
 */
export async function dynamicTokenTypeExamples() {
  console.log('=== 动态代币类型处理示例 ===');

  const tokenTypes: AssetType[] = ['NINE', 'USDT'];

  // 遍历不同代币类型
  for (const tokenType of tokenTypes) {
    console.log(`\n--- ${tokenType} 代币处理 ---`);

    // 获取账户列表
    const accounts = await UserService.getAccountsWithBalance(tokenType);
    console.log(`${tokenType}账户数量:`, accounts.count);

    // 获取单个账户余额
    const balance = await balanceService.getBalanceByAssetType(apiKey, tokenType);
    console.log(`${tokenType}余额:`, balance);

    // 批量获取余额
    const balances = await balanceService.getBatchBalancesByAssetType(apiKeys, tokenType);
    console.log(`批量${tokenType}余额:`, balances);

    // 同步余额
    const syncedBalance = await balanceService.syncBalance(apiKey, tokenType);
    console.log(`同步后的${tokenType}余额:`, syncedBalance);
  }
}

/**
 * 同步方法示例
 */
export async function syncExamples() {
  // 1. 同步NINE余额
  const syncedNineBalance = await balanceService.syncBalance(apiKey, 'NINE');
  console.log('同步后的NINE余额:', syncedNineBalance);

  // 2. 同步USDT余额
  const syncedUsdtBalance = await balanceService.syncBalance(apiKey, 'USDT');
  console.log('同步后的USDT余额:', syncedUsdtBalance);

  // 3. 批量同步NINE余额
  const syncedNineBalances = await balanceService.syncBatchBalances(apiKeys, 'NINE');
  console.log('批量同步后的NINE余额:', syncedNineBalances);

  // 4. 批量同步USDT余额
  const syncedUsdtBalances = await balanceService.syncBatchBalances(apiKeys, 'USDT');
  console.log('批量同步后的USDT余额:', syncedUsdtBalances);
}

/**
 * 实际业务场景示例
 */
export async function businessScenarioExamples() {
  console.log('=== 实际业务场景示例 ===');

  // 场景1: 内盘交易 - 需要USDT余额
  console.log('--- 内盘交易场景 ---');
  const usdtAccounts = await UserService.getAccountsWithBalance('USDT');
  const availableUsdtAccounts = usdtAccounts.data.filter((account: UserWithBalance) =>
    account.hasBalance && (account.balance || 0) > 10
  );
  console.log(`可用于内盘交易的账户数量: ${availableUsdtAccounts.length}`);

  // 场景2: 随机点亮 - 需要NINE余额
  console.log('--- 随机点亮场景 ---');
  const nineAccounts = await UserService.getAccountsWithBalance('NINE');
  const availableNineAccounts = nineAccounts.data.filter((account: UserWithBalance) =>
    account.hasBalance && (account.balance || 0) > 1
  );
  console.log(`可用于随机点亮的账户数量: ${availableNineAccounts.length}`);

  // 场景3: 批量余额检查
  console.log('--- 批量余额检查场景 ---');
  const selectedApiKeys = availableNineAccounts.slice(0, 5).map(acc => acc.apiKey);
  const balances = await balanceService.getBatchBalancesByAssetType(selectedApiKeys, 'NINE');
  console.log('批量余额检查结果:', balances);
}

/**
 * 错误处理示例
 */
export async function errorHandlingExamples() {
  console.log('=== 错误处理示例 ===');

  try {
    // 正常调用
    const accounts = await UserService.getAccountsWithBalance('NINE');
    console.log('成功获取账户:', accounts.success);
  } catch (error) {
    console.error('获取账户失败:', error);
  }

  try {
    // 缓存服务调用
    const balance = await balanceService.getBalanceByAssetType('invalid-key', 'NINE');
    console.log('余额查询结果:', balance); // 应该返回 0
  } catch (error) {
    console.error('余额查询失败:', error);
  }
}

/**
 * 运行所有示例
 */
export async function runAllOptimizedExamples() {
  console.log('=== 优化后的服务使用示例 ===');
  
  await userServiceExamples();
  await balanceCacheServiceExamples();
  await dynamicTokenTypeExamples();
  await syncExamples();
  await businessScenarioExamples();
  await errorHandlingExamples();
  
  console.log('\n=== 所有示例执行完成 ===');
}
