import { useState, useCallback, useRef } from 'react';
import type { FormInstance } from 'antd';
import type { EnhancedBatchLightResult } from '../services/enhancedBatchLightService';


export interface RefreshProgress {
  step: string;
  progress: number;
  total: number;
  isComplete: boolean;
}

interface UsePageRefreshOptions {
  form: FormInstance;
  onSmartFilter: () => Promise<void>;
  onForceRefreshBalances: () => Promise<void>;
  onShowSuccess: (message: string) => void;
  onShowError: (error: unknown, message: string) => void;
}

/**
 * 页面刷新Hook - 批量点亮完成后的自动刷新机制
 * 提供渐进式刷新策略和进度指示
 */
export function usePageRefresh({
  form,
  onSmartFilter,
  onForceRefreshBalances,
  onShowSuccess,
  onShowError,
}: UsePageRefreshOptions) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshProgress, setRefreshProgress] = useState<RefreshProgress>({
    step: '',
    progress: 0,
    total: 0,
    isComplete: false,
  });
  
  const isMountedRef = useRef(true);

  // 更新刷新进度
  const updateProgress = useCallback((step: string, progress: number, total: number) => {
    if (!isMountedRef.current) return;
    
    setRefreshProgress({
      step,
      progress,
      total,
      isComplete: progress >= total,
    });
  }, []);

  // 移除已完成点亮的账户
  const removeCompletedAccounts = useCallback((batchResult: EnhancedBatchLightResult) => {
    try {
      const currentAccounts = form.getFieldValue('accounts') || '';
      if (!currentAccounts.trim()) return;

      // 提取成功点亮的API Keys
      const successfulApiKeys = new Set<string>();
      batchResult.batches.forEach(batch => {
        batch.results.forEach(result => {
          if (result.status === 'success') {
            successfulApiKeys.add(result.apiKey);
          }
        });
      });

      if (successfulApiKeys.size === 0) return;

      // 解析当前账户信息
      const accountLines = currentAccounts.split('\n').filter((line: string) => line.trim());
      const remainingAccounts = accountLines.filter((line: string) => {
        const parts = line.split(',');
        if (parts.length >= 2) {
          const apiKey = parts[0].trim();
          return !successfulApiKeys.has(apiKey);
        }
        return true;
      });

      // 更新表单中的账户信息
      const newAccountsText = remainingAccounts.join('\n');
      form.setFieldsValue({ accounts: newAccountsText });

      console.log(`已移除 ${successfulApiKeys.size} 个成功点亮的账户，剩余 ${remainingAccounts.length} 个账户`);
      
      return {
        removedCount: successfulApiKeys.size,
        remainingCount: remainingAccounts.length,
      };
    } catch (error) {
      console.error('移除已完成账户失败:', error);
      return null;
    }
  }, [form]);

  // 执行完整的页面刷新流程
  const executeFullRefresh = useCallback(async (batchResult: EnhancedBatchLightResult) => {
    if (!isMountedRef.current || isRefreshing) {
      console.log('页面刷新已在进行中或组件已卸载');
      return;
    }

    setIsRefreshing(true);
    console.log('开始执行批量点亮完成后的页面刷新流程');

    try {
      // 步骤1: 移除已完成点亮的账户
      updateProgress('移除已完成账户', 1, 4);
      const removeResult = removeCompletedAccounts(batchResult);
      
      if (removeResult) {
        console.log(`账户移除完成: 移除${removeResult.removedCount}个，剩余${removeResult.remainingCount}个`);
      }

      // 步骤2: 强制刷新余额数据
      updateProgress('刷新余额数据', 2, 4);
      await onForceRefreshBalances();
      console.log('余额数据刷新完成');

      // 步骤3: 重新执行智能筛选（如果有剩余账户）
      updateProgress('重新筛选账户', 3, 4);
      const currentAccounts = form.getFieldValue('accounts') || '';
      if (currentAccounts.trim()) {
        await onSmartFilter();
        console.log('账户重新筛选完成');
      } else {
        console.log('无剩余账户，跳过重新筛选');
      }

      // 步骤4: 完成刷新
      updateProgress('刷新完成', 4, 4);
      
      // 显示成功消息
      const successMessage = removeResult 
        ? `页面刷新完成！已移除 ${removeResult.removedCount} 个成功点亮的账户，剩余 ${removeResult.remainingCount} 个账户`
        : '页面刷新完成！';
      
      onShowSuccess(successMessage);
      
      console.log('页面刷新流程完成');

    } catch (error) {
      console.error('页面刷新失败:', error);
      onShowError(error, '页面刷新失败，请手动刷新页面');
    } finally {
      if (isMountedRef.current) {
        setIsRefreshing(false);
        // 延迟清除进度信息
        setTimeout(() => {
          if (isMountedRef.current) {
            setRefreshProgress({
              step: '',
              progress: 0,
              total: 0,
              isComplete: false,
            });
          }
        }, 2000);
      }
    }
  }, [
    isRefreshing,
    form,
    removeCompletedAccounts,
    onForceRefreshBalances,
    onSmartFilter,
    onShowSuccess,
    onShowError,
    updateProgress,
  ]);

  // 手动触发页面刷新
  const manualRefresh = useCallback(async () => {
    if (!isMountedRef.current || isRefreshing) return;

    setIsRefreshing(true);
    console.log('开始手动页面刷新');

    try {
      updateProgress('刷新余额数据', 1, 2);
      await onForceRefreshBalances();

      updateProgress('重新筛选账户', 2, 2);
      const currentAccounts = form.getFieldValue('accounts') || '';
      if (currentAccounts.trim()) {
        await onSmartFilter();
      }

      onShowSuccess('手动刷新完成！');
    } catch (error) {
      console.error('手动刷新失败:', error);
      onShowError(error, '手动刷新失败');
    } finally {
      if (isMountedRef.current) {
        setIsRefreshing(false);
        setTimeout(() => {
          if (isMountedRef.current) {
            setRefreshProgress({
              step: '',
              progress: 0,
              total: 0,
              isComplete: false,
            });
          }
        }, 1000);
      }
    }
  }, [
    isRefreshing,
    form,
    onForceRefreshBalances,
    onSmartFilter,
    onShowSuccess,
    onShowError,
    updateProgress,
  ]);

  // 组件卸载时清理
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  return {
    isRefreshing,
    refreshProgress,
    executeFullRefresh,
    manualRefresh,
    cleanup,
  };
}
