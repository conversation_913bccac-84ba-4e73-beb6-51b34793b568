import { useState, useEffect, useCallback, useRef } from 'react';
import { BalanceCacheService } from '../services/balanceCacheService';
import type { EnhancedBatchLightResult } from '../services/enhancedBatchLightService';
import { useBaseHook } from './base/useBaseHook';

// 余额更新配置接口
interface BalanceUpdateConfig {
  enableAutoUpdate: boolean;
  updateInterval: number; // 更新间隔（毫秒）
  enableEstimation: boolean; // 是否启用本地估算
  syncAfterBatch: boolean; // 批次完成后是否同步
}

// 余额状态接口
interface BalanceState {
  balances: Map<string, number>;
  loading: boolean;
  lastUpdated: Date | null;
  estimatedCount: number;
  realCount: number;
}

/**
 * 余额自动更新Hook
 * 提供余额的自动更新、本地缓存计算和混合更新策略
 */
export const useBalanceAutoUpdate = (
  apiKeys: string[],
  config: Partial<BalanceUpdateConfig> = {}
) => {
  const defaultConfig: BalanceUpdateConfig = {
    enableAutoUpdate: true,
    updateInterval: 30000, // 30秒
    enableEstimation: true,
    syncAfterBatch: true,
  };

  const finalConfig = { ...defaultConfig, ...config };
  const balanceService = BalanceCacheService.getInstance();

  // 定时器引用和组件状态跟踪
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);
  const isUpdatingRef = useRef(false);

  // 使用基础Hook
  const baseHook = useBaseHook({
    enableAutoRefresh: finalConfig.enableAutoUpdate,
    refreshInterval: finalConfig.updateInterval,
    showErrorMessage: true,
    showSuccessMessage: false,
  });

  // 余额特定状态
  const [balanceState, setBalanceState] = useState<BalanceState>({
    balances: new Map(),
    loading: false,
    lastUpdated: null,
    estimatedCount: 0,
    realCount: 0,
  });

  // 安全的状态更新函数
  const safeSetBalanceState = useCallback((newState: Partial<BalanceState>) => {
    if (isMountedRef.current && !isUpdatingRef.current) {
      setBalanceState(prev => ({ ...prev, ...newState }));
    }
  }, []);

  // 更新余额状态
  const updateBalanceState = useCallback(async (forceRefresh = false) => {
    if (apiKeys.length === 0 || !isMountedRef.current || isUpdatingRef.current) return;

    isUpdatingRef.current = true;

    try {
      return await baseHook.safeExecute(
        async () => {
          const balances = await balanceService.getBatchBalancesByAssetType(apiKeys, 'NINE', forceRefresh);
          const stats = balanceService.getCacheStats();

          safeSetBalanceState({
            balances,
            loading: false,
            lastUpdated: new Date(),
            estimatedCount: stats.estimatedCount,
            realCount: stats.realCount,
          });

          return balances;
        },
        '更新余额状态',
        { showLoading: true }
      );
    } finally {
      isUpdatingRef.current = false;
    }
  }, [apiKeys, balanceService, baseHook, safeSetBalanceState]);

  // 记录点亮操作
  const recordLightOperation = useCallback((
    apiKey: string,
    amount: number,
    batchId: string
  ) => {
    if (finalConfig.enableEstimation) {
      balanceService.recordLightOperation(apiKey, amount, batchId);
      // 立即更新本地状态
      setBalanceState(prev => {
        const newBalances = new Map(prev.balances);
        const currentBalance = newBalances.get(apiKey) || 0;
        const newBalance = Math.max(0, currentBalance - amount);
        newBalances.set(apiKey, newBalance);

        return {
          ...prev,
          balances: newBalances,
          estimatedCount: prev.estimatedCount + 1,
        };
      });
    }
  }, [balanceService, finalConfig.enableEstimation]);

  // 批量记录点亮操作
  const recordBatchLightOperations = useCallback((
    operations: Array<{ apiKey: string; amount: number }>,
    batchId: string
  ) => {
    if (finalConfig.enableEstimation) {
      operations.forEach(({ apiKey, amount }) => {
        balanceService.recordLightOperation(apiKey, amount, batchId);
      });

      // 批量更新本地状态
      setBalanceState(prev => {
        const newBalances = new Map(prev.balances);
        let estimatedCount = prev.estimatedCount;

        operations.forEach(({ apiKey, amount }) => {
          const currentBalance = newBalances.get(apiKey) || 0;
          const newBalance = Math.max(0, currentBalance - amount);
          newBalances.set(apiKey, newBalance);
          estimatedCount++;
        });

        return {
          ...prev,
          balances: newBalances,
          estimatedCount,
        };
      });
    }
  }, [balanceService, finalConfig.enableEstimation]);

  // 处理批次完成事件（增强版本）
  const handleBatchComplete = useCallback(async (
    batchResult: EnhancedBatchLightResult
  ) => {
    if (!isMountedRef.current || isUpdatingRef.current) {
      console.log('组件已卸载或正在更新，跳过批次完成处理');
      return;
    }

    console.log('开始处理批次完成事件:', batchResult.batchId);

    if (finalConfig.syncAfterBatch) {
      try {
        // 提取所有涉及的API Keys
        const involvedApiKeys = new Set<string>();
        let totalSuccessfulOperations = 0;

        batchResult.batches.forEach(batch => {
          batch.results.forEach(result => {
            if (result.status === 'success') {
              involvedApiKeys.add(result.apiKey);
              totalSuccessfulOperations++;
            }
          });
        });

        console.log(`批次完成统计: 涉及${involvedApiKeys.size}个账户, ${totalSuccessfulOperations}次成功操作`);

        // 同步这些账户的余额
        if (involvedApiKeys.size > 0) {
          const apiKeysArray = Array.from(involvedApiKeys);

          // 清除相关账户的缓存
          apiKeysArray.forEach(apiKey => {
            balanceService.clearAccountCache(apiKey);
          });

          // 强制刷新余额数据
          console.log('强制刷新余额数据...');
          await updateBalanceState(true); // 强制刷新，不使用缓存

          console.log('批次完成后余额数据刷新完成');
        }
      } catch (error) {
        console.error('批次完成处理失败:', error);
      }
    }
  }, [finalConfig.syncAfterBatch, balanceService, updateBalanceState]);

  // 强制刷新余额
  const forceRefresh = useCallback(async () => {
    await updateBalanceState(true);
  }, [updateBalanceState]);

  // 获取单个账户余额
  const getBalance = useCallback((apiKey: string): number => {
    return balanceState.balances.get(apiKey) || 0;
  }, [balanceState.balances]);

  // 获取余额状态（实际值或估算值）
  const getBalanceStatus = useCallback((apiKey: string): {
    balance: number;
    isEstimated: boolean;
    lastOperations: Array<{ amount: number; timestamp: number; batchId: string }>;
  } => {
    const balance = getBalance(apiKey);
    const operations = balanceService.getLightOperations(apiKey);
    const recentOperations = operations.slice(-5); // 最近5次操作

    return {
      balance,
      isEstimated: operations.length > 0,
      lastOperations: recentOperations,
    };
  }, [getBalance, balanceService]);

  // 估算批量点亮的影响
  const estimateBatchImpact = useCallback((
    operations: Array<{ apiKey: string; amount: number }>
  ) => {
    return balanceService.estimateBatchLightImpact(operations);
  }, [balanceService]);

  // 启动自动更新
  const startAutoUpdate = useCallback(() => {
    if (updateTimerRef.current) {
      clearInterval(updateTimerRef.current);
      updateTimerRef.current = null;
    }

    if (finalConfig.enableAutoUpdate && isMountedRef.current) {
      updateTimerRef.current = setInterval(() => {
        if (isMountedRef.current && !isUpdatingRef.current) {
          updateBalanceState(false);
        }
      }, finalConfig.updateInterval);
    }
  }, [finalConfig.enableAutoUpdate, finalConfig.updateInterval, updateBalanceState]);

  // 停止自动更新
  const stopAutoUpdate = useCallback(() => {
    if (updateTimerRef.current) {
      clearInterval(updateTimerRef.current);
      updateTimerRef.current = null;
    }
    isUpdatingRef.current = false;
  }, []);

  // 清理缓存
  const clearCache = useCallback(() => {
    balanceService.clearCache();
    setBalanceState({
      balances: new Map(),
      loading: false,
      lastUpdated: null,
      estimatedCount: 0,
      realCount: 0,
    });
  }, [balanceService]);

  // 初始化和清理
  useEffect(() => {
    updateBalanceState(false);
    startAutoUpdate();

    return () => {
      isMountedRef.current = false;
      stopAutoUpdate();
    };
  }, [updateBalanceState, startAutoUpdate, stopAutoUpdate]);

  // 监听apiKeys变化
  useEffect(() => {
    if (apiKeys.length > 0) {
      updateBalanceState(false);
    }
  }, [apiKeys, updateBalanceState]);

  return {
    // 状态
    balances: balanceState.balances,
    loading: balanceState.loading,
    lastUpdated: balanceState.lastUpdated,
    estimatedCount: balanceState.estimatedCount,
    realCount: balanceState.realCount,

    // 方法
    getBalance,
    getBalanceStatus,
    recordLightOperation,
    recordBatchLightOperations,
    handleBatchComplete,
    forceRefresh,
    estimateBatchImpact,
    startAutoUpdate,
    stopAutoUpdate,
    clearCache,

    // 配置
    config: finalConfig,
  };
};
