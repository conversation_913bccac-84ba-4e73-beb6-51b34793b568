import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * SSE连接状态类型
 */
export type SSEConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

/**
 * SSE连接配置
 */
export interface SSEConnectionConfig {
  /** 创建EventSource的工厂函数 */
  createEventSource: () => EventSource;
  /** 解析事件数据的函数 */
  parseEvent?: (event: MessageEvent) => any;
  /** 是否在组件挂载时自动连接 */
  autoConnect?: boolean;
  /** 重连延迟时间（毫秒） */
  reconnectDelay?: number;
  /** 最大重连次数，-1为无限重连 */
  maxReconnectAttempts?: number;
}

/**
 * SSE连接返回值
 */
export interface SSEConnectionResult {
  /** 连接状态 */
  connectionStatus: SSEConnectionStatus;
  /** 手动连接 */
  connect: () => void;
  /** 断开连接 */
  disconnect: () => void;
  /** 重连次数 */
  reconnectCount: number;
}

/**
 * 通用SSE连接管理Hook
 * 基于RandomLightPage的成功实现，提供统一的SSE连接管理
 */
export const useSSEConnection = (
  config: SSEConnectionConfig,
  onEvent?: (event: any) => void,
  onDataRefresh?: () => void
): SSEConnectionResult => {
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<SSEConnectionStatus>('disconnected');
  const [reconnectCount, setReconnectCount] = useState(0);
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    createEventSource,
    parseEvent,
    autoConnect = true,
    reconnectDelay = 5000,
    maxReconnectAttempts = -1,
  } = config;

  const clearReconnectTimer = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }
  }, []);

  const connect = useCallback(() => {
    console.log('🔌 开始连接SSE...');
    
    // 清除重连定时器
    clearReconnectTimer();

    // 关闭现有连接
    if (eventSource) {
      console.log('🔌 关闭现有SSE连接');
      eventSource.close();
      setEventSource(null);
    }

    setConnectionStatus('connecting');

    try {
      const es = createEventSource();
      console.log('🔌 创建EventSource成功');
      
      es.onopen = () => {
        console.log('🔌 SSE连接已打开');
        setConnectionStatus('connected');
        setReconnectCount(0); // 连接成功后重置重连计数
      };

      es.onmessage = (event) => {
        console.log('📨 收到SSE消息:', event.data);
        try {
          let parsedEvent;
          
          // 如果提供了解析函数，则使用它解析事件
          if (parseEvent) {
            parsedEvent = parseEvent(event);
          } else {
            // 默认解析JSON数据
            try {
              // 首先解析外层的MessageEvent数据
              const messageData = JSON.parse(event.data);
              
              // 如果是包装的事件格式 {type: "message", data: "..."} 
              if (messageData.type === 'message' && typeof messageData.data === 'string') {
                // 再次解析内层的实际事件数据
                parsedEvent = JSON.parse(messageData.data);
              } else {
                // 直接使用解析的数据
                parsedEvent = messageData;
              }
            } catch {
              // 如果不是JSON，使用原始事件
              parsedEvent = event;
            }
          }
          
          if (parsedEvent && onEvent) {
            onEvent(parsedEvent);
          }
        } catch (error) {
          console.error('📨 解析SSE事件失败:', error);
        }
      };

      es.onerror = (error) => {
        console.error('🔌 SSE连接错误:', error);
        setConnectionStatus('error');
        
        // 连接断开时刷新数据（兜底机制）
        if (onDataRefresh) {
          console.log('🔄 SSE连接断开，触发数据刷新');
          onDataRefresh();
        }
        
        // 检查是否需要重连
        const shouldReconnect = maxReconnectAttempts === -1 || reconnectCount < maxReconnectAttempts;
        
        if (shouldReconnect && es.readyState === EventSource.CLOSED) {
          console.log(`🔌 ${reconnectDelay}ms后尝试第${reconnectCount + 1}次重连`);
          setReconnectCount(prev => prev + 1);
          
          reconnectTimerRef.current = setTimeout(() => {
            connect();
          }, reconnectDelay);
        } else if (!shouldReconnect) {
          console.warn('🔌 达到最大重连次数，停止重连');
          setConnectionStatus('disconnected');
        }
      };

      setEventSource(es);
    } catch (error) {
      console.error('🔌 创建EventSource失败:', error);
      setConnectionStatus('error');
    }
  }, [
    createEventSource, 
    parseEvent, 
    onEvent, 
    onDataRefresh, 
    eventSource, 
    reconnectDelay, 
    maxReconnectAttempts, 
    reconnectCount,
    clearReconnectTimer
  ]);

  const disconnect = useCallback(() => {
    console.log('🔌 手动断开SSE连接');
    clearReconnectTimer();
    
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
    
    setConnectionStatus('disconnected');
    setReconnectCount(0);
  }, [eventSource, clearReconnectTimer]);

  // 组件挂载时自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    // 组件卸载时清理
    return () => {
      disconnect();
    };
  }, [autoConnect]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    connectionStatus,
    connect,
    disconnect,
    reconnectCount,
  };
};

/**
 * 专门用于随机池交易的SSE连接Hook
 * 封装了随机池交易特定的配置和事件处理
 */
export const useRandomPoolTradeSSE = (
  onEvent?: (event: any) => void,
  onDataRefresh?: () => void
) => {
  return useSSEConnection(
    {
      createEventSource: () => {
        // 动态导入服务以避免循环依赖
        const { RandomPoolTradeService } = require('../services/randomPoolTradeService');
        return RandomPoolTradeService.createEventSource();
      },
      parseEvent: (event: MessageEvent) => {
        try {
          return JSON.parse(event.data);
        } catch (error) {
          console.warn('解析随机池交易事件失败:', error);
          return null;
        }
      },
      autoConnect: true,
      reconnectDelay: 5000,
      maxReconnectAttempts: -1, // 无限重连
    },
    onEvent,
    onDataRefresh
  );
};