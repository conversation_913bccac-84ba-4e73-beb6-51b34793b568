import { useCallback } from 'react';
import type { FormInstance } from 'antd';
import type { BatchConfigAnalysis } from './useLivePreview';
import type { FormValues } from '../types/formTypes';

interface UseBatchConfigSuggestionProps {
  form: FormInstance<FormValues>;
  batchConfigAnalysis: BatchConfigAnalysis | null;
  showSuccess: (message: string) => void;
}

/**
 * 批次配置建议Hook
 * 处理批次配置的智能建议和应用逻辑
 */
export const useBatchConfigSuggestion = ({
  form,
  batchConfigAnalysis,
  showSuccess,
}: UseBatchConfigSuggestionProps) => {

  // 应用批次配置建议
  const applyBatchConfigSuggestion = useCallback(() => {
    if (!batchConfigAnalysis) return;

    const { effectiveAccounts } = batchConfigAnalysis;
    let suggestedBatchSize: number;

    if (batchConfigAnalysis.batchSize >= effectiveAccounts && effectiveAccounts > 1) {
      suggestedBatchSize = Math.max(1, Math.floor(effectiveAccounts / 2));
    } else if (batchConfigAnalysis.batchCount === 1 && effectiveAccounts > 10) {
      suggestedBatchSize = Math.max(1, Math.floor(effectiveAccounts / 3));
    } else if (batchConfigAnalysis.batchCount > 20) {
      suggestedBatchSize = Math.max(1, Math.ceil(effectiveAccounts / 10));
    } else {
      return; // 没有建议
    }

    form.setFieldsValue({ batchSize: suggestedBatchSize });
    showSuccess(`已将批次大小调整为 ${suggestedBatchSize}`);
  }, [form, batchConfigAnalysis, showSuccess]);

  return {
    applyBatchConfigSuggestion,
  };
};
