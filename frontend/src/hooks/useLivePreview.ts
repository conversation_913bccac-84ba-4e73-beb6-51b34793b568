import { useState, useEffect, useCallback } from 'react';
import type { FormInstance } from 'antd';
import { SymbolService } from '../services/symbolService';

export interface LivePreviewSummary {
  totalAccountCount: number;
  effectiveAccountCount: number;
  estimatedTotalAmount: number;
  amountRange: string;
  isLimited: boolean;
  // 新增：当前点亮和预计点亮统计
  currentLightedCount?: number;
  estimatedAfterLightCount?: number;
  lightingIncrement?: number;
}

export interface BatchConfigAnalysis {
  totalAccounts: number;
  effectiveAccounts: number;
  batchCount: number;
  batchSize: number;
  suggestion?: string;
  warning?: string;
}

interface FormValues {
  symbol: string;
  assetId: string;
  accounts: string;
  amount?: number;
  enableRandomAmount: boolean;
  randomMinAmount?: number;
  randomMaxAmount?: number;
  enableBatchConfig: boolean;
  batchSize?: number;
  intervalMs?: number;
  enableCustomLightCount: boolean;
  customLightCount?: number;
}

/**
 * 实时预览Hook - 核心业务功能
 * 提供账户解析预览和批次配置分析
 */
export function useLivePreview(
  form: FormInstance<FormValues>,
  getAccountCount: (accounts: string) => number,
  _smartFilterResult?: { totalAccounts: number; accountsWithBalance: number; filteredAccounts: string } | null,
  selectedAssetId?: number,
  externalAssetLightNum?: number // 新增：外部传入的点亮数量
) {
  const [livePreviewSummary, setLivePreviewSummary] = useState<LivePreviewSummary>({
    totalAccountCount: 0,
    effectiveAccountCount: 0,
    estimatedTotalAmount: 0,
    amountRange: '0',
    isLimited: false,
    currentLightedCount: 0,
    estimatedAfterLightCount: 0,
    lightingIncrement: 0,
  });

  const [batchConfigAnalysis, setBatchConfigAnalysis] = useState<BatchConfigAnalysis>({
    totalAccounts: 0,
    effectiveAccounts: 0,
    batchCount: 0,
    batchSize: 50,
  });

  // 当前Asset ID的点亮状态
  const [currentAssetLightNum, setCurrentAssetLightNum] = useState<number>(0);

  // 当selectedAssetId变化时，查询当前点亮数量
  useEffect(() => {
    if (selectedAssetId) {
      SymbolService.getAssetLightStatus(selectedAssetId)
        .then(response => {
          if (response.code === 200 && response.data?.lightNum !== undefined) {
            setCurrentAssetLightNum(response.data.lightNum);
          }
        })
        .catch(error => {
          console.error('查询Asset点亮状态失败:', error);
          setCurrentAssetLightNum(0);
        });
    } else {
      setCurrentAssetLightNum(0);
    }
  }, [selectedAssetId]);

  // 当外部传入点亮数量时，优先使用外部数据
  useEffect(() => {
    if (externalAssetLightNum !== undefined) {
      setCurrentAssetLightNum(externalAssetLightNum);
    }
  }, [externalAssetLightNum]);

  // 手动刷新点亮数量
  const refreshAssetLightNum = useCallback(async (assetId?: number) => {
    const targetAssetId = assetId || selectedAssetId;
    if (!targetAssetId) return null;

    try {
      const response = await SymbolService.getAssetLightStatus(targetAssetId);
      if (response.code === 200 && response.data?.lightNum !== undefined) {
        setCurrentAssetLightNum(response.data.lightNum);
        return response.data.lightNum;
      }
      return null;
    } catch (error) {
      console.error('刷新Asset点亮数量失败:', error);
      return null;
    }
  }, [selectedAssetId]);

  // 计算预览摘要
  const calculatePreviewSummary = useCallback((formValues: FormValues): LivePreviewSummary => {
    const accounts = formValues.accounts || '';
    const totalAccountCount = getAccountCount(accounts);
    
    // 计算有效账户数（考虑自定义点亮数量限制）
    let effectiveAccountCount = totalAccountCount;
    let isLimited = false;
    
    if (formValues.enableCustomLightCount && formValues.customLightCount) {
      if (formValues.customLightCount < totalAccountCount) {
        effectiveAccountCount = formValues.customLightCount;
        isLimited = true;
      }
    }

    // 计算预估总金额和金额范围
    let estimatedTotalAmount = 0;
    let amountRange = '0';

    if (formValues.enableRandomAmount && formValues.randomMinAmount && formValues.randomMaxAmount) {
      const minAmount = formValues.randomMinAmount;
      const maxAmount = formValues.randomMaxAmount;
      const avgAmount = (minAmount + maxAmount) / 2;
      estimatedTotalAmount = effectiveAccountCount * avgAmount;
      amountRange = `${minAmount} - ${maxAmount}`;
    } else if (formValues.amount) {
      estimatedTotalAmount = effectiveAccountCount * formValues.amount;
      amountRange = formValues.amount.toString();
    }

    // 计算当前点亮状态（基于Asset ID的实际lightNum）
    const currentLightedCount = currentAssetLightNum;

    // 计算实际可以点亮的数量（考虑999的最大限制）
    const maxPossibleLights = 999;
    const remainingLightCapacity = Math.max(0, maxPossibleLights - currentLightedCount);
    const actualLightingIncrement = Math.min(effectiveAccountCount, remainingLightCapacity);
    const estimatedAfterLightCount = currentLightedCount + actualLightingIncrement;

    return {
      totalAccountCount,
      effectiveAccountCount,
      estimatedTotalAmount,
      amountRange,
      isLimited,
      currentLightedCount,
      estimatedAfterLightCount,
      lightingIncrement: actualLightingIncrement,
    };
  }, [getAccountCount, currentAssetLightNum]);

  // 计算批次配置分析
  const calculateBatchConfigAnalysis = useCallback((formValues: FormValues): BatchConfigAnalysis => {
    const accounts = formValues.accounts || '';
    const totalAccounts = getAccountCount(accounts);
    
    // 计算有效账户数
    let effectiveAccounts = totalAccounts;
    if (formValues.enableCustomLightCount && formValues.customLightCount) {
      effectiveAccounts = Math.min(formValues.customLightCount, totalAccounts);
    }

    // 获取批次大小
    const batchSize = formValues.enableBatchConfig && formValues.batchSize 
      ? formValues.batchSize 
      : 50; // 默认批次大小

    // 计算批次数量
    const batchCount = effectiveAccounts > 0 ? Math.ceil(effectiveAccounts / batchSize) : 0;

    // 生成建议和警告
    let suggestion: string | undefined;
    let warning: string | undefined;

    if (effectiveAccounts === 0) {
      warning = '没有有效的账户数据';
    } else if (effectiveAccounts > 500) {
      if (batchSize < 50) {
        suggestion = '账户数量较多，建议增加批次大小到50以上以提高效率';
      } else if (batchSize > 100) {
        warning = '批次大小过大可能导致API超时，建议控制在100以内';
      }
    } else if (effectiveAccounts < 50 && batchSize > effectiveAccounts) {
      suggestion = `账户数量较少(${effectiveAccounts})，建议将批次大小调整为${effectiveAccounts}`;
    }

    // 批次间隔建议
    if (batchCount > 10 && !formValues.intervalMs) {
      if (!suggestion) suggestion = '';
      else suggestion += '；';
      suggestion += '批次数量较多，建议设置批次间隔避免API限流';
    }

    return {
      totalAccounts,
      effectiveAccounts,
      batchCount,
      batchSize,
      suggestion,
      warning,
    };
  }, [getAccountCount]);

  // 监听表单变化并更新预览
  useEffect(() => {
    const updatePreview = () => {
      try {
        const formValues = form.getFieldsValue();
        
        // 更新预览摘要
        const newPreviewSummary = calculatePreviewSummary(formValues);
        setLivePreviewSummary(newPreviewSummary);

        // 更新批次配置分析
        const newBatchAnalysis = calculateBatchConfigAnalysis(formValues);
        setBatchConfigAnalysis(newBatchAnalysis);
      } catch (error) {
        console.warn('预览计算失败:', error);
      }
    };

    // 初始计算
    updatePreview();

    // 使用定时器进行轻量级的表单监听
    const interval = setInterval(updatePreview, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [form, calculatePreviewSummary, calculateBatchConfigAnalysis]);

  return {
    livePreviewSummary,
    batchConfigAnalysis,
    refreshAssetLightNum,
    currentAssetLightNum,
  };
}
