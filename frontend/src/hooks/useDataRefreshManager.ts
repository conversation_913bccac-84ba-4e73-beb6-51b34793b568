import { useState, useCallback, useRef, useMemo } from 'react';
import type { FormInstance } from 'antd';
import { SymbolService } from '../services/symbolService';
import type { EnhancedBatchLightResult } from '../services/enhancedBatchLightService';

// 刷新状态接口
export interface RefreshStatus {
  isRefreshing: boolean;
  currentStep: string;
  progress: number;
  total: number;
  error?: string;
}

// 刷新配置接口
export interface RefreshConfig {
  refreshAssetLightNum: boolean;
  refreshAccountBalances: boolean;
  refreshLightableTokens: boolean;
  refreshSmartFilter: boolean;
  showProgress: boolean;
  autoRefreshAfterBatch: boolean;
}

// 刷新回调接口
export interface RefreshCallbacks {
  onAssetLightNumUpdate?: (assetId: number, lightNum: number) => void;
  onBalanceRefresh?: () => Promise<void>;
  onSmartFilterRefresh?: () => Promise<void>;
  onLightableTokensRefresh?: () => Promise<void>;
  onRefreshComplete?: (refreshedData: RefreshResult) => void;
  onRefreshError?: (error: Error, step: string) => void;
  onShowSuccess?: (message: string) => void;
  onShowError?: (error: Error, message: string) => void;
}

// 刷新结果接口
export interface RefreshResult {
  assetLightNum?: number;
  balancesRefreshed: boolean;
  smartFilterRefreshed: boolean;
  lightableTokensRefreshed: boolean;
  totalRefreshedItems: number;
  duration: number;
}

// 默认配置
const DEFAULT_CONFIG: RefreshConfig = {
  refreshAssetLightNum: true,
  refreshAccountBalances: true,
  refreshLightableTokens: true,
  refreshSmartFilter: true,
  showProgress: true,
  autoRefreshAfterBatch: true,
};

/**
 * 数据刷新管理Hook
 * 统一管理页面所有数据的刷新逻辑，确保批次完成后数据一致性
 */
export function useDataRefreshManager(
  form: FormInstance,
  selectedAssetId: number | null,
  config: Partial<RefreshConfig> = {},
  callbacks: RefreshCallbacks = {}
) {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  const isMountedRef = useRef(true);
  
  // 刷新状态
  const [refreshStatus, setRefreshStatus] = useState<RefreshStatus>({
    isRefreshing: false,
    currentStep: '',
    progress: 0,
    total: 0,
  });

  // 更新刷新状态
  const updateRefreshStatus = useCallback((
    step: string, 
    progress: number, 
    total: number, 
    error?: string
  ) => {
    if (!isMountedRef.current) return;
    
    setRefreshStatus({
      isRefreshing: progress < total,
      currentStep: step,
      progress,
      total,
      error,
    });
  }, []);

  // 刷新Asset点亮数量
  const refreshAssetLightNum = useCallback(async (assetId: number): Promise<number | null> => {
    try {
      const response = await SymbolService.getAssetLightStatus(assetId);
      if (response.code === 200 && response.data?.lightNum !== undefined) {
        const lightNum = response.data.lightNum;
        
        // 触发回调
        if (callbacks.onAssetLightNumUpdate) {
          callbacks.onAssetLightNumUpdate(assetId, lightNum);
        }
        
        return lightNum;
      }
      return null;
    } catch (error) {
      console.error('刷新Asset点亮数量失败:', error);
      throw error;
    }
  }, [callbacks]);

  // 执行完整的数据刷新
  const executeFullRefresh = useCallback(async (
    batchResult?: EnhancedBatchLightResult
  ): Promise<RefreshResult> => {
    if (!isMountedRef.current || refreshStatus.isRefreshing) {
      throw new Error('刷新已在进行中');
    }

    const startTime = Date.now();
    let refreshedItems = 0;
    const result: RefreshResult = {
      balancesRefreshed: false,
      smartFilterRefreshed: false,
      lightableTokensRefreshed: false,
      totalRefreshedItems: 0,
      duration: 0,
    };

    try {
      // 计算总步骤数
      const totalSteps = [
        finalConfig.refreshAssetLightNum && selectedAssetId,
        finalConfig.refreshAccountBalances,
        finalConfig.refreshSmartFilter,
        finalConfig.refreshLightableTokens,
      ].filter(Boolean).length;

      let currentStep = 0;

      // 步骤1: 刷新Asset点亮数量
      if (finalConfig.refreshAssetLightNum && selectedAssetId) {
        currentStep++;
        updateRefreshStatus('刷新点亮数量统计', currentStep, totalSteps);
        
        try {
          const lightNum = await refreshAssetLightNum(selectedAssetId);
          if (lightNum !== null) {
            result.assetLightNum = lightNum;
            refreshedItems++;
          }
        } catch (error) {
          console.error('刷新点亮数量失败:', error);
          if (callbacks.onRefreshError) {
            callbacks.onRefreshError(error as Error, '刷新点亮数量统计');
          }
        }
      }

      // 步骤2: 刷新账户余额
      if (finalConfig.refreshAccountBalances && callbacks.onBalanceRefresh) {
        currentStep++;
        updateRefreshStatus('刷新账户余额数据', currentStep, totalSteps);
        
        try {
          await callbacks.onBalanceRefresh();
          result.balancesRefreshed = true;
          refreshedItems++;
        } catch (error) {
          console.error('刷新余额数据失败:', error);
          if (callbacks.onRefreshError) {
            callbacks.onRefreshError(error as Error, '刷新账户余额数据');
          }
        }
      }

      // 步骤3: 刷新智能筛选
      if (finalConfig.refreshSmartFilter && callbacks.onSmartFilterRefresh) {
        currentStep++;
        updateRefreshStatus('重新筛选账户', currentStep, totalSteps);
        
        try {
          const currentAccounts = form.getFieldValue('accounts') || '';
          if (currentAccounts.trim()) {
            await callbacks.onSmartFilterRefresh();
            result.smartFilterRefreshed = true;
            refreshedItems++;
          }
        } catch (error) {
          console.error('重新筛选账户失败:', error);
          if (callbacks.onRefreshError) {
            callbacks.onRefreshError(error as Error, '重新筛选账户');
          }
        }
      }

      // 步骤4: 刷新可点亮Token列表
      if (finalConfig.refreshLightableTokens && callbacks.onLightableTokensRefresh) {
        currentStep++;
        updateRefreshStatus('刷新可点亮Token列表', currentStep, totalSteps);
        
        try {
          await callbacks.onLightableTokensRefresh();
          result.lightableTokensRefreshed = true;
          refreshedItems++;
        } catch (error) {
          console.error('刷新可点亮Token列表失败:', error);
          if (callbacks.onRefreshError) {
            callbacks.onRefreshError(error as Error, '刷新可点亮Token列表');
          }
        }
      }

      // 完成刷新
      result.totalRefreshedItems = refreshedItems;
      result.duration = Date.now() - startTime;
      
      updateRefreshStatus('数据刷新完成', totalSteps, totalSteps);
      
      // 触发完成回调
      if (callbacks.onRefreshComplete) {
        callbacks.onRefreshComplete(result);
      }

      // 显示成功消息
      if (callbacks.onShowSuccess && refreshedItems > 0) {
        const message = batchResult 
          ? `批次完成后数据刷新成功！已更新 ${refreshedItems} 项数据`
          : `数据刷新完成！已更新 ${refreshedItems} 项数据`;
        callbacks.onShowSuccess(message);
      }

      return result;

    } catch (error) {
      console.error('数据刷新失败:', error);
      updateRefreshStatus('数据刷新失败', 0, 0, (error as Error).message);
      
      if (callbacks.onShowError) {
        callbacks.onShowError(error as Error, '数据刷新失败，请手动刷新页面');
      }
      
      throw error;
    } finally {
      // 延迟清除状态
      setTimeout(() => {
        if (isMountedRef.current) {
          setRefreshStatus({
            isRefreshing: false,
            currentStep: '',
            progress: 0,
            total: 0,
          });
        }
      }, 2000);
    }
  }, [
    refreshStatus.isRefreshing,
    finalConfig,
    selectedAssetId,
    form,
    refreshAssetLightNum,
    callbacks,
    updateRefreshStatus,
  ]);

  // 批次完成后自动刷新
  const handleBatchComplete = useCallback(async (batchResult: EnhancedBatchLightResult) => {
    if (!finalConfig.autoRefreshAfterBatch) return;
    
    console.log('批次完成，开始自动数据刷新...');
    try {
      await executeFullRefresh(batchResult);
    } catch (error) {
      console.error('批次完成后自动刷新失败:', error);
    }
  }, [finalConfig.autoRefreshAfterBatch, executeFullRefresh]);

  // 手动刷新
  const manualRefresh = useCallback(async () => {
    console.log('开始手动数据刷新...');
    try {
      return await executeFullRefresh();
    } catch (error) {
      console.error('手动数据刷新失败:', error);
      throw error;
    }
  }, [executeFullRefresh]);

  // 清理函数
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  return {
    // 状态
    refreshStatus,
    isRefreshing: refreshStatus.isRefreshing,
    
    // 方法
    executeFullRefresh,
    handleBatchComplete,
    manualRefresh,
    refreshAssetLightNum,
    cleanup,
  };
}
