import { useCallback } from 'react';
import { BatchService } from '../services/batchService';
import {
  EnhancedBatchLightService,
  type EnhancedBatchLightRequest,
  type UserLightBean,
  type EnhancedBatchLightResult,
} from '../services/enhancedBatchLightService';
import type { FormValues } from '../types/formTypes';

interface UseFormSubmissionProps {
  parseUserBeanList: (accountsText: string) => UserLightBean[];
  selectedAssetId: number | null;
  setLoading: (loading: boolean) => void;
  setCurrentBatch: (batch: EnhancedBatchLightResult) => void;
  setBatchStatusModalVisible: (visible: boolean) => void;
  showError: (error: unknown, defaultMessage: string) => void;
  showSuccess: (message: string) => void;
  onBatchOperations?: (operations: Array<{ apiKey: string; amount: number }>, batchId: string) => void;
  onBatchComplete?: (batchResult: EnhancedBatchLightResult) => void;
}

/**
 * 表单提交逻辑Hook
 * 处理增强批量点亮和原生批量点亮的提交逻辑
 */
export const useFormSubmission = ({
  parseUserBeanList,
  selectedAssetId,
  setLoading,
  setCurrentBatch,
  setBatchStatusModalVisible,
  showError,
  showSuccess,
  onBatchOperations,
  onBatchComplete,
}: UseFormSubmissionProps) => {

  // 构建增强批量点亮请求
  const buildBatchLightRequest = useCallback((values: FormValues): EnhancedBatchLightRequest => {
    const userBeanList = parseUserBeanList(values.accounts);

    // 获取最终的资产ID - 优先使用Token搜索结果
    let finalAssetId: number;
    if (selectedAssetId) {
      finalAssetId = selectedAssetId;
    } else if (values.assetId) {
      const parsedAssetId = parseInt(values.assetId);
      if (isNaN(parsedAssetId) || parsedAssetId <= 0) {
        throw new Error('资产ID必须是有效的正整数');
      }
      finalAssetId = parsedAssetId;
    } else {
      throw new Error('请先搜索Token或直接输入资产ID');
    }

    // 构建基础请求对象
    const request: EnhancedBatchLightRequest = {
      userBeanList,
      assetId: finalAssetId,
      enableFirstLightAnalysis: true,
    };

    // 自定义账户数量配置
    if (values.enableCustomLightCount && values.customLightCount) {
      request.customLightCount = values.customLightCount;
    }

    // 点亮金额配置
    if (values.enableRandomAmount) {
      if (!values.randomMinAmount || !values.randomMaxAmount) {
        throw new Error('启用随机金额时，必须设置最小和最大金额');
      }
      if (values.randomMinAmount >= values.randomMaxAmount) {
        throw new Error('最小金额必须小于最大金额');
      }
      request.randomAmount = {
        enabled: true,
        minAmount: values.randomMinAmount,
        maxAmount: values.randomMaxAmount,
      };
    } else {
      const amount = values.amount || 1;
      if (amount <= 0) {
        throw new Error('固定金额必须是大于0的数值');
      }
      request.amount = amount;
    }

    // 批次配置
    if (values.enableBatchConfig) {
      const batchSize = values.batchSize || 50;
      const intervalMs = values.intervalMs || 1000;

      if (batchSize <= 0 || batchSize > 200) {
        throw new Error('批次大小必须在1-200之间');
      }
      if (intervalMs < 0 || intervalMs > 60000) {
        throw new Error('间隔时间必须在0-60000毫秒之间');
      }

      request.batchConfig = {
        batchSize,
        intervalMs,
      };
    }

    return request;
  }, [parseUserBeanList, selectedAssetId]);

  // 构建原生批量点亮请求（简化版本）
  const buildNativeBatchLightRequest = useCallback((values: FormValues) => {
    const userBeanList = parseUserBeanList(values.accounts);

    // 获取最终的资产ID
    let finalAssetId: number;
    if (selectedAssetId) {
      finalAssetId = selectedAssetId;
    } else if (values.assetId) {
      const parsedAssetId = parseInt(values.assetId);
      if (isNaN(parsedAssetId) || parsedAssetId <= 0) {
        throw new Error('资产ID必须是有效的正整数');
      }
      finalAssetId = parsedAssetId;
    } else {
      throw new Error('请先搜索Token或直接输入资产ID');
    }

    // 获取点亮金额
    let amount: number;
    if (values.enableRandomAmount) {
      if (!values.randomMinAmount || !values.randomMaxAmount) {
        throw new Error('启用随机金额时，必须设置最小和最大金额');
      }
      amount = (values.randomMinAmount + values.randomMaxAmount) / 2;
      console.warn('原生API不支持随机金额，使用平均值:', amount);
    } else {
      amount = values.amount || 1;
      if (amount <= 0) {
        throw new Error('固定金额必须是大于0的数值');
      }
    }

    // 限制账户数量
    let finalUserBeanList = userBeanList;
    if (values.enableCustomLightCount && values.customLightCount) {
      finalUserBeanList = userBeanList.slice(0, values.customLightCount);
    }

    return {
      userBeanList: finalUserBeanList,
      assetId: finalAssetId,
      amount: amount,
    };
  }, [parseUserBeanList, selectedAssetId]);

  // 提交处理
  const handleSubmit = useCallback(async (values: FormValues) => {
    try {
      setLoading(true);

      // 检查是否使用原生API（基于配置的复杂度）
      const shouldUseNativeAPI = !values.enableRandomAmount && !values.enableBatchConfig;

      if (shouldUseNativeAPI) {
        // 使用原生批量点亮API
        console.log('=== 使用原生批量点亮API ===');
        const nativeRequest = buildNativeBatchLightRequest(values);

        console.log('原生API请求参数:', JSON.stringify(nativeRequest, null, 2));
        console.log('选中的资产ID (来自Token搜索):', selectedAssetId);
        console.log('最终使用的资产ID:', nativeRequest.assetId);
        console.log('用户账户列表长度:', nativeRequest.userBeanList.length);

        // 调用原生批量点亮API
        const response = await BatchService.batchLight(nativeRequest);

        if (response.success) {
          showSuccess(`批量点亮完成：成功 ${response.summary.successful}，失败 ${response.summary.failed}`);
          // 可以在这里添加结果显示逻辑
        } else {
          showError(null, response.message || '批量点亮失败');
        }
      } else {
        // 使用增强版批量点亮API
        console.log('=== 使用增强版批量点亮API ===');
        const request = buildBatchLightRequest(values);

        // 详细调试日志
        console.log('原始表单值:', values);
        console.log('选中的资产ID (来自Token搜索):', selectedAssetId);
        console.log('表单中的资产ID:', values.assetId);
        console.log('最终使用的资产ID:', request.assetId);
        console.log('构建的请求对象:', JSON.stringify(request, null, 2));
        console.log('用户账户列表长度:', request.userBeanList.length);
        console.log('前3个账户示例:', request.userBeanList.slice(0, 3));

        // 验证请求
        const validationErrors = EnhancedBatchLightService.validateRequest(request);
        if (validationErrors.length > 0) {
          console.error('前端验证失败:', validationErrors);
          showError(null, validationErrors.join('; '));
          return;
        }

        console.log('前端验证通过，发送增强版API请求...');
        const response = await EnhancedBatchLightService.executeEnhancedBatchLight(request);

        if (response.code === 200) {
          setCurrentBatch(response.data);
          setBatchStatusModalVisible(true);
          showSuccess('增强点亮已启动');

          // 记录批量操作（用于余额估算）
          if (onBatchOperations) {
            const operations = request.userBeanList.map(user => ({
              apiKey: user.apiKey,
              amount: request.amount || 1, // 使用实际金额或默认值
            }));
            onBatchOperations(operations, response.data.batchId);
          }

          // 开始轮询状态（使用新的取消机制）
          const pollControl = EnhancedBatchLightService.pollBatchStatus(
            response.data.batchId,
            (updatedResult) => {
              if (updatedResult) {
                setCurrentBatch(updatedResult);

                // 如果批次完成，触发余额同步和数据刷新
                if (updatedResult.overallStatus === 'completed' && onBatchComplete) {
                  console.log('批次完成，触发数据刷新...');
                  onBatchComplete(updatedResult);

                  // 停止轮询
                  if (pollControl && pollControl.cancel) {
                    pollControl.cancel();
                  }
                }
              }
            }
          );

          // 存储轮询控制器以便后续取消（可选）
          if (pollControl && typeof pollControl === 'object' && 'cancel' in pollControl) {
            console.log('轮询已启动，可通过 pollControl.cancel() 取消');
          }
        } else {
          showError(null, response.msg);
        }
      }
    } catch (error) {
      showError(error, '批量点亮失败');
    } finally {
      setLoading(false);
    }
  }, [
    buildBatchLightRequest,
    buildNativeBatchLightRequest,
    selectedAssetId,
    setLoading,
    setCurrentBatch,
    setBatchStatusModalVisible,
    showError,
    showSuccess,
    onBatchComplete,
    onBatchOperations,
  ]);

  return {
    handleSubmit,
    buildBatchLightRequest,
    buildNativeBatchLightRequest,
  };
};
