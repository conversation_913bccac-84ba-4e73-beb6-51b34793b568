import { useState, useCallback } from 'react';
import type { FormInstance } from 'antd';
import { SymbolService, type SymbolConversionResult } from '../services/symbolService';
import { UserService, type UserWithBalance } from '../services/userService';
import type { EnhancedBatchLightResult } from '../services/enhancedBatchLightService';
import { useErrorHandler } from './useErrorHandler';

interface BalanceFilterResult {
  totalAccounts: number;
  accountsWithBalance: number;
  filteredAccounts: string;
}

/**
 * 批量操作Hook
 * 提供Symbol搜索、余额筛选、批量点亮等核心业务功能
 */
export const useBatchOperations = (form: FormInstance) => {
  const { showError, showSuccess, showWarning } = useErrorHandler();
  
  // 状态管理
  const [symbolLoading, setSymbolLoading] = useState(false);
  const [smartFilterLoading, setSmartFilterLoading] = useState(false);
  const [lightableTokensLoading, setLightableTokensLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const [symbolResult, setSymbolResult] = useState<SymbolConversionResult | null>(null);
  const [selectedAssetId, setSelectedAssetId] = useState<number | null>(null);
  const [smartFilterResult, setSmartFilterResult] = useState<BalanceFilterResult | null>(null);
  const [lightableTokens, setLightableTokens] = useState<Array<{
    symbol: string;
    assetId: number;
    lightNum: number;
  }> | null>(null);
  const [currentBatch, setCurrentBatch] = useState<EnhancedBatchLightResult | null>(null);

  // Symbol搜索处理
  const handleSymbolSearch = useCallback(async (symbol: string) => {
    if (!symbol.trim()) {
      showWarning('请输入Token符号');
      return;
    }

    setSymbolLoading(true);
    try {
      const response = await SymbolService.convertSymbolToAssetIds(symbol.trim());
      if (response.code === 200 && response.data.found) {
        setSymbolResult(response.data);

        // 如果有推荐选项，自动选择
        if (response.data.recommendedOption) {
          setSelectedAssetId(response.data.recommendedOption.assetId);
          form.setFieldsValue({ assetId: response.data.recommendedOption.assetId.toString() });
          showSuccess(`找到 ${response.data.totalOptions} 个选项，已自动选择推荐选项`);
        } else if (response.data.allOptions.length > 0) {
          showWarning(`找到 ${response.data.totalOptions} 个选项，但都不可点亮，请检查`);
        }
      } else {
        showError(null, `未找到Token: ${symbol}`);
        setSymbolResult(null);
        setSelectedAssetId(null);
      }
    } catch (error) {
      showError(error, 'Symbol搜索失败');
    } finally {
      setSymbolLoading(false);
    }
  }, [form, showError, showSuccess, showWarning]);

  // 余额筛选处理（优化版本，支持限制数量）
  const handleSmartFilter = useCallback(async () => {
    setSmartFilterLoading(true);
    try {
      // 获取当前表单中的自定义账户数量限制
      const values = form.getFieldsValue();
      const maxAccounts = values.enableCustomLightCount && values.customLightCount
        ? values.customLightCount
        : 1200; // 默认最大1200个账户

      console.log(`余额筛选：最大账户数限制为 ${maxAccounts}`);

      const response = await UserService.getAccountsWithBalance();
      console.log('📊 批量点亮余额筛选API响应:', response); // 调试日志

      if (!response.success) {
        showError(null, '获取账户余额信息失败');
        return;
      }

      // 处理嵌套的数据结构：检查是否有嵌套的 data 属性
      const responseData = response.data as { data?: UserWithBalance[] } | UserWithBalance[];
      const accountsData = Array.isArray(responseData) ? responseData : (responseData.data || response.data);
      console.log('📊 批量点亮账户数据:', accountsData); // 调试日志

      // 确保 accountsData 是数组
      if (!Array.isArray(accountsData)) {
        console.error('账户数据不是数组:', accountsData);
        showError(null, '账户数据格式错误');
        return;
      }

      let accountsWithBalance = accountsData.filter(account => account.hasBalance);

      // 如果账户数量超过限制，进行截取
      if (accountsWithBalance.length > maxAccounts) {
        accountsWithBalance = accountsWithBalance.slice(0, maxAccounts);
        console.log(`账户数量超过限制，已截取前 ${maxAccounts} 个账户`);
      }

      // 构建账户信息字符串
      const filteredAccountsText = accountsWithBalance
        .map(account => `${account.apiKey},${account.secret}`)
        .join('\n');

      // 更新表单中的账户信息
      form.setFieldsValue({ accounts: filteredAccountsText });

      // 设置筛选结果统计
      setSmartFilterResult({
        totalAccounts: accountsData.length,
        accountsWithBalance: accountsWithBalance.length,
        filteredAccounts: filteredAccountsText,
      });

      const totalWithBalance = accountsData.filter(account => account.hasBalance).length;
      const actualUsed = accountsWithBalance.length;

      if (totalWithBalance > maxAccounts) {
        showSuccess(
          `余额筛选完成：从 ${accountsData.length} 个账户中找到 ${totalWithBalance} 个有余额账户，` +
          `已选择前 ${actualUsed} 个（受限制影响）`
        );
      } else {
        showSuccess(
          `余额筛选完成：从 ${accountsData.length} 个账户中筛选出 ${actualUsed} 个有余额账户`
        );
      }
    } catch (error) {
      showError(error, '余额筛选失败');
    } finally {
      setSmartFilterLoading(false);
    }
  }, [form, showError, showSuccess]);

  // 获取可点亮Token列表（优化版本，限制数量）
  const handleGetLightableTokens = useCallback(async () => {
    setLightableTokensLoading(true);
    try {
      // 限制获取数量以提高性能
      const limit = 50; // 最多获取50个Token
      const response = await SymbolService.getLightableTokens(limit);
      if (response.code === 200) {
        // 转换API返回的数据格式为我们需要的格式
        const formattedTokens = response.data
          .filter(option => option.canLight) // 只显示可点亮的Token
          .slice(0, 20) // 进一步限制显示数量，避免UI过载
          .map(option => ({
            symbol: option.tokenSymbol || option.tokenName, // 使用tokenSymbol或tokenName作为symbol
            assetId: option.assetId,
            lightNum: option.lightNum,
          }));
        
        setLightableTokens(formattedTokens);
        
        const totalLightable = response.data.filter(option => option.canLight).length;
        if (totalLightable > 20) {
          showSuccess(
            `获取到 ${totalLightable} 个可点亮Token，显示前 20 个（按点亮数排序）`
          );
        } else {
          showSuccess(`获取到 ${formattedTokens.length} 个可点亮Token`);
        }
      } else {
        showError(null, response.msg);
      }
    } catch (error) {
      showError(error, '获取可点亮Token失败');
    } finally {
      setLightableTokensLoading(false);
    }
  }, [showError, showSuccess]);

  // 选择可点亮Token
  const handleSelectLightableToken = useCallback((token: { symbol: string; assetId: number; lightNum: number }) => {
    setSelectedAssetId(token.assetId);
    form.setFieldsValue({ 
      symbol: token.symbol,
      assetId: token.assetId.toString() 
    });
    showSuccess(`已选择Token: ${token.symbol} (ID: ${token.assetId})`);
  }, [form, showSuccess]);

  return {
    // 状态
    symbolLoading,
    smartFilterLoading,
    lightableTokensLoading,
    loading,
    symbolResult,
    selectedAssetId,
    smartFilterResult,
    lightableTokens,
    currentBatch,

    // 方法
    handleSymbolSearch,
    handleSmartFilter,
    handleGetLightableTokens,
    handleSelectLightableToken,
    setSelectedAssetId,
    setCurrentBatch,
    setLoading,
    setLightableTokens,
  };
};
