import { Modal } from 'antd';

interface ConfirmOptions {
  title?: string;
  content?: string;
  okText?: string;
  cancelText?: string;
  okType?: 'primary' | 'danger';
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
}

export const useConfirm = () => {
  const confirm = (options: ConfirmOptions) => {
    const {
      title = '确认操作',
      content = '确定要执行此操作吗？',
      okText = '确定',
      cancelText = '取消',
      okType = 'primary',
      onOk,
      onCancel,
    } = options;

    Modal.confirm({
      title,
      content,
      okText,
      cancelText,
      okType,
      onOk,
      onCancel,
    });
  };

  const confirmDelete = (options: Omit<ConfirmOptions, 'okType'>) => {
    confirm({
      ...options,
      title: options.title || '确认删除',
      content: options.content || '删除后无法恢复，确定要删除吗？',
      okType: 'danger',
    });
  };

  const confirmDanger = (options: Omit<ConfirmOptions, 'okType'>) => {
    confirm({
      ...options,
      okType: 'danger',
    });
  };

  return {
    confirm,
    confirmDelete,
    confirmDanger,
  };
};

export default useConfirm;
