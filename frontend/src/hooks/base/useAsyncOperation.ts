import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';
import { ErrorUtils } from '../../utils/commonUtils';

/**
 * 异步操作状态接口
 */
export interface AsyncOperationState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * 异步操作配置接口
 */
export interface AsyncOperationConfig {
  showErrorMessage?: boolean;
  showSuccessMessage?: boolean;
}

/**
 * 异步操作Hook
 * 专门处理异步操作的状态管理和错误处理
 */
export const useAsyncOperation = (config: AsyncOperationConfig = {}) => {
  const {
    showErrorMessage = true,
    showSuccessMessage = false,
  } = config;

  // 基础状态
  const [state, setState] = useState<AsyncOperationState>({
    loading: false,
    error: null,
    lastUpdated: null,
  });

  // 组件挂载状态
  const mountedRef = useRef(true);

  // 设置加载状态
  const setLoading = useCallback((loading: boolean) => {
    if (mountedRef.current) {
      setState(prev => ({ ...prev, loading }));
    }
  }, []);

  // 设置错误状态
  const setError = useCallback((error: string | null) => {
    if (mountedRef.current) {
      setState(prev => ({ ...prev, error }));
      if (error && showErrorMessage) {
        message.error(error);
      }
    }
  }, [showErrorMessage]);

  // 设置成功状态
  const setSuccess = useCallback((successMessage?: string) => {
    if (mountedRef.current) {
      setState(prev => ({ 
        ...prev, 
        error: null, 
        lastUpdated: new Date() 
      }));
      if (successMessage && showSuccessMessage) {
        message.success(successMessage);
      }
    }
  }, [showSuccessMessage]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // 安全的异步操作执行
  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string,
    options: {
      showLoading?: boolean;
      showSuccess?: boolean;
      successMessage?: string;
      defaultValue?: T;
    } = {}
  ): Promise<T | undefined> => {
    const {
      showLoading = true,
      showSuccess = false,
      successMessage,
      defaultValue,
    } = options;

    try {
      if (showLoading) {
        setLoading(true);
      }
      clearError();

      const result = await operation();

      if (showSuccess || successMessage) {
        setSuccess(successMessage || `${operationName}成功`);
      } else {
        setSuccess(); // 只更新时间戳
      }

      return result;
    } catch (error) {
      const errorMessage = ErrorUtils.formatErrorMessage(error, `${operationName}失败`);
      setError(errorMessage);
      
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      
      throw error;
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [setLoading, setError, setSuccess, clearError]);

  // 重试机制
  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    delay = 1000,
    operationName = '操作'
  ): Promise<T> => {
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`${operationName}失败，第 ${attempt} 次尝试`, error);

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError;
  }, []);

  return {
    // 状态
    ...state,
    
    // 状态控制方法
    setLoading,
    setError,
    setSuccess,
    clearError,
    
    // 操作方法
    execute,
    retry,
  };
};

export default useAsyncOperation;
