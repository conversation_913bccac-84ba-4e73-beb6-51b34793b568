import { useCallback } from 'react';

/**
 * 工具函数Hook
 * 提供防抖、节流、批量处理等工具方法
 */
export const useUtilities = () => {
  // 防抖执行
  const debounce = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }, []);

  // 节流执行
  const throttle = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  }, []);

  // 批量处理数据
  const batchProcess = useCallback(async <T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    batchSize = 10,
    delay = 100
  ): Promise<R[]> => {
    const results: R[] = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map((item, index) =>
        processor(item, i + index)
      );

      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.warn(`批次处理失败 (索引 ${i + index})`, result.reason);
        }
      });

      // 批次间延迟
      if (i + batchSize < items.length && delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return results;
  }, []);

  // 深拷贝
  const deepClone = useCallback(<T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as T;
    }

    if (obj instanceof Array) {
      return obj.map(item => deepClone(item)) as T;
    }

    if (typeof obj === 'object') {
      const clonedObj = {} as T;
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          clonedObj[key] = deepClone(obj[key]);
        }
      }
      return clonedObj;
    }

    return obj;
  }, []);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // 格式化数字
  const formatNumber = useCallback((num: number, precision = 2): string => {
    if (num >= 1e9) {
      return (num / 1e9).toFixed(precision) + 'B';
    }
    if (num >= 1e6) {
      return (num / 1e6).toFixed(precision) + 'M';
    }
    if (num >= 1e3) {
      return (num / 1e3).toFixed(precision) + 'K';
    }
    return num.toFixed(precision);
  }, []);

  // 生成唯一ID
  const generateId = useCallback((prefix = 'id'): string => {
    // 使用更安全的随机字符串生成
    const randomPart = Math.random().toString(36).substring(2, 11);
    return `${prefix}_${Date.now()}_${randomPart}`;
  }, []);

  // 延迟执行
  const delay = useCallback((ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  }, []);

  return {
    debounce,
    throttle,
    batchProcess,
    deepClone,
    formatFileSize,
    formatNumber,
    generateId,
    delay,
  };
};

export default useUtilities;
