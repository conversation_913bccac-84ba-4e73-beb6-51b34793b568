import { useRef, useEffect, useCallback } from 'react';

/**
 * 自动刷新配置接口
 */
export interface AutoRefreshConfig {
  enabled?: boolean;
  interval?: number;
}

/**
 * 自动刷新Hook
 * 专门处理定时刷新功能
 */
export const useAutoRefresh = (config: AutoRefreshConfig = {}) => {
  const {
    enabled = false,
    interval = 30000,
  } = config;

  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // 启动自动刷新
  const start = useCallback((refreshFunction: () => Promise<void>) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    if (enabled) {
      timerRef.current = setInterval(() => {
        if (mountedRef.current) {
          refreshFunction().catch(error => {
            console.warn('自动刷新失败:', error);
          });
        }
      }, interval);
    }
  }, [enabled, interval]);

  // 停止自动刷新
  const stop = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 重启自动刷新
  const restart = useCallback((refreshFunction: () => Promise<void>) => {
    stop();
    start(refreshFunction);
  }, [start, stop]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      stop();
    };
  }, [stop]);

  return {
    start,
    stop,
    restart,
    isEnabled: enabled,
    interval,
  };
};

export default useAutoRefresh;
