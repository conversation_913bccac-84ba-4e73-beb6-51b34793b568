import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';
import { ErrorUtils } from '../../utils/commonUtils';

/**
 * 基础Hook状态接口
 */
export interface BaseHookState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * 基础Hook配置接口
 */
export interface BaseHookConfig {
  enableAutoRefresh?: boolean;
  refreshInterval?: number;
  showErrorMessage?: boolean;
  showSuccessMessage?: boolean;
}

/**
 * 基础Hook
 * 提供通用的状态管理、错误处理、自动刷新等功能
 */
export const useBaseHook = (config: BaseHookConfig = {}) => {
  const {
    enableAutoRefresh = false,
    refreshInterval = 30000,
    showErrorMessage = true,
    showSuccessMessage = false,
  } = config;

  // 基础状态
  const [state, setState] = useState<BaseHookState>({
    loading: false,
    error: null,
    lastUpdated: null,
  });

  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // 设置加载状态
  const setLoading = useCallback((loading: boolean) => {
    if (mountedRef.current) {
      setState(prev => ({ ...prev, loading }));
    }
  }, []);

  // 设置错误状态
  const setError = useCallback((error: string | null) => {
    if (mountedRef.current) {
      setState(prev => ({ ...prev, error }));
      if (error && showErrorMessage) {
        message.error(error);
      }
    }
  }, [showErrorMessage]);

  // 设置成功状态
  const setSuccess = useCallback((successMessage?: string) => {
    if (mountedRef.current) {
      setState(prev => ({ 
        ...prev, 
        error: null, 
        lastUpdated: new Date() 
      }));
      if (successMessage && showSuccessMessage) {
        message.success(successMessage);
      }
    }
  }, [showSuccessMessage]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // 安全的异步操作执行
  const safeExecute = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string,
    options: {
      showLoading?: boolean;
      showSuccess?: boolean;
      successMessage?: string;
      defaultValue?: T;
    } = {}
  ): Promise<T | undefined> => {
    const {
      showLoading = true,
      showSuccess = false,
      successMessage,
      defaultValue,
    } = options;

    try {
      if (showLoading) {
        setLoading(true);
      }
      clearError();

      const result = await operation();

      if (showSuccess || successMessage) {
        setSuccess(successMessage || `${operationName}成功`);
      } else {
        setSuccess(); // 只更新时间戳
      }

      return result;
    } catch (error) {
      const errorMessage = ErrorUtils.formatErrorMessage(error, `${operationName}失败`);
      setError(errorMessage);
      
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      
      throw error;
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  }, [setLoading, setError, setSuccess, clearError]);

  // 重试机制
  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    delay = 1000,
    operationName = '操作'
  ): Promise<T> => {
    let lastError: unknown;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`${operationName}失败，第 ${attempt} 次尝试`, error);

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError;
  }, []);

  // 防抖执行
  const debounce = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }, []);

  // 节流执行
  const throttle = useCallback(<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  }, []);

  // 启动自动刷新
  const startAutoRefresh = useCallback((refreshFunction: () => Promise<void>) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    if (enableAutoRefresh) {
      timerRef.current = setInterval(() => {
        if (mountedRef.current) {
          refreshFunction().catch(error => {
            console.warn('自动刷新失败:', error);
          });
        }
      }, refreshInterval);
    }
  }, [enableAutoRefresh, refreshInterval]);

  // 停止自动刷新
  const stopAutoRefresh = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 批量处理数据
  const batchProcess = useCallback(async <T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    batchSize = 10,
    delay = 100,
    operationName = '批量处理'
  ): Promise<R[]> => {
    const result = await safeExecute(
      async () => {
        const results: R[] = [];

        for (let i = 0; i < items.length; i += batchSize) {
          const batch = items.slice(i, i + batchSize);
          const batchPromises = batch.map((item, index) =>
            processor(item, i + index)
          );

          const batchResults = await Promise.allSettled(batchPromises);

          batchResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              results.push(result.value);
            } else {
              console.warn(`批次处理失败 (索引 ${i + index})`, result.reason);
            }
          });

          // 批次间延迟
          if (i + batchSize < items.length && delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        return results;
      },
      operationName,
      { showLoading: true }
    );

    return result || [];
  }, [safeExecute]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      stopAutoRefresh();
    };
  }, [stopAutoRefresh]);

  return {
    // 状态
    ...state,
    
    // 状态控制方法
    setLoading,
    setError,
    setSuccess,
    clearError,
    
    // 工具方法
    safeExecute,
    retry,
    debounce,
    throttle,
    batchProcess,
    
    // 自动刷新控制
    startAutoRefresh,
    stopAutoRefresh,
  };
};

export default useBaseHook;
