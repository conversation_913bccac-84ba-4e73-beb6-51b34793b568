import { useRef, useCallback, useEffect } from 'react';

/**
 * 安全关闭Hook
 * 提供安全的组件关闭机制，确保所有异步操作和定时器被正确清理
 */
export const useSafeClose = () => {
  const isMountedRef = useRef(true);
  const isClosingRef = useRef(false);
  const cleanupFunctionsRef = useRef<Array<() => void>>([]);

  // 注册清理函数
  const registerCleanup = useCallback((cleanupFn: () => void) => {
    cleanupFunctionsRef.current.push(cleanupFn);
    
    // 返回取消注册的函数
    return () => {
      const index = cleanupFunctionsRef.current.indexOf(cleanupFn);
      if (index > -1) {
        cleanupFunctionsRef.current.splice(index, 1);
      }
    };
  }, []);

  // 执行所有清理函数
  const executeCleanup = useCallback(() => {
    cleanupFunctionsRef.current.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.error('清理函数执行失败:', error);
      }
    });
    cleanupFunctionsRef.current = [];
  }, []);

  // 安全关闭函数
  const safeClose = useCallback((onClose: () => void) => {
    if (isClosingRef.current) return;
    
    isClosingRef.current = true;
    
    // 立即执行清理
    executeCleanup();
    
    // 调用原始关闭函数
    onClose();
    
    // 重置状态（延迟执行）
    setTimeout(() => {
      if (isMountedRef.current) {
        isClosingRef.current = false;
      }
    }, 100);
  }, [executeCleanup]);

  // 检查是否正在关闭
  const isClosing = useCallback(() => isClosingRef.current, []);

  // 检查是否已挂载
  const isMounted = useCallback(() => isMountedRef.current, []);

  // 安全的状态更新函数
  const safeSetState = useCallback(<T>(
    setter: React.Dispatch<React.SetStateAction<T>>, 
    value: T | ((prev: T) => T)
  ) => {
    if (isMountedRef.current && !isClosingRef.current) {
      setter(value);
    }
  }, []);

  // 安全的异步操作执行
  const safeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: unknown) => void
  ): Promise<T | undefined> => {
    if (!isMountedRef.current || isClosingRef.current) {
      return undefined;
    }

    try {
      const result = await asyncFn();
      
      if (isMountedRef.current && !isClosingRef.current && onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      if (isMountedRef.current && !isClosingRef.current && onError) {
        onError(error);
      }
      throw error;
    }
  }, []);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      executeCleanup();
    };
  }, [executeCleanup]);

  return {
    // 状态检查
    isMounted,
    isClosing,
    
    // 清理管理
    registerCleanup,
    executeCleanup,
    
    // 安全操作
    safeClose,
    safeSetState,
    safeAsync,
  };
};

export default useSafeClose;
