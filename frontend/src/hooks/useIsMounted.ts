import React, { useRef, useEffect } from 'react';

/**
 * 用于检查组件是否仍然挂载的Hook
 * 防止在组件卸载后进行状态更新，避免React警告和潜在的内存泄漏
 */
export const useIsMounted = () => {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return isMountedRef;
};

/**
 * 安全的状态更新函数
 * 只有在组件仍然挂载时才执行状态更新
 */
export const useSafeState = <T>(initialState: T): [T, (newState: T | ((prevState: T) => T)) => void] => {
  const isMountedRef = useIsMounted();
  const [state, setState] = React.useState<T>(initialState);

  const safeSetState = React.useCallback((newState: T | ((prevState: T) => T)) => {
    if (isMountedRef.current) {
      setState(newState);
    }
  }, [isMountedRef]);

  return [state, safeSetState];
};

/**
 * 安全的异步操作Hook
 * 确保异步操作完成后组件仍然挂载才执行回调
 */
export const useSafeAsync = () => {
  const isMountedRef = useIsMounted();

  const safeAsync = React.useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: unknown) => void
  ) => {
    try {
      const result = await asyncFn();
      if (isMountedRef.current && onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (error) {
      if (isMountedRef.current && onError) {
        onError(error);
      }
      throw error;
    }
  }, [isMountedRef]);

  return { safeAsync, isMounted: () => isMountedRef.current };
};
