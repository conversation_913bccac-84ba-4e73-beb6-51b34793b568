import { useCallback } from 'react';
import { message } from 'antd';

interface AxiosError {
  response?: {
    status: number;
    statusText: string;
    data?: unknown;
    headers?: unknown;
  };
  request?: unknown;
  message: string;
  config?: {
    url?: string;
    method?: string;
    data?: unknown;
  };
}

/**
 * 错误处理Hook
 * 提供统一的错误处理和用户友好的错误信息显示
 */
export const useErrorHandler = () => {
  
  // 处理API错误
  const handleApiError = useCallback((error: unknown, defaultMessage: string = '操作失败'): string => {
    console.error('API错误详情:', error);
    
    // 检查是否是Axios错误
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as AxiosError;
      
      if (axiosError.response) {
        const status = axiosError.response.status;
        const data = axiosError.response.data;

        if (status === 400) {
          // 处理400错误
          if (data && typeof data === 'object') {
            const errorData = data as { message?: string | string[]; msg?: string; error?: string };
            if (errorData.message) {
              if (Array.isArray(errorData.message)) {
                return `参数验证失败: ${errorData.message.join(', ')}`;
              } else {
                return `参数验证失败: ${errorData.message}`;
              }
            } else if (errorData.msg) {
              return `请求错误: ${errorData.msg}`;
            } else if (errorData.error) {
              return `请求错误: ${errorData.error}`;
            } else {
              return `请求参数错误 (400): ${JSON.stringify(data)}`;
            }
          } else {
            return `请求参数错误 (400): ${data}`;
          }
        } else if (status === 401) {
          return '认证失败，请重新登录';
        } else if (status === 403) {
          return '权限不足，无法执行此操作';
        } else if (status === 404) {
          return '请求的资源不存在';
        } else if (status === 500) {
          const errorData = data as { message?: string; msg?: string };
          return `服务器错误: ${errorData?.message || errorData?.msg || '内部服务器错误'}`;
        } else {
          const errorData = data as { message?: string; msg?: string };
          return `API错误 (${status}): ${errorData?.message || errorData?.msg || '未知错误'}`;
        }
      } else if (axiosError.request) {
        return '网络连接失败，请检查网络连接';
      } else {
        return `请求配置错误: ${axiosError.message}`;
      }
    } else if (error instanceof Error) {
      return `${defaultMessage}: ${error.message}`;
    } else {
      return `${defaultMessage}: 未知错误`;
    }
  }, []);

  // 显示错误消息
  const showError = useCallback((error: unknown, defaultMessage: string = '操作失败') => {
    const errorMessage = handleApiError(error, defaultMessage);
    message.error(errorMessage);
    return errorMessage;
  }, [handleApiError]);

  // 显示成功消息
  const showSuccess = useCallback((msg: string) => {
    message.success(msg);
  }, []);

  // 显示警告消息
  const showWarning = useCallback((msg: string) => {
    message.warning(msg);
  }, []);

  // 显示信息消息
  const showInfo = useCallback((msg: string) => {
    message.info(msg);
  }, []);

  return {
    handleApiError,
    showError,
    showSuccess,
    showWarning,
    showInfo,
  };
};
