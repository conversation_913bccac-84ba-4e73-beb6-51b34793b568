import { useState, useCallback } from 'react';
import type { UserLightBean } from '../services/enhancedBatchLightService';

export interface ParsedAccountsCache {
  text: string;
  accounts: UserLightBean[];
  count: number;
}

/**
 * 账户解析Hook
 * 提供账户列表解析、缓存和性能优化功能
 */
export const useAccountParser = () => {
  const [parsedAccountsCache, setParsedAccountsCache] = useState<ParsedAccountsCache | null>(null);

  // 优化的账户列表解析（带缓存）
  const parseUserBeanList = useCallback((accountsText: string): UserLightBean[] => {
    // 检查缓存
    if (parsedAccountsCache && parsedAccountsCache.text === accountsText) {
      return parsedAccountsCache.accounts;
    }

    // 解析账户列表
    const accounts = accountsText
      .split('\n')
      .filter((line: string) => line.trim())
      .map((line: string) => {
        const [apiKey, secret] = line.trim().split(',').map((s: string) => s.trim());
        if (!apiKey || !secret) {
          throw new Error(`Invalid account format: ${line}`);
        }
        return { apiKey, secret };
      });

    // 更新缓存
    setParsedAccountsCache({
      text: accountsText,
      accounts,
      count: accounts.length,
    });

    return accounts;
  }, [parsedAccountsCache]);

  // 获取账户数量（快速方法，不需要完整解析）
  const getAccountCount = useCallback((accountsText: string): number => {
    if (!accountsText || !accountsText.trim()) return 0;
    
    // 检查缓存
    if (parsedAccountsCache && parsedAccountsCache.text === accountsText) {
      return parsedAccountsCache.count;
    }

    // 快速计算行数（不进行完整解析）
    return accountsText.split('\n').filter(line => line.trim()).length;
  }, [parsedAccountsCache]);

  // 验证账户格式
  const validateAccountFormat = useCallback((accountsText: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!accountsText || !accountsText.trim()) {
      errors.push('账户信息不能为空');
      return { isValid: false, errors };
    }

    const lines = accountsText.split('\n').filter(line => line.trim());
    
    lines.forEach((line, index) => {
      const parts = line.trim().split(',');
      if (parts.length !== 2) {
        errors.push(`第${index + 1}行格式错误：应为"apiKey,secret"格式`);
      } else {
        const [apiKey, secret] = parts.map(s => s.trim());
        if (!apiKey) {
          errors.push(`第${index + 1}行：API密钥不能为空`);
        }
        if (!secret) {
          errors.push(`第${index + 1}行：秘钥不能为空`);
        }
      }
    });

    return { isValid: errors.length === 0, errors };
  }, []);

  // 清除缓存
  const clearCache = useCallback(() => {
    setParsedAccountsCache(null);
  }, []);

  return {
    parseUserBeanList,
    getAccountCount,
    validateAccountFormat,
    clearCache,
    parsedAccountsCache,
  };
};
