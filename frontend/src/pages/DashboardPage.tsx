import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, Space, Typography, Spin, Divider } from 'antd';
import {
  UserOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { UserService, type AccountStatistics } from '../services/userService';
import { useAuth } from '../hooks/useAuth';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [statistics, setStatistics] = useState<AccountStatistics | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取账户统计信息
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const response = await UserService.getAccountStatistics();
      console.log('📊 Dashboard统计API响应:', response); // 调试日志
      if (response.success) {
        // 处理嵌套的数据结构：检查是否有嵌套的 data 属性
        const responseData = response.data as { data?: AccountStatistics } | AccountStatistics;
        const rawData = 'totalAccounts' in responseData ? responseData : (responseData.data || response.data);
        console.log('📊 Dashboard原始统计数据:', rawData); // 调试日志

        // 验证数据完整性
        const validatedData: AccountStatistics = {
          totalAccounts: typeof rawData.totalAccounts === 'number' ? rawData.totalAccounts : 0,
          accountsWithBalance: typeof rawData.accountsWithBalance === 'number' ? rawData.accountsWithBalance : 0,
          accountsWithoutBalance: typeof rawData.accountsWithoutBalance === 'number' ? rawData.accountsWithoutBalance : 0,
          totalNineBalance: typeof rawData.totalNineBalance === 'number' ? rawData.totalNineBalance : 0,
          averageBalance: typeof rawData.averageBalance === 'number' ? rawData.averageBalance : 0,
          executionTimeMs: typeof rawData.executionTimeMs === 'number' ? rawData.executionTimeMs : 0,
        };
        setStatistics(validatedData);
        console.log('✅ Dashboard统计数据已设置:', validatedData); // 调试日志
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>Nine Light 管理后台</Title>
        <Text type="secondary">
          欢迎回来，{user?.username}！批量账户点亮管理系统
        </Text>
      </div>

      {/* 统计卡片 */}
      <Spin spinning={loading}>
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12}>
            <Card>
              <Statistic
                title="总账户数"
                value={statistics?.totalAccounts || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card>
              <Statistic
                title="有余额账户数"
                value={statistics?.accountsWithBalance || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>
      </Spin>

      {/* 核心功能入口 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title="增强点亮"
            extra={<RocketOutlined style={{ color: '#1890ff' }} />}
            hoverable
          >
            <div style={{ marginBottom: '16px' }}>
              <Text>
                全新升级的批量点亮功能，集成首次点亮分析、随机数量、批次处理等高级功能。
                支持智能筛选、实时监控和详细统计。
              </Text>
            </div>
            <Space wrap>
              <Button
                type="primary"
                icon={<RocketOutlined />}
                onClick={() => navigate('/batch-light')}
                size="large"
              >
                开始增强点亮
              </Button>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title="随机点亮"
            extra={<ThunderboltOutlined style={{ color: '#722ed1' }} />}
            hoverable
          >
            <div style={{ marginBottom: '16px' }}>
              <Text>
                轻量级随机点亮功能，支持随机数量设置和即时执行。
                适合小规模快速测试和灵活操作需求。
              </Text>
            </div>
            <Space wrap>
              <Button
                type="primary"
                icon={<ThunderboltOutlined />}
                onClick={() => navigate('/random-light')}
                size="large"
                style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
              >
                开始随机点亮
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 管理工具 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} lg={12}>
          <Card
            title="账户管理"
            extra={<UserOutlined style={{ color: '#52c41a' }} />}
            hoverable
          >
            <div style={{ marginBottom: '16px' }}>
              <Text>
                查看和管理系统中的所有账户信息，实时查看NINE余额状态。
                支持筛选、搜索和余额查询等功能。
              </Text>
            </div>
            <Space wrap>
              <Button
                icon={<UserOutlined />}
                onClick={() => navigate('/users')}
              >
                账户管理
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => navigate('/users')}
              >
                余额查询
              </Button>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card
            title="操作记录"
            extra={<HistoryOutlined style={{ color: '#fa8c16' }} />}
            hoverable
          >
            <div style={{ marginBottom: '16px' }}>
              <Text>
                查看所有批量点亮和随机点亮的执行记录，包括详细的操作历史和统计信息。
                支持筛选和导出功能。
              </Text>
            </div>
            <Space wrap>
              <Button
                icon={<HistoryOutlined />}
                onClick={() => navigate('/batch-light', { state: { activeTab: '操作记录' } })}
              >
                批量记录
              </Button>
              <Button
                icon={<HistoryOutlined />}
                onClick={() => navigate('/random-light', { state: { activeTab: '执行历史' } })}
              >
                随机记录
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 系统状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24}>
          <Card
            title="系统状态"
            extra={
              <Space>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={fetchStatistics}
                  loading={loading}
                >
                  刷新统计
                </Button>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <Statistic
                    title="后端服务"
                    value="正常"
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <Statistic
                    title="数据库"
                    value="已连接"
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <Statistic
                    title="账户数据"
                    value="已加载"
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div style={{ textAlign: 'center' }}>
                  <Statistic
                    title="API接口"
                    value="可用"
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 功能介绍 */}
      <Card title="Nine Light 功能概览" style={{ marginTop: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <Title level={4}>🚀 增强点亮</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text>✅ <strong>首次点亮分析</strong>：自动识别首次点亮账户</Text><br />
              <Text>✅ <strong>智能筛选</strong>：自动筛选有余额账户</Text><br />
              <Text>✅ <strong>批次处理</strong>：分批执行，避免系统压力</Text><br />
              <Text>✅ <strong>随机数量</strong>：支持随机数量范围设置</Text><br />
              <Text>✅ <strong>实时监控</strong>：批次进度实时更新</Text><br />
              <Text>✅ <strong>详细统计</strong>：完整的执行报告和历史记录</Text>
            </div>
          </Col>

          <Col xs={24} md={8}>
            <Title level={4}>⚡ 随机点亮</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text>✅ <strong>快速执行</strong>：轻量级即时执行</Text><br />
              <Text>✅ <strong>随机数量</strong>：灵活的数量范围设置</Text><br />
              <Text>✅ <strong>简单配置</strong>：最少的配置参数</Text><br />
              <Text>✅ <strong>测试友好</strong>：适合小规模测试</Text><br />
              <Text>✅ <strong>执行历史</strong>：记录所有执行详情</Text><br />
              <Text>✅ <strong>操作便捷</strong>：一键启动和停止</Text>
            </div>
          </Col>

          <Col xs={24} md={8}>
            <Title level={4}>👥 账户管理</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text>✅ <strong>账户查看</strong>：查看所有账户信息</Text><br />
              <Text>✅ <strong>余额查询</strong>：实时查看NINE余额</Text><br />
              <Text>✅ <strong>智能筛选</strong>：按余额状态筛选账户</Text><br />
              <Text>✅ <strong>搜索功能</strong>：按邮箱或API Key搜索</Text><br />
              <Text>✅ <strong>状态监控</strong>：账户余额状态实时更新</Text><br />
              <Text>✅ <strong>统计信息</strong>：账户数量和余额统计</Text>
            </div>
          </Col>
        </Row>

        <Divider />

        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Title level={4}>📋 快速开始</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text><strong>增强点亮操作步骤：</strong></Text><br />
              <Text>1. 搜索Token符号或直接输入资产ID</Text><br />
              <Text>2. 点击"智能筛选"获取有余额账户</Text><br />
              <Text>3. 配置点亮数量（固定或随机）</Text><br />
              <Text>4. 设置批次处理参数（可选）</Text><br />
              <Text>5. 启用首次点亮分析（推荐）</Text><br />
              <Text>6. 预览配置并开始执行</Text>
            </div>
          </Col>

          <Col xs={24} md={12}>
            <Title level={4}>⚡ 随机点亮操作</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text><strong>随机点亮操作步骤：</strong></Text><br />
              <Text>1. 输入或搜索资产信息</Text><br />
              <Text>2. 设置随机数量范围</Text><br />
              <Text>3. 选择要执行的账户</Text><br />
              <Text>4. 点击"开始随机点亮"</Text><br />
              <Text>5. 实时查看执行结果</Text><br />
              <Text>6. 查看执行历史和统计</Text>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default DashboardPage; 