import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Typography,
  Tag,
  Tooltip,
  Select,
  Input,
} from 'antd';
import {
  UserOutlined,
  ReloadOutlined,
  FilterOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  UserService,
  type UserWithBalance,
  type AccountStatistics
} from '../services/userService';
// 移除调试工具引用
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

// 筛选选项类型
type BalanceFilterType = 'all' | 'withBalance' | 'withoutBalance';

const UserManagementPage: React.FC = () => {
  const [accountsWithBalance, setAccountsWithBalance] = useState<UserWithBalance[]>([]);
  const [statistics, setStatistics] = useState<AccountStatistics | null>(null);
  const [balanceLoading, setBalanceLoading] = useState(false);

  // 筛选状态
  const [balanceFilter, setBalanceFilter] = useState<BalanceFilterType>('all');
  const [searchText, setSearchText] = useState<string>('');



  // 获取带余额信息的账户列表
  const fetchAccountsWithBalance = async () => {
    setBalanceLoading(true);
    try {
      const response = await UserService.getAccountsWithBalance();
      console.log('📊 账户余额API响应:', response); // 调试日志
      if (response.success) {
        // 处理嵌套的数据结构：检查是否有嵌套的 data 属性
        const responseData = response.data as { data?: UserWithBalance[]; count?: number } | UserWithBalance[];
        const accountsData = Array.isArray(responseData) ? responseData : (responseData.data || response.data);
        const accountsCount = Array.isArray(responseData)
          ? responseData.length
          : (responseData.count || response.count || (Array.isArray(accountsData) ? accountsData.length : 0));

        setAccountsWithBalance(Array.isArray(accountsData) ? accountsData : []);
        message.success(`成功获取 ${accountsCount} 个账户的余额信息`);
      } else {
        message.error(response.message || '获取账户余额信息失败');
      }
    } catch (error) {
      console.error('获取账户余额信息失败:', error);
      message.error('获取账户余额信息失败');
    } finally {
      setBalanceLoading(false);
    }
  };

  // 获取账户统计信息
  const fetchStatistics = async () => {
    try {
      const response = await UserService.getAccountStatistics();
      console.log('📊 统计API响应:', response); // 调试日志
      if (response.success && response.data) {
        // 处理嵌套的数据结构：检查是否有嵌套的 data 属性
        const responseData = response.data as { data?: AccountStatistics } | AccountStatistics;
        const rawData = 'totalAccounts' in responseData ? responseData : (responseData.data || response.data);
        console.log('📊 原始统计数据:', rawData); // 调试日志

        // 验证数据完整性
        const validatedData: AccountStatistics = {
          totalAccounts: typeof rawData.totalAccounts === 'number' ? rawData.totalAccounts : 0,
          accountsWithBalance: typeof rawData.accountsWithBalance === 'number' ? rawData.accountsWithBalance : 0,
          accountsWithoutBalance: typeof rawData.accountsWithoutBalance === 'number' ? rawData.accountsWithoutBalance : 0,
          totalNineBalance: typeof rawData.totalNineBalance === 'number' ? rawData.totalNineBalance : 0,
          averageBalance: typeof rawData.averageBalance === 'number' ? rawData.averageBalance : 0,
          executionTimeMs: typeof rawData.executionTimeMs === 'number' ? rawData.executionTimeMs : 0,
        };
        setStatistics(validatedData);
        console.log('✅ 统计数据已设置:', validatedData); // 调试日志
      } else {
        console.error('统计API返回数据无效:', response);
        message.error(response.message || '获取统计信息失败');
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
      message.error('获取统计信息失败');
    }
  };

  // 移除调试功能
  const handleDebugApi = async () => {
    console.log('🔧 调试功能已移除');
  };





  // 带余额信息的账户表格列定义
  const balanceColumns: ColumnsType<UserWithBalance> = [
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
      width: 200,
    },
    {
      title: 'API Key',
      dataIndex: 'apiKey',
      key: 'apiKey',
      render: (apiKey: string) => (
        <Tooltip title={apiKey}>
          <Text code copyable={{ text: apiKey }}>
            {`${apiKey.slice(0, 8)}...${apiKey.slice(-8)}`}
          </Text>
        </Tooltip>
      ),
      width: 150,
    },
    {
      title: 'Secret',
      dataIndex: 'secret',
      key: 'secret',
      render: (secret: string) => (
        <Tooltip title={secret}>
          <Text code copyable={{ text: secret }}>
            {`${secret.slice(0, 8)}...${secret.slice(-8)}`}
          </Text>
        </Tooltip>
      ),
      width: 150,
    },
    {
      title: 'NINE余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => {
        const safeBalance = typeof balance === 'number' ? balance : 0;
        return (
          <Text strong style={{ color: safeBalance > 0 ? '#52c41a' : '#ff4d4f' }}>
            {safeBalance.toFixed(2)}
          </Text>
        );
      },
      width: 120,
      sorter: (a, b) => (a.balance || 0) - (b.balance || 0),
    },
    {
      title: '余额状态',
      key: 'balanceStatus',
      render: (_, record) => (
        record.hasBalance ? (
          <Tag color="green">有余额</Tag>
        ) : (
          <Tag color="red">无余额</Tag>
        )
      ),
      width: 100,
    },
  ];

  // 筛选后的账户数据
  const filteredAccountsWithBalance = useMemo(() => {
    return accountsWithBalance.filter(account => {
      // 余额筛选
      if (balanceFilter === 'withBalance' && !account.hasBalance) return false;
      if (balanceFilter === 'withoutBalance' && account.hasBalance) return false;

      // 搜索文本筛选
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        return (
          account.email.toLowerCase().includes(searchLower) ||
          account.apiKey.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }, [accountsWithBalance, balanceFilter, searchText]);

  // 重置筛选条件
  const resetFilters = () => {
    setBalanceFilter('all');
    setSearchText('');
  };

  // 初始化数据
  useEffect(() => {
    fetchAccountsWithBalance();
    fetchStatistics();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <UserOutlined /> 用户管理
      </Title>



      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {statistics && (
          <>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总账户数"
                  value={statistics.totalAccounts}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="有余额账户"
                  value={statistics.accountsWithBalance}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总NINE余额"
                  value={typeof statistics.totalNineBalance === 'number' ? statistics.totalNineBalance.toFixed(2) : '0.00'}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="平均余额"
                  value={typeof statistics.averageBalance === 'number' ? statistics.averageBalance.toFixed(2) : '0.00'}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </>
        )}
      </Row>

      {/* 筛选控件 */}
        <Card title="筛选条件" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} md={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <span>余额状态:</span>
                <Select
                  style={{ width: '100%' }}
                  value={balanceFilter}
                  onChange={setBalanceFilter}
                  options={[
                    { label: '全部', value: 'all' },
                    { label: '有余额', value: 'withBalance' },
                    { label: '无余额', value: 'withoutBalance' },
                  ]}
                />
              </Space>
            </Col>

            <Col xs={24} sm={8} md={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <span>搜索:</span>
                <Input
                  placeholder="搜索邮箱或API Key"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  prefix={<SearchOutlined />}
                  allowClear
                />
              </Space>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <span>操作:</span>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Button
                    icon={<FilterOutlined />}
                    onClick={resetFilters}
                    style={{ flex: 1 }}
                  >
                    重置筛选
                  </Button>
                </Space>
              </Space>
            </Col>
            <Col xs={24} sm={24} md={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <span>统计:</span>
                <Tag color="blue" style={{ textAlign: 'center', width: '100%' }}>
                  显示 {filteredAccountsWithBalance.length} / {accountsWithBalance.length}
                </Tag>
              </Space>
            </Col>
          </Row>
        </Card>

      {/* 操作按钮和用户列表 */}
      <Card
        title="账户列表（含余额信息）"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAccountsWithBalance}
              loading={balanceLoading}
            >
              刷新
            </Button>
            <Button
              onClick={handleDebugApi}
              type="dashed"
            >
              调试API
            </Button>
          </Space>
        }
      >
        <Table
          columns={balanceColumns}
          dataSource={filteredAccountsWithBalance}
          rowKey="apiKey"
          loading={balanceLoading}
          pagination={{
            total: filteredAccountsWithBalance.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default UserManagementPage;
