import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Form,
  Button,
  Space,
  Typography,
  Badge,
  message,
  Divider,
  InputNumber,
  Table,
  Tag,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  DatabaseOutlined,
  HistoryOutlined,
} from '@ant-design/icons';

import { RandomPoolTradeService } from '../services/randomPoolTradeService';
import { NumberUtils } from '../utils/commonUtils';
import { TradeStatusUtils } from '../utils/tradeStatusUtils';
import InnerTradeAccountSelector from '../components/InnerTrade/InnerTradeAccountSelector';
import { useAccountParser } from '../hooks/useAccountParser';
import { useRandomPoolTradeSSE } from '../hooks/useSSEConnection';
import type {
  StartRandomPoolTradeRequest,
  RandomPoolTradeStatusResponse,
  RandomPoolTradeEvent,
  LoadingState,
  RandomPoolTradeFormValues,
  SelectablePool,
  TradeOperationRecord,
} from '../types/randomPoolTrade';


const { Title, Text } = Typography;

/**
 * 随机内盘交易页面组件
 *
 * 功能特性：
 * - 支持随机模式和手动模式的账户选择
 * - USDT余额筛选和智能账户管理
 * - 随机选择池子进行买入卖出操作
 * - 支持买入累积逻辑和配置化参数
 * - 实时状态监控和历史记录查看
 * - 基于现有框架设计模式实现
 */
const RandomPoolTradePage: React.FC = () => {
  const [form] = Form.useForm<RandomPoolTradeFormValues>();
  const isMountedRef = useRef(true);

  // 状态管理
  const [status, setStatus] = useState<RandomPoolTradeStatusResponse | null>(null);
  const [selectablePools, setSelectablePools] = useState<SelectablePool[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    start: false,
    stop: false,
    refresh: false,
    history: false,
    pools: false,
  });

  // 交易历史记录状态
  const [tradeHistory, setTradeHistory] = useState<TradeOperationRecord[]>([]);
  const [historyPagination, setHistoryPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 使用账户解析 Hook
  const { parsedAccountsCache, parseUserBeanList } = useAccountParser();

  /**
   * 加载数据
   */
  const loadData = useCallback(async () => {
    if (!isMountedRef.current) return;

    setLoading(prev => ({ ...prev, refresh: true }));
    try {
      const response = await RandomPoolTradeService.getStatus();
      if (response.success && isMountedRef.current) {
        setStatus(response.data || null);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      if (isMountedRef.current) {
        message.error('加载数据失败');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(prev => ({ ...prev, refresh: false }));
      }
    }
  }, []);

  /**
   * 加载交易历史记录
   */
  const loadTradeHistory = useCallback(async (page: number = 1, pageSize: number = 10) => {
    if (!isMountedRef.current) return;

    setLoading(prev => ({ ...prev, history: true }));
    try {
      // 只显示当前会话的历史记录
      const sessionId = status?.session?.sessionId;
      const response = await RandomPoolTradeService.getTradeHistory({
        sessionId, // 传递当前会话ID
        page,
        pageSize,
      });

      if (response.success && isMountedRef.current) {
        const historyData = response.data;
        setTradeHistory(historyData?.records || []);
        setHistoryPagination({
          current: historyData?.pagination?.page || 1,
          pageSize: historyData?.pagination?.pageSize || 10,
          total: historyData?.pagination?.total || 0,
        });
      }
    } catch (error) {
      console.error('加载交易历史失败:', error);
      message.error('加载交易历史失败');
    } finally {
      if (isMountedRef.current) {
        setLoading(prev => ({ ...prev, history: false }));
      }
    }
  }, [status?.session?.sessionId]);

  /**
   * 处理实时事件
   */
  const handleRealtimeEvent = useCallback((event: RandomPoolTradeEvent) => {
    if (!isMountedRef.current) return;

    console.log('收到实时事件:', event);

    switch (event.type) {
      case 'heartbeat':
        // 心跳事件，用于保持连接活跃，不需要额外处理
        console.debug('收到心跳事件，连接正常');
        break;

      case 'session_started':
        message.success('随机池子刷量已启动');
        loadData();
        loadTradeHistory();
        break;

      case 'session_stopped':
        message.info('随机池子刷量已停止');
        loadData();
        loadTradeHistory();
        break;

      case 'pool_selected':
        if (event.data?.pool) {
          message.info(`选择池子: ${event.data.pool.symbol}`);
        }
        loadData();
        break;

      case 'buy_operation_completed':
        if (event.data?.pool && event.data?.trade) {
          message.success(
            `买入成功: ${event.data.pool.symbol} ${event.data.trade.amount}`
          );
        }
        loadData();
        loadTradeHistory();
        break;

      case 'sell_operation_completed':
        if (event.data?.pool && event.data?.trade) {
          message.success(
            `卖出成功: ${event.data.pool.symbol} ${event.data.trade.amount}`
          );
        }
        loadData();
        loadTradeHistory();
        break;

      case 'buy_operation_failed':
      case 'sell_operation_failed':
        if (event.data?.pool && event.data?.trade) {
          message.error(
            `${event.data.trade.type === 'BUY' ? '买入' : '卖出'}失败: ${event.data.pool.symbol} - ${event.data.trade.error || '未知错误'}`
          );
        }
        loadTradeHistory();
        break;

      case 'pool_completed':
        if (event.data?.pool) {
          message.success(`池子交易完成: ${event.data.pool.symbol}`);
        }
        loadData();
        loadTradeHistory();
        break;

      case 'positions_cleared':
        message.info('所有仓位已清空');
        loadData();
        loadTradeHistory();
        break;

      case 'statistics_updated':
        if (event.data?.statistics) {
          setStatus(prevStatus => {
            if (!prevStatus) return null;
            return {
              ...prevStatus,
              statistics: event.data!.statistics!.operations ? {
                operations: event.data!.statistics!.operations,
                pools: event.data!.statistics!.pools,
                volume: event.data!.statistics!.volume,
              } : prevStatus.statistics,
            };
          });
        }
        break;

      case 'error_occurred':
        message.error(`系统错误: ${event.data?.error || '未知错误'}`);
        loadTradeHistory();
        break;

      default:
        console.warn('未处理的事件类型:', event.type, '事件数据:', event);
    }
  }, [loadData, loadTradeHistory]);

  /**
   * 数据刷新回调（SSE连接断开时的兜底机制）
   */
  const handleDataRefresh = useCallback(() => {
    console.log('🔄 SSE连接断开，执行数据刷新');
    loadData();
    loadTradeHistory();
  }, [loadData, loadTradeHistory]);

  // 使用SSE连接Hook
  const { connectionStatus } = useRandomPoolTradeSSE(
    handleRealtimeEvent,
    handleDataRefresh
  );

  // 组件挂载状态
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  /**
   * 加载可选择的池子
   */
  const loadSelectablePools = useCallback(async () => {
    if (!isMountedRef.current) return;

    setLoading(prev => ({ ...prev, pools: true }));
    try {
      const response = await RandomPoolTradeService.getSelectablePools();
      if (response.success && isMountedRef.current) {
        setSelectablePools(response.data?.pools || []);
      }
    } catch (error) {
      console.error('加载池子数据失败:', error);
      message.error('加载池子数据失败');
    } finally {
      if (isMountedRef.current) {
        setLoading(prev => ({ ...prev, pools: false }));
      }
    }
  }, []);

  // 初始化数据加载
  useEffect(() => {
    loadData();
    loadSelectablePools();
    loadTradeHistory();
  }, [loadData, loadSelectablePools, loadTradeHistory]);


  /**
   * 启动随机池子刷量
   */
  const handleStart = async () => {
    try {
      const values = await form.validateFields();
      setLoading(prev => ({ ...prev, start: true }));

      const request: StartRandomPoolTradeRequest = {
        minBuyAmount: values.minBuyAmount,
        maxBuyAmount: values.maxBuyAmount,
        minBuyIntervalMs: (values.minBuyIntervalSeconds || 1) * 1000,
        maxBuyIntervalMs: (values.maxBuyIntervalSeconds || 5) * 1000,
        minSellDelayMs: (values.minSellDelaySeconds || 5) * 1000,
        maxSellDelayMs: (values.maxSellDelaySeconds || 10) * 1000,
        buyCount: values.buyCount,
        poolCooldownMs: (values.poolCooldownMinutes || 5) * 60000,
      };

      // 验证配置
      const errors = RandomPoolTradeService.validateConfig(request);
      if (errors.length > 0) {
        message.error(errors[0]);
        return;
      }

      const response = await RandomPoolTradeService.startRandomPoolTrade(request);

      if (response.success) {
        message.success('随机池子刷量已启动');
        await loadData(); // 立即刷新状态（会触发useEffect自动启动定时器）
        await loadTradeHistory(); // 立即刷新历史记录
      } else {
        message.error(response.message || '启动失败');
      }
    } catch (error) {
      console.error('启动失败:', error);
      message.error('启动失败');
    } finally {
      setLoading(prev => ({ ...prev, start: false }));
    }
  };

  /**
   * 停止随机池子刷量
   */
  const handleStop = async () => {
    try {
      setLoading(prev => ({ ...prev, stop: true }));

      const response = await RandomPoolTradeService.stopRandomPoolTrade();
      
      if (response.success) {
        message.success('随机池子刷量已停止');
        await loadData(); // 刷新状态（会触发useEffect自动停止定时器）
        await loadTradeHistory(); // 最后刷新一次历史记录
      } else {
        message.error(response.message || '停止失败');
      }
    } catch (error) {
      console.error('停止失败:', error);
      message.error('停止失败');
    } finally {
      setLoading(prev => ({ ...prev, stop: false }));
    }
  };

  /**
   * 刷新数据
   */
  const handleRefresh = async () => {
    await Promise.all([
      loadData(),
      loadSelectablePools(),
      loadTradeHistory(historyPagination.current, historyPagination.pageSize)
    ]);
  };

  // 获取运行状态
  const isRunning = status?.session?.isRunning || false;

  /**
   * 解析账户信息
   */
  const handleAccountsChange = useCallback((accountsText: string) => {
    if (!accountsText.trim()) {
      return;
    }

    try {
      parseUserBeanList(accountsText);
    } catch (error) {
      console.error('解析账户信息失败:', error);
    }
  }, [parseUserBeanList]);

  /**
   * 显示成功消息
   */
  const showSuccess = useCallback((msg: string) => {
    message.success(msg);
  }, []);

  /**
   * 显示错误消息
   */
  const showError = useCallback((title: string | null, msg: string) => {
    message.error(title ? `${title}: ${msg}` : msg);
  }, []);

  /**
   * 监听表单账户字段变化
   */
  useEffect(() => {
    const subscription = form.getFieldInstance('accounts')?.addEventListener?.('input', (e: Event) => {
      const target = e.target as HTMLTextAreaElement;
      handleAccountsChange(target.value);
    });

    return () => {
      if (subscription) {
        subscription();
      }
    };
  }, [form, handleAccountsChange]);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BarChartOutlined /> 随机内盘交易
      </Title>
      <Text type="secondary">
        支持随机模式和手动模式的账户选择，随机选择池子进行内盘交易操作，支持USDT余额筛选和智能账户管理
      </Text>

      <Divider />

      <Row gutter={16}>
        {/* 控制面板 */}
        <Col span={8}>
          <Card title="控制面板" extra={
            <Space>
              <Badge 
                status={connectionStatus === 'connected' ? 'success' : 
                       connectionStatus === 'connecting' ? 'processing' : 'error'} 
                text={connectionStatus === 'connected' ? '已连接' : 
                     connectionStatus === 'connecting' ? '连接中' : '已断开'}
              />
              {connectionStatus === 'connected' ? (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading.refresh}
                  size="small"
                  title="刷新数据"
                />
              ) : (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => { loadData(); loadTradeHistory(); }}
                  size="small"
                  type="primary"
                  title="刷新数据"
                />
              )}
            </Space>
          }>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                minBuyAmount: 1,
                maxBuyAmount: 10,
                minBuyIntervalSeconds: 1,
                maxBuyIntervalSeconds: 5,
                minSellDelaySeconds: 5,
                maxSellDelaySeconds: 10,
                buyCount: 2,
                poolCooldownMinutes: 5,
                minUsdtBalance: 10,
                customAccountCount: 500,
              }}
            >
              {/* 账户选择组件 */}
              <InnerTradeAccountSelector
                form={{
                  getFieldsValue: () => form.getFieldsValue() as unknown as Record<string, unknown>,
                  setFieldsValue: (values: Record<string, unknown>) => form.setFieldsValue(values as Partial<RandomPoolTradeFormValues>),
                  getFieldValue: (name: string) => form.getFieldValue(name as keyof RandomPoolTradeFormValues),
                }}
                parsedAccountsCache={parsedAccountsCache}
                showError={showError}
                showSuccess={showSuccess}
              />

              {/* 交易配置 */}
              <Card title="交易配置" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="买入量区间"
                  style={{ marginBottom: 16 }}
                >
                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name="minBuyAmount"
                        rules={[
                          { required: true, message: '请输入最小买入量' },
                          { type: 'number', min: 0.01, message: '最小买入量不能小于0.01' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最小值"
                          style={{ width: '100%' }}
                          min={0.01}
                          step={0.01}
                          precision={2}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maxBuyAmount"
                        rules={[
                          { required: true, message: '请输入最大买入量' },
                          { type: 'number', min: 0.01, message: '最大买入量不能小于0.01' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最大值"
                          style={{ width: '100%' }}
                          min={0.01}
                          step={0.01}
                          precision={2}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form.Item>

                {/* 买入间隔时间区间 */}
                <Form.Item
                  label="买入间隔时间区间（秒）"
                  style={{ marginBottom: 16 }}
                >
                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name="minBuyIntervalSeconds"
                        rules={[
                          { required: true, message: '请输入最小买入间隔时间' },
                          { type: 'number', min: 1, message: '最小买入间隔时间不能小于1秒' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最小值"
                          style={{ width: '100%' }}
                          min={1}
                          max={300}
                          addonAfter="秒"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maxBuyIntervalSeconds"
                        rules={[
                          { required: true, message: '请输入最大买入间隔时间' },
                          { type: 'number', min: 1, message: '最大买入间隔时间不能小于1秒' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最大值"
                          style={{ width: '100%' }}
                          min={1}
                          max={300}
                          addonAfter="秒"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form.Item>

                {/* 卖出延迟时间区间 */}
                <Form.Item
                  label="卖出延迟时间区间（秒）"
                  style={{ marginBottom: 16 }}
                >
                  <Row gutter={8}>
                    <Col span={12}>
                      <Form.Item
                        name="minSellDelaySeconds"
                        rules={[
                          { required: true, message: '请输入最小卖出延迟时间' },
                          { type: 'number', min: 1, message: '最小卖出延迟时间不能小于1秒' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最小值"
                          style={{ width: '100%' }}
                          min={1}
                          max={600}
                          addonAfter="秒"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maxSellDelaySeconds"
                        rules={[
                          { required: true, message: '请输入最大卖出延迟时间' },
                          { type: 'number', min: 1, message: '最大卖出延迟时间不能小于1秒' },
                        ]}
                        noStyle
                      >
                        <InputNumber
                          placeholder="最大值"
                          style={{ width: '100%' }}
                          min={1}
                          max={600}
                          addonAfter="秒"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form.Item>

                {/* 买入次数 */}
                <Form.Item
                  label="买入次数"
                  name="buyCount"
                  rules={[
                    { required: true, message: '请输入买入次数' },
                    { type: 'number', min: 1, max: 10, message: '买入次数必须在1-10之间' },
                  ]}
                  tooltip="每个池子的买入次数，完成指定次数后会自动卖出"
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    placeholder="买入次数"
                    style={{ width: '100%' }}
                    min={1}
                    max={10}
                  />
                </Form.Item>

                {/* 池子冷却时间 */}
                <Form.Item
                  label="池子冷却时间（分钟）"
                  name="poolCooldownMinutes"
                  rules={[
                    { type: 'number', min: 1, message: '池子冷却时间不能小于1分钟' },
                  ]}
                  tooltip="同一个池子再次被选择的最小间隔时间"
                  style={{ marginBottom: 16 }}
                >
                  <InputNumber
                    placeholder="池子冷却时间"
                    style={{ width: '100%' }}
                    min={1}
                    max={60}
                    addonAfter="分钟"
                  />
                </Form.Item>
              </Card>

              <Form.Item style={{ marginBottom: 0 }}>
                <Space style={{ width: '100%' }}>
                  {!isRunning ? (
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={handleStart}
                      loading={loading.start}
                      style={{ flex: 1 }}
                    >
                      启动
                    </Button>
                  ) : (
                    <Button
                      danger
                      icon={<PauseCircleOutlined />}
                      onClick={handleStop}
                      loading={loading.stop}
                      style={{ flex: 1 }}
                    >
                      停止
                    </Button>
                  )}
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 状态监控 */}
        <Col span={8}>
          <Card title="运行状态" loading={loading.refresh}>
            {status ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>会话状态: </Text>
                  <Badge
                    status={status.session.isRunning ? 'processing' : 'default'}
                    text={status.session.isRunning ? '运行中' : '已停止'}
                  />
                </div>

                <div>
                  <Text strong>运行时长: </Text>
                  <Text>{RandomPoolTradeService.formatDuration(status.session.runningDuration)}</Text>
                </div>

                <div>
                  <Text strong>活跃池子: </Text>
                  <Text>{status.statistics.pools.activePoolsCount}</Text>
                </div>

                <div>
                  <Text strong>已完成池子: </Text>
                  <Text>{status.statistics.pools.completedPoolsCount}</Text>
                </div>

                <Divider style={{ margin: '12px 0' }} />

                <div>
                  <Text strong>买入操作: </Text>
                  <Text>
                    {status.statistics.operations.successfulBuyOperations}/{status.statistics.operations.totalBuyOperations}
                    {status.statistics.operations.totalBuyOperations > 0 && (
                      <Text type="secondary">
                        {' '}({status.statistics.operations.buySuccessRate.toFixed(1)}%)
                      </Text>
                    )}
                  </Text>
                </div>

                <div>
                  <Text strong>卖出操作: </Text>
                  <Text>
                    {status.statistics.operations.successfulSellOperations}/{status.statistics.operations.totalSellOperations}
                    {status.statistics.operations.totalSellOperations > 0 && (
                      <Text type="secondary">
                        {' '}({status.statistics.operations.sellSuccessRate.toFixed(1)}%)
                      </Text>
                    )}
                  </Text>
                </div>

                <div>
                  <Text strong>总交易量: </Text>
                  <Text>{NumberUtils.formatPrice(status.statistics.volume.totalVolume)}</Text>
                </div>
              </Space>
            ) : (
              <Text type="secondary">暂无运行数据</Text>
            )}
          </Card>
        </Col>

        {/* 活跃池子状态 */}
        <Col span={8}>
          <Card title="活跃池子" loading={loading.refresh}>
            {status && status.activePools.length > 0 ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                {status.activePools.map((pool) => (
                  <Card key={pool.poolId} size="small" style={{ marginBottom: 8 }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>{pool.symbol}</Text>
                        <Badge
                          status={TradeStatusUtils.getStatusColor(pool.status) as 'success' | 'processing' | 'default' | 'error' | 'warning'}
                          text={TradeStatusUtils.getStatusText(pool.status)}
                          style={{ marginLeft: 8 }}
                        />
                      </div>

                      <div>
                        <Text type="secondary">累积买入: </Text>
                        <Text>{NumberUtils.formatPrice(pool.accumulatedAmount)}</Text>
                      </div>

                      <div>
                        <Text type="secondary">买入进度: </Text>
                        <Text>{pool.completedBuyCount}/{pool.targetBuyCount}</Text>
                      </div>
                    </Space>
                  </Card>
                ))}
              </Space>
            ) : (
              <Text type="secondary">暂无活跃池子</Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* 可选择池子列表 */}
      <Row style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                可选择池子列表
                <Tag color="blue">{selectablePools.length} 个池子</Tag>
              </Space>
            }
            loading={loading.pools}
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadSelectablePools}
                loading={loading.pools}
                size="small"
              >
                刷新池子
              </Button>
            }
          >
            {selectablePools.length > 0 ? (
              <Table
                dataSource={selectablePools}
                rowKey="poolId"
                pagination={{ pageSize: 10, showSizeChanger: true }}
                scroll={{ x: 800 }}
                size="small"
                columns={[
                  {
                    title: 'ID',
                    dataIndex: 'poolId',
                    key: 'poolId',
                    width: 60,
                  },
                  {
                    title: '代币符号',
                    dataIndex: 'symbol',
                    key: 'symbol',
                    width: 120,
                    render: (symbol: string) => <Text strong>{symbol}</Text>,
                  },
                  {
                    title: '稳定币',
                    dataIndex: 'stableCoinSymbol',
                    key: 'stableCoinSymbol',
                    width: 80,
                  },
                  {
                    title: '价格',
                    dataIndex: 'price',
                    key: 'price',
                    width: 120,
                    render: (price: number) => NumberUtils.formatPrice(price),
                  },
                  {
                    title: '流动性',
                    dataIndex: 'liquidity',
                    key: 'liquidity',
                    width: 120,
                    render: (liquidity: number) => NumberUtils.formatPrice(liquidity),
                  },
                  {
                    title: '24h交易量',
                    dataIndex: 'volume24h',
                    key: 'volume24h',
                    width: 120,
                    render: (volume: number) => NumberUtils.formatPrice(volume),
                  },
                  {
                    title: '状态',
                    dataIndex: 'poolStatus',
                    key: 'poolStatus',
                    width: 80,
                    render: (status: string) => (
                      <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
                        {status}
                      </Tag>
                    ),
                  },
                ]}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无可选择的池子</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  请检查内盘API连接或池子筛选条件
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 交易历史记录 */}
      <Row style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <HistoryOutlined />
                交易历史记录
                <Tag color="blue">{historyPagination.total} 条记录</Tag>
                {isRunning && (
                  <Badge
                    status="processing"
                    text="自动刷新中"
                    style={{ fontSize: '12px' }}
                  />
                )}
              </Space>
            }
            loading={loading.history}
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadTradeHistory(historyPagination.current, historyPagination.pageSize)}
                loading={loading.history}
                size="small"
              >
                刷新
              </Button>
            }
          >
            {tradeHistory.length > 0 ? (
              <Table
                dataSource={tradeHistory}
                rowKey={(record) => `${record.sessionId}_${record.timestamp}_${record.poolId}`}
                pagination={{
                  current: historyPagination.current,
                  pageSize: historyPagination.pageSize,
                  total: historyPagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  onChange: (page, pageSize) => {
                    loadTradeHistory(page, pageSize);
                  },
                }}
                scroll={{ x: 1200 }}
                size="small"
                columns={[
                  {
                    title: '时间',
                    dataIndex: 'timestamp',
                    key: 'timestamp',
                    width: 160,
                    render: (timestamp: number) => (
                      <Text style={{ fontSize: '12px' }}>
                        {new Date(timestamp).toLocaleString('zh-CN')}
                      </Text>
                    ),
                  },
                  {
                    title: '交易类型',
                    dataIndex: 'tradeType',
                    key: 'tradeType',
                    width: 80,
                    render: (tradeType: 'BUY' | 'SELL') => (
                      <Tag color={tradeType === 'BUY' ? 'green' : 'red'}>
                        {tradeType === 'BUY' ? '买入' : '卖出'}
                      </Tag>
                    ),
                  },
                  {
                    title: '交易者',
                    dataIndex: 'trader',
                    key: 'trader',
                    width: 180,
                    render: (trader?: { email: string; apiKey: string }) => (
                      trader ? (
                        <div style={{ fontSize: '11px' }}>
                          <div>
                            <Text strong>{trader.email}</Text>
                          </div>
                          <div>
                            <Text type="secondary" style={{ fontSize: '10px' }}>
                              {trader.apiKey.substring(0, 8)}...
                            </Text>
                          </div>
                        </div>
                      ) : (
                        <Text type="secondary" style={{ fontSize: '11px' }}>-</Text>
                      )
                    ),
                  },
                  {
                    title: '池子信息',
                    dataIndex: 'poolId',
                    key: 'poolInfo',
                    width: 120,
                    render: (poolId: number, record: TradeOperationRecord) => (
                      <div>
                        <Text strong style={{ fontSize: '12px' }}>ID: {poolId}</Text>
                        {record.symbol && (
                          <div>
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              {record.symbol}
                            </Text>
                          </div>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: '交易金额',
                    dataIndex: 'amount',
                    key: 'amount',
                    width: 100,
                    render: (amount: number) => (
                      <Text style={{ fontSize: '12px' }}>
                        {NumberUtils.formatPrice(amount)}
                      </Text>
                    ),
                  },
                  {
                    title: '状态',
                    dataIndex: 'success',
                    key: 'success',
                    width: 80,
                    render: (success: boolean) => (
                      <Tag color={success ? 'success' : 'error'}>
                        {success ? '成功' : '失败'}
                      </Tag>
                    ),
                  },
                  {
                    title: '交易ID',
                    dataIndex: 'transactionId',
                    key: 'transactionId',
                    width: 180,
                    ellipsis: true,
                    render: (transactionId: string) => (
                      transactionId ? (
                        <Text
                          style={{
                            fontSize: '11px',
                            fontFamily: 'monospace',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: 'block',
                            maxWidth: '160px'
                          }}
                          copyable={{ text: transactionId }}
                          title={transactionId}
                        >
                          {transactionId.length > 18
                            ? `${transactionId.substring(0, 18)}...`
                            : transactionId
                          }
                        </Text>
                      ) : (
                        <Text type="secondary" style={{ fontSize: '11px' }}>-</Text>
                      )
                    ),
                  },
                  {
                    title: '累积信息',
                    key: 'accumulation',
                    width: 120,
                    render: (record: TradeOperationRecord) => (
                      record.accumulationSnapshot ? (
                        <div style={{ fontSize: '11px' }}>
                          {record.accumulationSnapshot.accumulatedAmount > 0 && (
                            <div>
                              <Text type="secondary">累积: </Text>
                              <Text>{NumberUtils.formatPrice(record.accumulationSnapshot.accumulatedAmount)}</Text>
                            </div>
                          )}
                          <div>
                            <Text type="secondary">进度: </Text>
                            <Text>
                              {record.accumulationSnapshot.completedBuyCount}/
                              {record.accumulationSnapshot.targetBuyCount}
                            </Text>
                          </div>
                        </div>
                      ) : (
                        <Text type="secondary" style={{ fontSize: '11px' }}>-</Text>
                      )
                    ),
                  },
                  {
                    title: '错误信息',
                    dataIndex: 'error',
                    key: 'error',
                    width: 150,
                    render: (error: string) => (
                      error ? (
                        <Text type="danger" style={{ fontSize: '11px' }}>
                          {error.length > 30 ? `${error.substring(0, 30)}...` : error}
                        </Text>
                      ) : (
                        <Text type="secondary" style={{ fontSize: '11px' }}>-</Text>
                      )
                    ),
                  },
                ]}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无交易历史记录</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  启动交易后将显示详细的交易记录
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RandomPoolTradePage;
