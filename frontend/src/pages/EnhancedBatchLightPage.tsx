import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Form,
  Button,
  Row,
  Col,
  Space,
  Alert,
  Divider,
  Typography,
  Progress,
  Tabs,
} from 'antd';
import {
  PlayCircleOutlined,
  BarChartOutlined,
  HistoryOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';

// 类型导入
import type { EnhancedBatchLightResult } from '../services/enhancedBatchLightService';

// 自定义Hook导入
import { useAccountParser } from '../hooks/useAccountParser';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { useLivePreview } from '../hooks/useLivePreview';

import { useBatchOperations } from '../hooks/useBatchOperations';
import { useFormSubmission } from '../hooks/useFormSubmission';

import { useBatchConfigSuggestion } from '../hooks/useBatchConfigSuggestion';
import { useBalanceAutoUpdate } from '../hooks/useBalanceAutoUpdate';
import { useDataRefreshManager } from '../hooks/useDataRefreshManager';

// 子组件导入
import {
  TokenSearchSection,
  AccountConfigSection,
  AmountConfigSection,
  BatchConfigSection,
  BatchStatusModal,
} from '../components/EnhancedBatchLight';
import BatchHistorySection from '../components/BatchHistory/BatchHistorySection';


import type { FormValues } from '../types/formTypes';

const { Text, Title } = Typography;

/**
 * 增强点亮页面组件
 *
 * 功能特性：
 * - Token 搜索和资产ID配置
 * - 账户信息管理和性能优化提醒
 * - 随机金额分配和实时预览
 * - 批次处理配置和智能建议
 * - 首次点亮分析（默认启用）
 * - 执行状态监控和进度跟踪
 *
 * 架构特点：
 * - 高内聚低耦合的组件设计
 * - 自定义 hooks 管理复杂逻辑
 * - 子组件拆分提高可维护性
 * - TypeScript 类型安全保障
 */
const EnhancedBatchLightPage: React.FC = () => {
  const [form] = Form.useForm<FormValues>();
  const isMountedRef = useRef(true);

  // 使用自定义Hook
  const { parseUserBeanList, getAccountCount, parsedAccountsCache } = useAccountParser();
  const { showError, showSuccess } = useErrorHandler();
  const {
    symbolLoading,
    smartFilterLoading,
    lightableTokensLoading,
    loading,
    symbolResult,
    selectedAssetId,
    smartFilterResult,
    lightableTokens,
    currentBatch,
    handleSymbolSearch,
    handleSmartFilter,
    handleGetLightableTokens,
    handleSelectLightableToken,
    setSelectedAssetId,
    setCurrentBatch,
    setLoading,
    setLightableTokens,
  } = useBatchOperations(form);

  // 数据刷新管理器（需要在其他Hook之前初始化）
  const [externalAssetLightNum, setExternalAssetLightNum] = useState<number | undefined>(undefined);

  const dataRefreshManager = useDataRefreshManager(
    form,
    selectedAssetId,
    {
      refreshAssetLightNum: true,
      refreshAccountBalances: true,
      refreshLightableTokens: true,
      refreshSmartFilter: true,
      showProgress: true,
      autoRefreshAfterBatch: true,
    },
    {
      onAssetLightNumUpdate: (assetId, lightNum) => {
        console.log(`Asset ${assetId} 点亮数量更新为: ${lightNum}`);
        setExternalAssetLightNum(lightNum);
      },
      onBalanceRefresh: async () => {
        await refreshBalances();
      },
      onSmartFilterRefresh: async () => {
        await handleSmartFilter();
      },
      onLightableTokensRefresh: async () => {
        await handleGetLightableTokens();
      },
      onShowSuccess: showSuccess,
      onShowError: showError,
    }
  );

  // 实时预览Hook（需要在useBatchOperations之后调用以获取smartFilterResult）
  const {
    livePreviewSummary,
    batchConfigAnalysis
  } = useLivePreview(
    form,
    getAccountCount,
    smartFilterResult,
    selectedAssetId || undefined,
    externalAssetLightNum
  );



  // 本地状态（模态框等UI状态）
  const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);

  // 余额自动更新Hook（简化版本，只保留必要功能）
  const {
    recordBatchLightOperations,
    handleBatchComplete,
    forceRefresh: refreshBalances,
  } = useBalanceAutoUpdate(
    parsedAccountsCache?.accounts?.map((account: { apiKey: string }) => account.apiKey) || [],
    {
      enableAutoUpdate: true,
      updateInterval: 30000, // 30秒自动更新
      enableEstimation: true,
      syncAfterBatch: true,
    }
  );

  // 注意：已移除旧的 usePageRefresh Hook，统一使用 useDataRefreshManager

  // 批次配置建议Hook
  const { applyBatchConfigSuggestion } = useBatchConfigSuggestion({
    form,
    batchConfigAnalysis,
    showSuccess,
  });

  // 增强的批次完成处理函数（集成数据刷新管理器）
  const enhancedBatchComplete = useCallback(async (batchResult: EnhancedBatchLightResult) => {
    console.log('批次完成，开始数据刷新流程...');

    try {
      // 先执行原有的余额更新逻辑
      await handleBatchComplete(batchResult);

      // 使用新的数据刷新管理器执行全面刷新
      await dataRefreshManager.handleBatchComplete(batchResult);

    } catch (error) {
      console.error('批次完成处理失败:', error);
      showError(error, '数据刷新失败，请手动刷新页面');
    }
  }, [handleBatchComplete, dataRefreshManager, showError]);

  // 表单提交Hook
  const { handleSubmit } = useFormSubmission({
    parseUserBeanList,
    selectedAssetId,
    setLoading,
    setCurrentBatch,
    setBatchStatusModalVisible,
    showError,
    showSuccess,
    onBatchOperations: recordBatchLightOperations,
    onBatchComplete: enhancedBatchComplete,
  });

  // 手动刷新批次状态（增强版本，支持取消）
  const handleRefreshBatchStatus = useCallback(async (batchId: string) => {
    if (!isMountedRef.current || !batchId) {
      return;
    }

    try {
      const response = await import('../services/enhancedBatchLightService').then(
        module => module.EnhancedBatchLightService.getBatchStatus(batchId)
      );

      // 双重检查组件是否仍然挂载
      if (isMountedRef.current && response.code === 200 && response.data) {
        setCurrentBatch(response.data);
      }
    } catch (error) {
      // 只有在组件仍然挂载时才显示错误
      if (isMountedRef.current) {
        console.error('刷新批次状态失败:', error);
        showError(error, '刷新批次状态失败');
      }
    }
  }, [showError, setCurrentBatch]);

  // 初始化表单默认值
  useEffect(() => {
    form.setFieldsValue({
      enableRandomAmount: false,
      enableBatchConfig: true,
      batchSize: 50,
      intervalMs: 1000,
      // enableFirstLightAnalysis 已默认启用，无需设置
      amount: 1,
      enableCustomLightCount: false,
      customLightCount: 100,
    });
  }, [form]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      dataRefreshManager.cleanup();
    };
  }, [dataRefreshManager]);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>增强点亮</Title>
      <Text type="secondary">
        集成首次点亮分析、随机金额分配、批次处理等高级功能的批量点亮工具
      </Text>

      <Tabs
        defaultActiveKey="batch-light"
        style={{ marginTop: 24 }}
        items={[
          {
            key: 'batch-light',
            label: (
              <span>
                <ThunderboltOutlined />
                批量点亮
              </span>
            ),
            children: (
              <div>
                <Alert
                  message="使用提示"
                  description={
                    <div>
                      <p>1. 点击"获取可点亮Token"按钮获取当前可用的Token列表</p>
                      <p>2. 或者在Token符号框中搜索特定Token（如：愤怒蜡笔、BTC等）</p>
                      <p>3. 使用"筛选有余额账户"功能自动过滤有NINE余额的账户</p>
                      <p>4. 实时预览功能会自动显示配置效果，包括账户数量和预计金额</p>
                      <p><strong>性能优化：</strong>建议单次处理账户数量不超过1000个，大量账户请分批处理</p>
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: 24 }}
                  closable
                />



      {/* 性能警告 */}
      {parsedAccountsCache && parsedAccountsCache.accounts && parsedAccountsCache.accounts.length > 1200 && (
        <Alert
          message="性能提醒"
          description={
            <div>
              <p>检测到您输入了 {parsedAccountsCache.count} 个账户，数量较大可能影响性能。</p>
              <p>建议：</p>
              <ul>
                <li>启用"自定义账户数量"功能，限制单次处理数量</li>
                <li>或将账户分批处理，每批不超过500个</li>
                <li>使用"余额筛选"功能预先过滤有效账户</li>
              </ul>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          closable
        />
      )}

      <Card style={{ marginTop: '24px' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* Token 搜索和资产配置区域 */}
          <TokenSearchSection
            symbolLoading={symbolLoading}
            smartFilterLoading={smartFilterLoading}
            lightableTokensLoading={lightableTokensLoading}
            symbolResult={symbolResult}
            lightableTokens={lightableTokens}
            smartFilterResult={smartFilterResult}
            onSymbolSearch={handleSymbolSearch}
            onSmartFilter={handleSmartFilter}
            onGetLightableTokens={handleGetLightableTokens}
            onSelectLightableToken={handleSelectLightableToken}
            onSelectAssetId={(assetId) => {
              setSelectedAssetId(assetId);
              form.setFieldsValue({ assetId: assetId.toString() });
            }}
            onSetLightableTokens={setLightableTokens}
            showSuccess={showSuccess}
            // 页面刷新相关
            pageRefreshing={dataRefreshManager.isRefreshing}
            refreshProgress={dataRefreshManager.refreshStatus.progress > 0 ? {
              step: dataRefreshManager.refreshStatus.currentStep,
              progress: dataRefreshManager.refreshStatus.progress,
              total: dataRefreshManager.refreshStatus.total,
              isComplete: dataRefreshManager.refreshStatus.progress >= dataRefreshManager.refreshStatus.total
            } : undefined}
            onManualRefresh={async () => {
              try {
                console.log('触发手动刷新...');
                await dataRefreshManager.manualRefresh();
              } catch (error) {
                console.error('手动刷新失败:', error);
                showError(error, '手动刷新失败');
              }
            }}
          />

          <Divider />

          {/* 账户信息配置区域 */}
          <AccountConfigSection parsedAccountsCache={parsedAccountsCache} />

          <Divider />

          {/* 点亮配置区域：金额设置和批次配置 */}
          <Row gutter={[24, 24]}>
            <AmountConfigSection
              livePreviewSummary={livePreviewSummary}
            />

            <BatchConfigSection
              batchConfigAnalysis={batchConfigAnalysis}
              onApplyBatchConfigSuggestion={applyBatchConfigSuggestion}
            />
          </Row>

          <Divider />

          {/* 提交按钮和进度显示 */}
          <Row>
            <Col span={24}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<PlayCircleOutlined />}
                    size="large"
                  >
                    {loading ? '正在执行批量点亮...' : '开始增强点亮'}
                  </Button>

                  <Button
                    icon={<BarChartOutlined />}
                    onClick={() => setBatchStatusModalVisible(true)}
                    disabled={!currentBatch}
                    size="large"
                  >
                    查看执行状态
                  </Button>
                </Space>

                {/* 执行进度提示 */}
                {loading && (
                  <Alert
                    message="批量点亮执行中"
                    description={
                      <div>
                        <p>正在处理您的批量点亮请求，请耐心等待...</p>
                        <p>• 大量账户处理可能需要几分钟时间</p>
                        <p>• 系统会自动重试失败的请求</p>
                        <p>• 您可以在执行完成后查看详细状态</p>
                        <Progress percent={undefined} status="active" />
                      </div>
                    }
                    type="info"
                    showIcon
                    style={{ marginTop: 16 }}
                  />
                )}
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

                {/* 批次执行状态监控模态框 */}
                <BatchStatusModal
                  visible={batchStatusModalVisible}
                  currentBatch={currentBatch}
                  onClose={() => setBatchStatusModalVisible(false)}
                  onRefresh={handleRefreshBatchStatus}
                  onBatchComplete={handleBatchComplete}
                />
              </div>
            ),
          },
          {
            key: 'history',
            label: (
              <span>
                <HistoryOutlined />
                操作历史
              </span>
            ),
            children: <BatchHistorySection />,
          },
        ]}
      />
    </div>
  );
};

export default EnhancedBatchLightPage;
