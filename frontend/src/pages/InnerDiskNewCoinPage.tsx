import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  Alert,
  Space,
  Typography,
  Badge,
  message,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Statistic,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';

import { InnerDiskNewCoinService } from '../services/innerDiskNewCoinService';
import type {
  InnerDiskNewCoinConfig,
  MonitoringStatus,
  CreateConfigRequest,
} from '../types/innerDiskNewCoin';

const { Title } = Typography;
const { Option } = Select;

/**
 * 内盘打新页面 - 简化版本
 */
const InnerDiskNewCoinPage: React.FC = () => {
  const [config, setConfig] = useState<InnerDiskNewCoinConfig | null>(null);
  const [status, setStatus] = useState<MonitoringStatus | null>(null);
  const [loading, setLoading] = useState({
    config: false,
    status: false,
    start: false,
    stop: false,
    save: false,
  });
  const [form] = Form.useForm();

  // 加载配置
  const loadConfig = useCallback(async () => {
    setLoading(prev => ({ ...prev, config: true }));
    try {
      const response = await InnerDiskNewCoinService.getConfig();
      if (response.success && response.data) {
        setConfig(response.data);
        form.setFieldsValue({
          isEnabled: response.data.isEnabled,
          apiKey: response.data.apiKey || '',
          secret: response.data.secret || '',
          purchaseAmount: response.data.purchaseAmount,
          purchaseType: response.data.purchaseType,
          profitRate: response.data.profitRate * 100,
          monitorInterval: response.data.monitorInterval / 1000,
          maxPositions: response.data.maxPositions,

        });
      }
    } catch {
      message.error('加载配置失败');
    } finally {
      setLoading(prev => ({ ...prev, config: false }));
    }
  }, [form]);

  // 加载状态
  const loadStatus = useCallback(async () => {
    setLoading(prev => ({ ...prev, status: true }));
    try {
      const response = await InnerDiskNewCoinService.getStatus();
      if (response.success && response.data) {
        setStatus(response.data);
      }
    } catch {
      message.error('加载状态失败');
    } finally {
      setLoading(prev => ({ ...prev, status: false }));
    }
  }, []);

  // 保存配置
  const handleSave = async (values: {
    isEnabled: boolean;
    apiKey: string;
    secret: string;
    purchaseAmount: number;
    purchaseType: 'FIXED' | 'PERCENTAGE';
    profitRate: number;
    monitorInterval: number;
    maxPositions: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  }) => {
    setLoading(prev => ({ ...prev, save: true }));
    try {
      const configRequest: CreateConfigRequest = {
        isEnabled: values.isEnabled,
        apiKey: values.apiKey,
        secret: values.secret,
        purchaseAmount: values.purchaseAmount,
        purchaseType: values.purchaseType,
        profitRate: values.profitRate / 100,
        monitorInterval: values.monitorInterval * 1000,
        maxPositions: values.maxPositions,

      };

      const response = await InnerDiskNewCoinService.createOrUpdateConfig(configRequest);
      if (response.success && response.data) {
        message.success('配置保存成功');
        setConfig(response.data);
      } else {
        message.error(response.message);
      }
    } catch {
      message.error('保存配置失败');
    } finally {
      setLoading(prev => ({ ...prev, save: false }));
    }
  };

  // 启动监控
  const handleStart = async () => {
    setLoading(prev => ({ ...prev, start: true }));
    try {
      const response = await InnerDiskNewCoinService.startMonitoring();
      if (response.success) {
        message.success('监控启动成功');
        loadStatus();
      } else {
        message.error(response.message);
      }
    } catch {
      message.error('启动监控失败');
    } finally {
      setLoading(prev => ({ ...prev, start: false }));
    }
  };

  // 停止监控
  const handleStop = async () => {
    setLoading(prev => ({ ...prev, stop: true }));
    try {
      const response = await InnerDiskNewCoinService.stopMonitoring();
      if (response.success) {
        message.success('监控已停止');
        loadStatus();
      } else {
        message.error(response.message);
      }
    } catch {
      message.error('停止监控失败');
    } finally {
      setLoading(prev => ({ ...prev, stop: false }));
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    await Promise.all([loadConfig(), loadStatus()]);
  };

  useEffect(() => {
    loadConfig();
    loadStatus();
  }, [loadConfig, loadStatus]);

  const isRunning = status?.isRunning || false;
  const canStart = config && config.isEnabled && !isRunning;

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            内盘打新
          </Title>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading.config || loading.status}
          >
            刷新
          </Button>
        </Col>
      </Row>

      <Row gutter={24}>
        {/* 左侧：配置面板 */}
        <Col span={12}>
          <Card title={<><SettingOutlined /> 配置管理</>} loading={loading.config}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                isEnabled: false,
                apiKey: '',
                secret: '',
                purchaseType: 'FIXED',
                profitRate: 10,
                monitorInterval: 5,
                maxPositions: 5,

              }}
            >
              <Form.Item name="isEnabled" label="启用监控" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item
                name="apiKey"
                label="API Key"
                rules={[{ required: true, message: '请输入API Key' }]}
                extra="用于内盘交易的API密钥"
              >
                <Input.Password
                  placeholder="请输入您的API Key"
                  visibilityToggle
                />
              </Form.Item>

              <Form.Item
                name="secret"
                label="API Secret"
                rules={[{ required: true, message: '请输入API Secret' }]}
                extra="用于内盘交易的API密钥"
              >
                <Input.Password
                  placeholder="请输入您的API Secret"
                  visibilityToggle
                />
              </Form.Item>

              <Form.Item
                name="purchaseAmount"
                label="购买数量 (USDT)"
                rules={[{ required: true, message: '请输入购买数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0.01}
                  step={0.01}
                  placeholder="例如: 100"
                />
              </Form.Item>

              <Form.Item name="purchaseType" label="购买方式">
                <Select>
                  <Option value="FIXED">固定数量</Option>
                  <Option value="PERCENTAGE">按余额百分比</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="profitRate"
                label="止盈率 (%)"
                rules={[{ required: true, message: '请输入止盈率' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={1000}
                  step={1}
                  placeholder="例如: 10"
                />
              </Form.Item>

              <Form.Item name="monitorInterval" label="监控间隔 (秒)">
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={60}
                  step={1}
                  placeholder="例如: 5"
                />
              </Form.Item>

              <Form.Item name="maxPositions" label="最大持仓数量">
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={20}
                  step={1}
                  placeholder="例如: 5"
                />
              </Form.Item>



              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading.save}
                  block
                >
                  保存配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 右侧：状态面板 */}
        <Col span={12}>
          <Card title="监控状态" loading={loading.status}>
            {/* 控制按钮 */}
            <Space style={{ marginBottom: '24px' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={loading.start}
                disabled={!canStart}
                onClick={handleStart}
              >
                启动监控
              </Button>
              <Button
                danger
                icon={<PauseCircleOutlined />}
                loading={loading.stop}
                disabled={!isRunning}
                onClick={handleStop}
              >
                停止监控
              </Button>
              <Badge
                status={isRunning ? 'processing' : 'default'}
                text={isRunning ? '运行中' : '已停止'}
              />
            </Space>

            {/* 状态提示 */}
            {!config && (
              <Alert
                message="请先配置监控参数"
                type="warning"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {config && !config.isEnabled && (
              <Alert
                message="监控未启用"
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {/* 统计信息 */}
            {status && (
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="检测到新币"
                    value={status.totalDetected}
                    suffix="个"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="执行交易"
                    value={status.totalTrades}
                    suffix="笔"
                  />
                </Col>
                <Col span={12} style={{ marginTop: '16px' }}>
                  <Statistic
                    title="成功交易"
                    value={status.successfulTrades}
                    suffix="笔"
                  />
                </Col>
                <Col span={12} style={{ marginTop: '16px' }}>
                  <Statistic
                    title="总盈亏"
                    value={status.totalProfit.toFixed(4)}
                    suffix="USDT"
                    valueStyle={{
                      color: status.totalProfit >= 0 ? '#52c41a' : '#ff4d4f'
                    }}
                  />
                </Col>
              </Row>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default InnerDiskNewCoinPage;
