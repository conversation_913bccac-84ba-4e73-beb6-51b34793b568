import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Button,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic,
  Table,
  Alert,
  Space,
  Typography,
  Tag,
  Tooltip,
  message,
  Spin,
  Divider,
  Badge,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  HistoryOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import { RandomLightService } from '../services/randomLightService';
import { TimeUtils } from '../utils/commonUtils';
import type {
  RandomLightStatus,
  RandomLightStatistics,
  RandomLightExecution,
  RandomLightEvent,
  StartRandomLightRequest,
} from '../types/randomLight';

const { Title, Text } = Typography;

/**
 * 连接管理Hook
 */
const useEventSourceConnection = (
  onEvent: (event: RandomLightEvent) => void,
  onDataRefresh: () => void
) => {
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    // 清除重连定时器
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }

    // 关闭现有连接
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }

    setConnectionStatus('connecting');

    try {
      const es = RandomLightService.createEventSource();
      
      es.onopen = () => {
        setConnectionStatus('connected');
      };

      es.onmessage = (event) => {
        const parsedEvent = RandomLightService.parseEvent(event);
        if (parsedEvent) {
          onEvent(parsedEvent);
        }
      };

      es.onerror = () => {
        setConnectionStatus('disconnected');
        onDataRefresh(); // 连接断开时刷新数据
        
        // 5秒后重连
        reconnectTimerRef.current = setTimeout(() => {
          if (es.readyState === EventSource.CLOSED) {
            connect();
          }
        }, 5000);
      };

      setEventSource(es);
    } catch (error) {
      console.error('Failed to create EventSource:', error);
      setConnectionStatus('disconnected');
    }
  }, [onEvent, onDataRefresh, eventSource]);

  const disconnect = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
    setConnectionStatus('disconnected');
  }, [eventSource]);

  return { connectionStatus, connect, disconnect };
};

/**
 * 随机点亮页面组件
 */
const RandomLightPage: React.FC = () => {
  const [form] = Form.useForm();
  const [status, setStatus] = useState<RandomLightStatus | null>(null);
  const [statistics, setStatistics] = useState<RandomLightStatistics | null>(null);
  const [history, setHistory] = useState<RandomLightExecution[]>([]);
  const [loading, setLoading] = useState({
    status: false,
    start: false,
    stop: false,
    refresh: false,
  });

  /**
   * 加载初始数据
   */
  const loadData = useCallback(async () => {
    setLoading(prev => ({ ...prev, status: true }));
    try {
      const [statusRes, statisticsRes, historyRes] = await Promise.all([
        RandomLightService.getStatus(),
        RandomLightService.getStatistics(),
        RandomLightService.getHistory(),
      ]);

      if (statusRes.code === 200) {
        setStatus(statusRes.data);
      }

      if (statisticsRes.code === 200) {
        setStatistics(statisticsRes.data);
      }

      if (historyRes.code === 200) {
        setHistory(historyRes.data);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(prev => ({ ...prev, status: false }));
    }
  }, []);

  /**
   * 启动随机点亮
   */
  const handleStart = async () => {
    try {
      const values = await form.validateFields();
      setLoading(prev => ({ ...prev, start: true }));

      const request: StartRandomLightRequest = {
        intervalMs: values.intervalMs || 3000,
        amount: values.amount || 1,
      };

      const response = await RandomLightService.startRandomLight(request);
      
      if (response.code === 200) {
        message.success('随机点亮已启动');
        await loadData();
      } else {
        message.error(response.msg || '启动失败');
      }
    } catch (error) {
      console.error('启动失败:', error);
      message.error('启动失败');
    } finally {
      setLoading(prev => ({ ...prev, start: false }));
    }
  };

  /**
   * 停止随机点亮
   */
  const handleStop = async () => {
    setLoading(prev => ({ ...prev, stop: true }));
    try {
      const response = await RandomLightService.stopRandomLight();
      
      if (response.code === 200) {
        message.success('随机点亮已停止');
        await loadData();
      } else {
        message.error(response.msg || '停止失败');
      }
    } catch (error) {
      console.error('停止失败:', error);
      message.error('停止失败');
    } finally {
      setLoading(prev => ({ ...prev, stop: false }));
    }
  };

  /**
   * 刷新数据
   */
  const handleRefresh = async () => {
    setLoading(prev => ({ ...prev, refresh: true }));
    try {
      await loadData();
      message.success('数据已刷新');
    } finally {
      setLoading(prev => ({ ...prev, refresh: false }));
    }
  };

  /**
   * 处理实时事件
   */
  const handleRealtimeEvent = useCallback((event: RandomLightEvent) => {
    console.log('收到实时事件:', event);
    
    switch (event.type) {
      case 'status':
        // 处理状态更新事件
        if (event.data && typeof event.data === 'object') {
          const statusData = event.data as RandomLightStatus;
          
          // 验证状态数据的有效性
          if (typeof statusData.uptime === 'number' && 
              isFinite(statusData.uptime) && 
              statusData.uptime >= 0 &&
              typeof statusData.intervalMs === 'number' &&
              isFinite(statusData.intervalMs)) {
            console.log('更新状态数据:', statusData);
            setStatus(statusData);
          } else {
            console.warn('收到无效的状态数据:', statusData);
          }
        }
        break;
      case 'execution':
        console.log('收到执行事件，刷新数据');
        loadData();
        break;
      case 'error': {
        const errorMsg = (event.data as { error?: string })?.error || '未知错误';
        console.error('收到错误事件:', errorMsg);
        message.error(`随机点亮错误: ${errorMsg}`);
        break;
      }
      case 'stopped':
        console.log('收到停止事件');
        message.info('随机点亮已停止');
        loadData();
        break;
      case 'connected':
        console.log('EventSource连接已建立');
        break;
      default:
        console.log('收到未知事件类型:', event.type);
    }
  }, [loadData]);

  // 使用连接管理Hook
  const { connectionStatus, connect, disconnect } = useEventSourceConnection(
    handleRealtimeEvent,
    loadData
  );

  /**
   * 组件挂载时初始化
   */
  useEffect(() => {
    loadData();
    connect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * 组件卸载时清理
   */
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  /**
   * 历史记录表格列定义
   */
  const historyColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp: string) => (
        <Text style={{ fontSize: '12px' }}>
          {TimeUtils.formatUTC8DateTime(timestamp, false)}
        </Text>
      ),
    },
    {
      title: '账户',
      dataIndex: 'accountEmail',
      key: 'accountEmail',
      width: 180,
      render: (email: string) => (
        <Tooltip title={email}>
          <Text ellipsis style={{ maxWidth: '160px' }}>
            {email}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '资产',
      key: 'asset',
      width: 120,
      render: (record: RandomLightExecution) => (
        <div>
          <Text strong>{record.assetSymbol}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '11px' }}>
            ID: {record.assetId}
          </Text>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center' as const,
      render: (status: string) => (
        <Tag color={RandomLightService.getStatusColor(status)}>
          {RandomLightService.getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      align: 'center' as const,
      render: (duration: number) => (
        <Text style={{ fontSize: '12px' }}>
          {TimeUtils.formatDuration(duration)}
        </Text>
      ),
    },
    {
      title: '点亮数',
      key: 'lightNum',
      width: 100,
      align: 'center' as const,
      render: (record: RandomLightExecution) => (
        <div style={{ fontSize: '12px' }}>
          {record.lightNumBefore}
          {record.lightNumAfter !== undefined && (
            <>
              <span style={{ color: '#1890ff' }}> → </span>
              {record.lightNumAfter}
            </>
          )}
        </div>
      ),
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      render: (message: string) => (
        <Tooltip title={message}>
          <Text ellipsis>{message}</Text>
        </Tooltip>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <ThunderboltOutlined style={{ marginRight: '8px' }} />
          随机点亮
        </Title>
        <Text type="secondary">
          自动随机选择有余额的账户和可点亮的资产进行点亮操作
        </Text>
      </div>

      {/* 连接状态提示 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={24}>
          <Alert
            message={
              <Space>
                <Badge 
                  status={connectionStatus === 'connected' ? 'success' : 
                         connectionStatus === 'connecting' ? 'processing' : 'error'} 
                  text={connectionStatus === 'connected' ? '已连接' : 
                       connectionStatus === 'connecting' ? '连接中' : '已断开'}
                />
                {connectionStatus !== 'connected' && (
                  <Button 
                    size="small" 
                    type="link" 
                    onClick={connect}
                  >
                    重新连接
                  </Button>
                )}
              </Space>
            }
            type={connectionStatus === 'connected' ? 'success' : 'warning'}
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 控制面板 */}
        <Col span={8}>
          <Card title="控制面板" extra={
            <Space>
              <Badge 
                status={connectionStatus === 'connected' ? 'success' : 
                       connectionStatus === 'connecting' ? 'processing' : 'error'} 
                text={connectionStatus === 'connected' ? '已连接' : 
                     connectionStatus === 'connecting' ? '连接中' : '已断开'}
              />
              {connectionStatus === 'connected' ? (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading.refresh}
                  size="small"
                  title="刷新数据"
                />
              ) : (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={connect}
                  size="small"
                  type="primary"
                  title="重新连接"
                />
              )}
            </Space>
          }>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                intervalMs: 3000,
                amount: 1,
              }}
            >
              <Form.Item
                label="点亮间隔 (毫秒)"
                name="intervalMs"
                rules={[
                  { required: true, message: '请输入点亮间隔' },
                  { type: 'number', min: 1000, max: 300000, message: '间隔时间应在1-300秒之间' },
                ]}
              >
                <InputNumber
                  min={1000}
                  max={300000}
                  step={1000}
                  style={{ width: '100%' }}
                  placeholder="3000"
                  addonAfter="ms"
                />
              </Form.Item>

              <Form.Item
                label="点亮数量"
                name="amount"
                rules={[
                  { required: true, message: '请输入点亮数量' },
                  { type: 'number', min: 1, max: 10, message: '数量应在1-10之间' },
                ]}
              >
                <InputNumber
                  min={1}
                  max={10}
                  style={{ width: '100%' }}
                  placeholder="1"
                />
              </Form.Item>

              <Form.Item>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handleStart}
                    loading={loading.start}
                    disabled={status?.isRunning || loading.stop}
                    style={{ flex: 1 }}
                  >
                    启动
                  </Button>
                  <Button
                    danger
                    icon={<PauseCircleOutlined />}
                    onClick={handleStop}
                    loading={loading.stop}
                    disabled={!status?.isRunning || loading.start}
                    style={{ flex: 1 }}
                  >
                    停止
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 运行状态 */}
        <Col span={16}>
          <Card title="运行状态" loading={loading.status}>
            {status ? (
              <>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="运行状态"
                      value={status.isRunning ? '运行中' : '已停止'}
                      valueStyle={{ 
                        color: status.isRunning ? '#3f8600' : '#cf1322',
                        fontSize: '18px'
                      }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="运行时长"
                      value={TimeUtils.formatDuration(status.uptime)}
                      valueStyle={{ fontSize: '18px' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="执行次数"
                      value={status.totalExecutions}
                      valueStyle={{ fontSize: '18px' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="成功率"
                      value={status.totalExecutions > 0 
                        ? RandomLightService.formatSuccessRate((status.successCount / status.totalExecutions) * 100)
                        : '0%'
                      }
                      valueStyle={{ 
                        color: status.successCount > status.failedCount ? '#3f8600' : '#cf1322',
                        fontSize: '18px'
                      }}
                    />
                  </Col>
                </Row>

                <Divider />

                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="成功次数"
                      value={status.successCount}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="失败次数"
                      value={status.failedCount}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="点亮间隔"
                      value={status.intervalMs > 0 ? `${status.intervalMs}ms` : '未设置'}
                    />
                  </Col>
                </Row>

                {status.currentAccount && (
                  <>
                    <Divider />
                    <div>
                      <Text strong>当前选中账户：</Text>
                      <Text>{status.currentAccount}</Text>
                      <br />
                      <Text strong>当前选中资产：</Text>
                      <Text>
                        {status.currentAssetSymbol} (ID: {status.currentAssetId})
                      </Text>
                    </div>
                  </>
                )}
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">加载状态中...</Text>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <BarChartOutlined />
                <span>统计信息</span>
              </Space>
            }
          >
            {statistics ? (
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="总执行次数"
                    value={statistics.totalExecutions}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="总成功次数"
                    value={statistics.totalSuccessCount}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="总失败次数"
                    value={statistics.totalFailedCount}
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="平均成功率"
                    value={RandomLightService.formatSuccessRate(statistics.successRate)}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
              </Row>
            ) : (
              <Spin />
            )}
          </Card>
        </Col>
      </Row>

      {/* 执行历史 */}
      <Row gutter={16} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <HistoryOutlined />
                <span>执行历史</span>
              </Space>
            }
          >
            <Table
              columns={historyColumns}
              dataSource={history}
              rowKey="id"
              size="small"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RandomLightPage;