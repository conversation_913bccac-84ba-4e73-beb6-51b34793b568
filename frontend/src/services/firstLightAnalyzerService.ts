import api from './api';

// 首次点亮查询请求接口
export interface FirstLightQueryRequest {
  userBeanList: Array<{
    apiKey: string;
    secret: string;
  }>;
  assetId: number;
  postId?: number;
}

// 账户分析结果接口
export interface AccountAnalysis {
  apiKey: string;
  canLight: boolean;
  reason: string;
  balance?: number;
  lastLightTime?: string;
  lightCount?: number;
}

// 首次点亮分析响应接口
export interface FirstLightAnalysisResponse {
  code: number;
  msg: string;
  data: {
    totalAccounts: number;
    firstLightAccounts: number;
    alreadyLightedAccounts: number;
    accountAnalysis: AccountAnalysis[];
  } | null;
}

/**
 * 首次点亮分析服务
 * 提供账户首次点亮状态分析功能
 */
export class FirstLightAnalyzerService {
  /**
   * 分析账户首次点亮状态
   */
  static async analyzeFirstLight(
    request: FirstLightQueryRequest
  ): Promise<FirstLightAnalysisResponse> {
    try {
      const response = await api.post<FirstLightAnalysisResponse>(
        '/batch/analyze-first-light',
        request
      );
      return response.data;
    } catch (error) {
      console.error('首次点亮分析失败:', error);
      return {
        code: 500,
        msg: '首次点亮分析失败',
        data: null,
      };
    }
  }

  /**
   * 获取首次点亮批次
   */
  static async getFirstLightBatch(
    request: FirstLightQueryRequest
  ): Promise<FirstLightAnalysisResponse> {
    try {
      const response = await api.post<FirstLightAnalysisResponse>(
        '/batch/get-first-light-batch',
        request
      );
      return response.data;
    } catch (error) {
      console.error('获取首次点亮批次失败:', error);
      return {
        code: 500,
        msg: '获取首次点亮批次失败',
        data: null,
      };
    }
  }

  /**
   * 检查单个账户首次点亮状态
   */
  static async checkSingleAccountFirstLight(
    apiKey: string,
    assetId: number,
    postId?: number
  ): Promise<{
    code: number;
    msg: string;
    data: AccountAnalysis | null;
  }> {
    try {
      const response = await api.post<{
        code: number;
        msg: string;
        data: AccountAnalysis | null;
      }>('/batch/check-single-first-light', {
        apiKey,
        assetId,
        postId,
      });
      return response.data;
    } catch (error) {
      console.error('检查单个账户首次点亮状态失败:', error);
      return {
        code: 500,
        msg: '检查失败',
        data: null,
      };
    }
  }
}
