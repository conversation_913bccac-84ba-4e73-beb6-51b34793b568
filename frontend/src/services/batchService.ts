import api, { batchApi } from './api';
import type { BatchLightExecutionHistoryDto } from './enhancedBatchLightService';





// 批量点亮请求接口
export interface BatchLightRequest {
  userBeanList: {
    apiKey: string;
    secret: string;
  }[];
  assetId: number;
  amount: number;
}

// 交易结果接口
export interface TradeResult {
  apiKey: string;
  success: boolean;
  message: string;
  transactionId?: string;
}

// 批量交易响应接口
export interface BatchTradeResponse {
  success: boolean;
  message: string;
  results: TradeResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// 操作历史记录接口
export interface HistoryRecord {
  id: string;
  type: 'login' | 'trade';
  timestamp: string;
  accountCount: number;
  successCount: number;
  failedCount: number;
  details?: Record<string, unknown>;
}

// 操作历史响应接口
export interface HistoryResponse {
  success: boolean;
  data: HistoryRecord[];
  total: number;
}

// 点亮记录接口
export interface LightRecord {
  id: string;
  postId: string;
  assetId: string;
  lightNum: number;
  apiKey: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: string;
  message?: string;
}

// 点亮记录响应接口
export interface LightRecordsResponse {
  success: boolean;
  data: LightRecord[];
  total: number;
}

export class BatchService {

  // 批量点亮（新接口，使用长超时）
  static async batchLight(request: BatchLightRequest): Promise<BatchTradeResponse> {
    const response = await batchApi.post<BatchTradeResponse>('/batch/light', request);
    return response.data;
  }

  // 获取操作历史 (新版本使用增强批量点亮历史记录)
  static async getHistory(): Promise<HistoryResponse> {
    try {
      // 使用新的增强批量点亮历史记录API
      const response = await api.get<{ code: number; msg: string; data: BatchLightExecutionHistoryDto[] }>('/batch/enhanced-light/execution-history');
      
      if (response.data.code === 200) {
        // 转换新格式到旧格式以保持兼容性
        const convertedData = response.data.data.map((item: BatchLightExecutionHistoryDto) => ({
          id: item.id,
          type: 'trade' as const,
          timestamp: item.timestamp,
          accountCount: 1, // 单次执行记录
          successCount: item.status === 'success' ? 1 : 0,
          failedCount: item.status === 'success' ? 0 : 1,
          details: {
            isBatchExecution: true,
            accountEmail: item.accountEmail,
            accountApiKey: item.accountApiKey,
            assetSymbol: item.assetSymbol,
            assetName: item.assetName,
            amount: item.amount,
            lightNumBefore: item.lightNumBefore,
            lightNumAfter: item.lightNumAfter,
            duration: item.duration,
            batchIndex: item.batchIndex,
            status: item.status,
            message: item.message,
          }
        }));

        return {
          success: true,
          data: convertedData,
          total: convertedData.length,
        };
      } else {
        throw new Error(response.data.msg || '获取历史记录失败');
      }
    } catch (error) {
      console.error('获取增强批量点亮历史失败:', error);
      return { success: false, data: [], total: 0 };
    }
  }

  // 获取点亮记录
  static async getLightRecords(): Promise<LightRecordsResponse> {
    const response = await api.get<LightRecordsResponse>('/batch/records');
    return response.data;
  }
}
