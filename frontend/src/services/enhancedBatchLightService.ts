import api, { batchApi } from './api';
import { BaseApiService } from './base/BaseService';
import { TimeUtils } from '../utils/commonUtils';


// 用户点亮信息接口
export interface UserLightBean {
  apiKey: string;
  secret: string;
}

// 可点亮Token接口
export interface LightableToken {
  symbol: string;
  assetId: number;
  lightNum: number;
}

// 随机数量配置接口
export interface RandomAmountConfig {
  enabled: boolean;
  minAmount: number;
  maxAmount: number;
}

// 批次配置接口
export interface BatchConfig {
  batchSize: number;
  intervalMs: number;
}

// 增强的批量点亮请求接口
export interface EnhancedBatchLightRequest {
  userBeanList: UserLightBean[];
  assetId: number;
  amount?: number;
  randomAmount?: RandomAmountConfig;
  batchConfig?: BatchConfig;
  enableFirstLightAnalysis?: boolean;
  customLightCount?: number; // 自定义点亮数量
}

// 批次执行状态接口
export interface BatchExecutionStatus {
  batchIndex: number;
  batchSize: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  successCount: number;
  failedCount: number;
  results: Array<{
    apiKey: string;
    status: string;
    message: string;
    amount?: number;
    recordId?: number;
  }>;
}

// 增强的批量点亮结果接口
export interface EnhancedBatchLightResult {
  batchId: string;
  totalAccounts: number;
  firstLightAccounts?: number;
  alreadyLightedAccounts?: number;
  batches: BatchExecutionStatus[];
  overallStatus: 'pending' | 'processing' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  summary: {
    totalProcessed: number;
    totalSuccess: number;
    totalFailed: number;
    averageAmountPerAccount?: number;
    totalAmount?: number;
  };
}

// 批量点亮执行历史DTO接口
export interface BatchLightExecutionHistoryDto {
  id: string;
  batchId: string;
  timestamp: string;
  accountApiKey: string; // 脱敏后的API Key
  accountEmail: string;
  assetId: number;
  assetSymbol: string;
  assetName: string;
  amount: number;
  status: 'success' | 'failed' | 'error';
  message: string;
  duration: number;
  lightNumBefore: number;
  lightNumAfter?: number;
  batchIndex: number;
}



// API响应接口
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 增强的批量点亮服务
 */
export class EnhancedBatchLightService extends BaseApiService {
  private static instance: EnhancedBatchLightService;

  constructor() {
    super('EnhancedBatchLightService');
  }

  static getInstance(): EnhancedBatchLightService {
    if (!EnhancedBatchLightService.instance) {
      EnhancedBatchLightService.instance = new EnhancedBatchLightService();
    }
    return EnhancedBatchLightService.instance;
  }
  /**
   * 执行增强的批量点亮（实例方法）
   */
  async executeEnhancedBatchLightInstance(
    request: EnhancedBatchLightRequest
  ): Promise<ApiResponse<EnhancedBatchLightResult>> {
    this.validateRequiredParams(request as unknown as Record<string, unknown>, ['userBeanList', 'assetId']);

    return this.apiCall(
      async () => {
        const response = await batchApi.post<ApiResponse<EnhancedBatchLightResult>>(
          '/batch/enhanced-light/execute',
          request
        );
        return response.data;
      },
      '执行增强批量点亮'
    );
  }

  /**
   * 执行增强的批量点亮（静态方法，保持向后兼容）
   */
  static async executeEnhancedBatchLight(
    request: EnhancedBatchLightRequest
  ): Promise<ApiResponse<EnhancedBatchLightResult>> {
    return EnhancedBatchLightService.getInstance().executeEnhancedBatchLightInstance(request);
  }

  /**
   * 获取批次执行状态
   */
  static async getBatchStatus(
    batchId: string
  ): Promise<ApiResponse<EnhancedBatchLightResult | null>> {
    const response = await api.get<ApiResponse<EnhancedBatchLightResult | null>>(
      `/batch/enhanced-light/status?batchId=${encodeURIComponent(batchId)}`
    );
    return response.data;
  }





  /**
   * 清理已完成的批次
   */
  static async cleanupCompletedBatches(): Promise<ApiResponse<{ message: string }>> {
    const response = await api.post<ApiResponse<{ message: string }>>(
      '/batch/enhanced-light/cleanup-completed'
    );
    return response.data;
  }

  /**
   * 轮询批次状态（增强版本，支持取消）
   */
  static pollBatchStatus(
    batchId: string,
    onUpdate: (result: EnhancedBatchLightResult | null) => void,
    intervalMs: number = 2000,
    maxAttempts: number = 300 // 10分钟
  ): { cancel: () => void } {
    let attempts = 0;
    let timeoutId: NodeJS.Timeout | null = null;
    let cancelled = false;

    const poll = async () => {
      if (cancelled) return;

      try {
        const response = await this.getBatchStatus(batchId);
        if (cancelled) return; // 检查是否已取消

        if (response.code === 200 && response.data) {
          onUpdate(response.data);

          // 如果批次已完成或失败，停止轮询
          if (response.data.overallStatus === 'completed' ||
              response.data.overallStatus === 'failed') {
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts && !cancelled) {
          timeoutId = setTimeout(poll, intervalMs);
        }
      } catch (error) {
        if (!cancelled) {
          console.error('轮询批次状态失败:', error);
          attempts++;
          if (attempts < maxAttempts) {
            timeoutId = setTimeout(poll, intervalMs);
          }
        }
      }
    };

    // 立即开始轮询
    poll();

    // 返回取消函数
    return {
      cancel: () => {
        cancelled = true;
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      }
    };
  }

  /**
   * 格式化批次状态文本
   */
  static formatBatchStatus(status: string): string {
    const statusMap: Record<string, string> = {
      pending: '等待中',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
    };
    return statusMap[status] || status;
  }

  /**
   * 计算批次进度百分比
   */
  static calculateProgress(result: EnhancedBatchLightResult): number {
    if (result.batches.length === 0) return 0;
    
    const completedBatches = result.batches.filter(
      batch => batch.status === 'completed' || batch.status === 'failed'
    ).length;
    
    return Math.round((completedBatches / result.batches.length) * 100);
  }

  /**
   * 格式化执行时间
   * @deprecated 请使用 TimeUtils.formatExecutionTime() 替代
   */
  static formatExecutionTime(startTime: string, endTime?: string): string {
    // 重定向到统一的工具函数
    return TimeUtils.formatExecutionTime(startTime, endTime);
  }

  /**
   * 验证批量点亮请求
   */
  static validateRequest(request: EnhancedBatchLightRequest): string[] {
    const errors: string[] = [];

    if (!request.userBeanList || request.userBeanList.length === 0) {
      errors.push('用户列表不能为空');
    }

    if (!request.assetId || request.assetId <= 0) {
      errors.push('资产ID必须是正数');
    }

    const hasFixedAmount = request.amount !== undefined;
    const hasRandomAmount = request.randomAmount?.enabled === true;

    if (!hasFixedAmount && !hasRandomAmount) {
      errors.push('必须指定固定数量或启用随机数量');
    }

    if (hasFixedAmount && hasRandomAmount) {
      errors.push('不能同时使用固定数量和随机数量');
    }

    if (hasRandomAmount && request.randomAmount) {
      if (request.randomAmount.minAmount >= request.randomAmount.maxAmount) {
        errors.push('随机数量的最小值必须小于最大值');
      }
    }

    return errors;
  }



  /**
   * 获取所有活跃批次（实例方法）
   */
  async getAllActiveBatchesInstance(): Promise<EnhancedBatchLightResult[]> {
    const result = await this.safeExecute(
      async () => {
        const response = await api.get<ApiResponse<EnhancedBatchLightResult[]>>('/batch/enhanced-light/active-batches');
        return this.handleApiResponse(response.data, '获取活跃批次');
      },
      '获取活跃批次',
      [] // 默认返回空数组
    );
    return result || [];
  }

  /**
   * 获取批量点亮执行历史
   * 参考随机点亮的优秀实现
   */
  async getBatchExecutionHistory(limit?: number): Promise<BatchLightExecutionHistoryDto[]> {
    const result = await this.safeExecute(
      async () => {
        const params = limit ? { limit: limit.toString() } : {};
        const response = await api.get<ApiResponse<BatchLightExecutionHistoryDto[]>>(
          '/batch/enhanced-light/execution-history',
          { params }
        );
        return this.handleApiResponse(response.data, '获取批量点亮执行历史');
      },
      '获取批量点亮执行历史',
      [] // 默认返回空数组
    );
    return result || [];
  }

  /**
   * 获取指定批次的执行历史
   */
  async getBatchExecutionHistoryByBatchId(batchId: string): Promise<BatchLightExecutionHistoryDto[]> {
    const result = await this.safeExecute(
      async () => {
        const response = await api.get<ApiResponse<BatchLightExecutionHistoryDto[]>>(
          `/batch/enhanced-light/execution-history/${batchId}`
        );
        return this.handleApiResponse(response.data, '获取指定批次执行历史');
      },
      '获取指定批次执行历史',
      [] // 默认返回空数组
    );
    return result || [];
  }

  /**
   * 获取所有活跃批次（静态方法，保持向后兼容）
   */
  static async getAllActiveBatches(): Promise<EnhancedBatchLightResult[]> {
    return EnhancedBatchLightService.getInstance().getAllActiveBatchesInstance();
  }

  /**
   * 获取批量点亮执行历史（静态方法）
   */
  static async getBatchExecutionHistory(limit?: number): Promise<BatchLightExecutionHistoryDto[]> {
    return EnhancedBatchLightService.getInstance().getBatchExecutionHistory(limit);
  }

  /**
   * 获取指定批次的执行历史（静态方法）
   */
  static async getBatchExecutionHistoryByBatchId(batchId: string): Promise<BatchLightExecutionHistoryDto[]> {
    return EnhancedBatchLightService.getInstance().getBatchExecutionHistoryByBatchId(batchId);
  }
}
