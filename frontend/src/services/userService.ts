import api, { batchApi } from './api';

// 用户统计响应接口
export interface UserCountResponse {
  success: boolean;
  count: number;
}

// 用户信息接口
export interface UserInfo {
  id: number;
  email: string;
  apiKey: string;
  secret: string;
  createdAt: string;
}

// 带余额信息的用户接口
export interface UserWithBalance {
  email: string;
  password: string;
  apiKey: string;
  secret: string;
  balance?: number;
  hasBalance?: boolean;
}

// 账户统计信息接口
export interface AccountStatistics {
  totalAccounts: number;
  accountsWithBalance: number;
  accountsWithoutBalance: number;
  totalNineBalance: number;
  averageBalance: number;
  executionTimeMs: number;
}

// 用户列表响应接口
export interface UserListResponse {
  success: boolean;
  data: UserInfo[];
  count: number;
  message?: string;
}

// 带余额信息的用户列表响应接口
export interface UserWithBalanceListResponse {
  success: boolean;
  data: UserWithBalance[];
  count: number;
  message?: string;
}

// 账户统计响应接口
export interface AccountStatisticsResponse {
  success: boolean;
  data: AccountStatistics;
  message?: string;
}

// 单个账户余额响应接口
export interface SingleAccountBalanceResponse {
  code: number;
  msg: string;
  data: {
    userId: number;
    apiKey: string;
    email: string;
    totalBalance: number;
    hasBalance: boolean;
  } | null;
}

// 账户初始化响应接口
export interface InitAccountsResponse {
  success: boolean;
  message: string;
  count?: number;
}

export class UserService {
  // 获取用户统计
  static async getUserCount(): Promise<UserCountResponse> {
    const response = await api.get<UserCountResponse>('/users/count');
    return response.data;
  }

  // 获取用户列表
  static async getUserList(): Promise<UserListResponse> {
    const response = await api.get<UserListResponse>('/users');
    return response.data;
  }

  // 初始化账户数据
  static async initAccounts(): Promise<InitAccountsResponse> {
    const response = await api.post<InitAccountsResponse>('/users/init-accounts');
    return response.data;
  }

  // 获取带余额信息的账户列表（使用长超时）
  static async getAccountsWithBalance(tokenType: 'NINE' | 'USDT' = 'NINE'): Promise<UserWithBalanceListResponse> {
    const endpoint = tokenType === 'NINE'
      ? '/users/accounts-with-balance'
      : '/users/accounts-with-usdt-balance';
    const response = await batchApi.get<UserWithBalanceListResponse>(endpoint);
    return response.data;
  }

  // 获取带USDT余额信息的账户列表（专门用于内盘交易）
  static async getAccountsWithUsdtBalance(): Promise<UserWithBalanceListResponse> {
    return this.getAccountsWithBalance('USDT');
  }

  // 获取账户统计信息（使用长超时）
  static async getAccountStatistics(): Promise<AccountStatisticsResponse> {
    const response = await batchApi.get<AccountStatisticsResponse>('/users/statistics');
    return response.data;
  }

  // 从文件获取账户数据
  static async getAccountsFromFile(): Promise<UserWithBalanceListResponse> {
    const response = await api.get<UserWithBalanceListResponse>('/users/accounts-from-file');
    return response.data;
  }

  // 快速查询单个账户余额
  static async getSingleAccountBalance(apiKey: string): Promise<SingleAccountBalanceResponse> {
    const response = await api.get<SingleAccountBalanceResponse>(`/batch/balance/${apiKey}`);
    return response.data;
  }
}
