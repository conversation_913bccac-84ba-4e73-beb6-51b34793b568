import { UserService, type UserWithBalance } from './userService';

// 支持的资产类型
export type AssetType = 'NINE' | 'USDT';

// 余额缓存项接口
interface BalanceCacheItem {
  apiKey: string;
  balance: number;
  lastUpdated: number;
  isEstimated: boolean; // 是否为估算值
}

// 点亮操作记录接口
interface LightOperation {
  apiKey: string;
  amount: number;
  timestamp: number;
  batchId: string;
}

/**
 * 余额缓存管理服务
 * 实现本地缓存计算机制和混合更新策略，平衡实时性和性能
 */
export class BalanceCacheService {
  private static instance: BalanceCacheService;
  private balanceCache = new Map<string, BalanceCacheItem>();
  private lightOperations = new Map<string, LightOperation[]>(); // 按apiKey存储点亮操作
  private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存过期
  private readonly SYNC_INTERVAL = 30 * 1000; // 30秒同步间隔
  private syncTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.startPeriodicSync();
  }

  static getInstance(): BalanceCacheService {
    if (!BalanceCacheService.instance) {
      BalanceCacheService.instance = new BalanceCacheService();
    }
    return BalanceCacheService.instance;
  }

  /**
   * 生成缓存key
   */
  private getCacheKey(apiKey: string, assetType: AssetType): string {
    return assetType === 'NINE' ? apiKey : `${apiKey}_${assetType}`;
  }

  /**
   * 获取对应资产类型的API调用方法
   */
  private getApiMethod(assetType: AssetType): () => Promise<{ success: boolean; data: UserWithBalance[] }> {
    return () => UserService.getAccountsWithBalance(assetType);
  }

  /**
   * 通用余额获取方法（优先使用缓存，必要时从服务器获取）
   */
  async getBalanceByAssetType(apiKey: string, assetType: AssetType = 'NINE', forceRefresh = false): Promise<number> {
    const cacheKey = this.getCacheKey(apiKey, assetType);
    const cached = this.balanceCache.get(cacheKey);
    const now = Date.now();

    // 如果有缓存且未过期且不强制刷新，返回缓存值
    if (cached && !forceRefresh && (now - cached.lastUpdated) < this.CACHE_EXPIRY) {
      return cached.balance;
    }

    try {
      // 从服务器获取最新余额
      const apiMethod = this.getApiMethod(assetType);
      const response = await apiMethod();
      if (response.success) {
        const account = response.data.find((acc: UserWithBalance) => acc.apiKey === apiKey);
        if (account) {
          this.updateCache(cacheKey, account.balance || 0, false);
          return account.balance || 0;
        }
      }
    } catch (error) {
      console.error(`获取${assetType}余额失败:`, error);
      // 如果有缓存，返回缓存值
      if (cached) {
        return cached.balance;
      }
    }

    return 0;
  }



  /**
   * 通用批量获取账户余额
   */
  async getBatchBalancesByAssetType(apiKeys: string[], assetType: AssetType = 'NINE', forceRefresh = false): Promise<Map<string, number>> {
    const result = new Map<string, number>();
    const needRefresh: string[] = [];
    const now = Date.now();

    // 检查缓存
    for (const apiKey of apiKeys) {
      const cacheKey = this.getCacheKey(apiKey, assetType);
      const cached = this.balanceCache.get(cacheKey);
      if (cached && !forceRefresh && (now - cached.lastUpdated) < this.CACHE_EXPIRY) {
        result.set(apiKey, cached.balance);
      } else {
        needRefresh.push(apiKey);
      }
    }

    // 批量刷新需要更新的余额
    if (needRefresh.length > 0) {
      try {
        const apiMethod = this.getApiMethod(assetType);
        const response = await apiMethod();
        if (response.success) {
          for (const account of response.data) {
            if (needRefresh.includes(account.apiKey)) {
              const cacheKey = this.getCacheKey(account.apiKey, assetType);
              this.updateCache(cacheKey, account.balance || 0, false);
              result.set(account.apiKey, account.balance || 0);
            }
          }
        }
      } catch (error) {
        console.error(`批量获取${assetType}余额失败:`, error);
        // 使用缓存值填充失败的请求
        for (const apiKey of needRefresh) {
          const cacheKey = this.getCacheKey(apiKey, assetType);
          const cached = this.balanceCache.get(cacheKey);
          if (cached) {
            result.set(apiKey, cached.balance);
          } else {
            result.set(apiKey, 0);
          }
        }
      }
    }

    return result;
  }



  /**
   * 记录点亮操作（用于本地余额估算）
   */
  recordLightOperation(apiKey: string, amount: number, batchId: string): void {
    const operation: LightOperation = {
      apiKey,
      amount,
      timestamp: Date.now(),
      batchId,
    };

    if (!this.lightOperations.has(apiKey)) {
      this.lightOperations.set(apiKey, []);
    }
    this.lightOperations.get(apiKey)!.push(operation);

    // 更新本地估算余额
    this.updateEstimatedBalance(apiKey, amount);

    // 清理过期操作记录（保留最近1小时）
    this.cleanupOldOperations(apiKey);
  }

  /**
   * 更新估算余额
   */
  private updateEstimatedBalance(apiKey: string, lightAmount: number): void {
    const cached = this.balanceCache.get(apiKey);
    if (cached) {
      const newBalance = Math.max(0, cached.balance - lightAmount);
      this.updateCache(apiKey, newBalance, true);
    }
  }

  /**
   * 更新缓存
   */
  private updateCache(apiKey: string, balance: number, isEstimated: boolean): void {
    this.balanceCache.set(apiKey, {
      apiKey,
      balance,
      lastUpdated: Date.now(),
      isEstimated,
    });
  }

  /**
   * 清理过期的操作记录
   */
  private cleanupOldOperations(apiKey: string): void {
    const operations = this.lightOperations.get(apiKey);
    if (operations) {
      const oneHourAgo = Date.now() - 60 * 60 * 1000;
      const filtered = operations.filter(op => op.timestamp > oneHourAgo);
      this.lightOperations.set(apiKey, filtered);
    }
  }

  /**
   * 强制同步指定账户的余额
   */
  async syncBalance(apiKey: string, assetType: AssetType = 'NINE'): Promise<number> {
    return this.getBalanceByAssetType(apiKey, assetType, true);
  }

  /**
   * 批量同步余额
   */
  async syncBatchBalances(apiKeys: string[], assetType: AssetType = 'NINE'): Promise<Map<string, number>> {
    return this.getBatchBalancesByAssetType(apiKeys, assetType, true);
  }

  /**
   * 获取缓存状态信息
   */
  getCacheStats(): {
    totalCached: number;
    estimatedCount: number;
    realCount: number;
    oldestCache: number;
  } {
    const now = Date.now();
    let estimatedCount = 0;
    let realCount = 0;
    let oldestCache = now;

    for (const item of this.balanceCache.values()) {
      if (item.isEstimated) {
        estimatedCount++;
      } else {
        realCount++;
      }
      oldestCache = Math.min(oldestCache, item.lastUpdated);
    }

    return {
      totalCached: this.balanceCache.size,
      estimatedCount,
      realCount,
      oldestCache: now - oldestCache,
    };
  }

  /**
   * 清理所有缓存
   */
  clearCache(): void {
    this.balanceCache.clear();
    this.lightOperations.clear();
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      await this.performPeriodicSync();
    }, this.SYNC_INTERVAL);
  }

  /**
   * 执行定期同步
   */
  private async performPeriodicSync(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // 找出过期的缓存项
    for (const [apiKey, item] of this.balanceCache.entries()) {
      if (now - item.lastUpdated > this.CACHE_EXPIRY) {
        expiredKeys.push(apiKey);
      }
    }

    // 批量更新过期的缓存
    if (expiredKeys.length > 0) {
      try {
        // 分离不同资产类型的key
        const nineKeys: string[] = [];
        const usdtKeys: string[] = [];

        for (const key of expiredKeys) {
          if (key.includes('_USDT')) {
            usdtKeys.push(key.replace('_USDT', ''));
          } else {
            nineKeys.push(key);
          }
        }

        // 分别同步不同资产类型
        if (nineKeys.length > 0) {
          await this.getBatchBalancesByAssetType(nineKeys, 'NINE', true);
        }
        if (usdtKeys.length > 0) {
          await this.getBatchBalancesByAssetType(usdtKeys, 'USDT', true);
        }

        console.log(`定期同步完成，更新了 ${expiredKeys.length} 个账户的余额`);
      } catch (error) {
        console.error('定期同步失败:', error);
      }
    }
  }

  /**
   * 停止定期同步
   */
  stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * 清除单个账户的缓存
   */
  clearAccountCache(apiKey: string): void {
    this.balanceCache.delete(apiKey);
    this.lightOperations.delete(apiKey);
    console.log(`已清除账户 ${apiKey.substring(0, 8)}... 的缓存`);
  }

  /**
   * 批量清除账户缓存
   */
  clearAccountsCache(apiKeys: string[]): void {
    apiKeys.forEach(apiKey => this.clearAccountCache(apiKey));
    console.log(`已清除 ${apiKeys.length} 个账户的缓存`);
  }

  /**
   * 获取账户的点亮操作历史
   */
  getLightOperations(apiKey: string): LightOperation[] {
    return this.lightOperations.get(apiKey) || [];
  }

  /**
   * 估算批量点亮后的余额变化
   */
  estimateBatchLightImpact(operations: Array<{ apiKey: string; amount: number }>): Map<string, {
    currentBalance: number;
    estimatedBalance: number;
    isEstimated: boolean;
  }> {
    const result = new Map();

    for (const { apiKey, amount } of operations) {
      const cached = this.balanceCache.get(apiKey);
      const currentBalance = cached?.balance || 0;
      const estimatedBalance = Math.max(0, currentBalance - amount);
      const isEstimated = cached?.isEstimated || false;

      result.set(apiKey, {
        currentBalance,
        estimatedBalance,
        isEstimated,
      });
    }

    return result;
  }
}
