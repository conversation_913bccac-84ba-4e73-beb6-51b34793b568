import api from './api';
import type { LoginRequest, LoginResponse, User, VerifyTokenResponse } from '../types/auth';
import Cookies from 'js-cookie';

export class AuthService {
  // 登录
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  }

  // 验证令牌
  static async verifyToken(): Promise<VerifyTokenResponse> {
    const response = await api.post<VerifyTokenResponse>('/auth/verify');
    return response.data;
  }

  // 获取用户资料
  static async getProfile(): Promise<{ success: boolean; user: User }> {
    const response = await api.get<{ success: boolean; user: User }>('/auth/profile');
    return response.data;
  }

  // 保存token
  static saveToken(token: string): void {
    Cookies.set('auth_token', token, { expires: 1 }); // 1天过期
  }

  // 获取token
  static getToken(): string | undefined {
    return Cookies.get('auth_token');
  }

  // 清除token
  static clearToken(): void {
    Cookies.remove('auth_token');
  }

  // 解析JWT token获取用户信息
  static parseJWT(token: string): User | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        username: payload.username,
        sub: payload.sub,
      };
    } catch (error) {
      console.error('Invalid token:', error);
      return null;
    }
  }

  // 检查token是否有效
  static isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }

  // 获取当前用户信息
  static getCurrentUser(): User | null {
    const token = this.getToken();
    if (!token || !this.isTokenValid(token)) {
      return null;
    }
    return this.parseJWT(token);
  }
} 