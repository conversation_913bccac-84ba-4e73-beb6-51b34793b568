import api, { batchApi } from './api';

// Symbol转换选项接口
export interface SymbolConversionOption {
  assetId: number;
  tokenName: string;
  tokenSymbol: string;
  lightNum: number;
  postId: number;
  status: string;
  canLight: boolean;
  isRecommended?: boolean;
}

// Symbol转换结果接口
export interface SymbolConversionResult {
  symbol: string;
  found: boolean;
  totalOptions: number;
  lightableOptions: number;
  recommendedOption: SymbolConversionOption | null;
  allOptions: SymbolConversionOption[];
}

// API响应接口
export interface SymbolConversionResponse {
  code: number;
  msg: string;
  data: SymbolConversionResult;
}

// Asset点亮状态接口
export interface AssetLightStatus {
  assetId: number;
  symbol: string;
  tokenName: string;
  lightNum: number;
  canLight: boolean;
  postId: number;
  status: string;
}

// Asset点亮状态响应接口
export interface AssetLightStatusResponse {
  code: number;
  msg: string;
  data: AssetLightStatus;
}

// 批量转换响应接口
export interface BatchSymbolConversionResponse {
  code: number;
  msg: string;
  data: Record<string, SymbolConversionResult>;
}

// 可点亮代币列表响应接口
export interface LightableTokensResponse {
  code: number;
  msg: string;
  data: SymbolConversionOption[];
}

export class SymbolService {
  // 将Symbol转换为AssetId选项
  static async convertSymbolToAssetIds(symbol: string): Promise<SymbolConversionResponse> {
    const response = await api.get<SymbolConversionResponse>(
      `/batch/light-optimizer/resolve-symbol?symbol=${encodeURIComponent(symbol)}`
    );
    return response.data;
  }

  // 批量转换多个Symbol（使用长超时）
  static async batchConvertSymbols(symbols: string[]): Promise<BatchSymbolConversionResponse> {
    const response = await batchApi.post<BatchSymbolConversionResponse>(
      '/batch/light-optimizer/batch-resolve-symbols',
      { symbols }
    );
    return response.data;
  }

  // 获取可点亮的代币列表（使用长超时）
  static async getLightableTokens(limit: number = 50): Promise<LightableTokensResponse> {
    const response = await batchApi.get<LightableTokensResponse>(
      `/batch/light-optimizer/lightable-tokens?limit=${limit}`
    );
    return response.data;
  }

  // 获取Symbol的最佳点亮选择
  static async getBestLightOption(symbol: string): Promise<SymbolConversionResponse> {
    const response = await api.get<SymbolConversionResponse>(
      `/batch/light-optimizer/best-light-option?symbol=${encodeURIComponent(symbol)}`
    );
    return response.data;
  }

  // 验证AssetId是否可点亮
  static async validateAssetId(assetId: number): Promise<SymbolConversionResponse> {
    const response = await api.get<SymbolConversionResponse>(
      `/batch/light-optimizer/validate-lightability?assetId=${assetId}`
    );
    return response.data;
  }

  // 获取特定Asset ID的当前点亮状态
  static async getAssetLightStatus(assetId: number): Promise<AssetLightStatusResponse> {
    const response = await api.get<AssetLightStatusResponse>(
      `/batch/light-optimizer/validate-lightability?assetId=${assetId}`
    );
    return response.data;
  }
}
