import api from './api';
import { EventSourceUtils } from '../utils/eventSourceUtils';
import type {
  StartRandomLightRequest,
  RandomLightStatus,
  RandomLightStatistics,
  RandomLightExecution,
  RandomLightEvent,
  ApiResponse,
} from '../types/randomLight';

/**
 * 随机点亮服务
 */
export class RandomLightService {
  /**
   * 启动随机点亮
   */
  static async startRandomLight(
    request: StartRandomLightRequest,
  ): Promise<ApiResponse> {
    const response = await api.post<ApiResponse>('/batch/random-light/start', {
      intervalMs: request.intervalMs || 3000,
      amount: request.amount || 1,
    });
    return response.data;
  }

  /**
   * 停止随机点亮
   */
  static async stopRandomLight(): Promise<ApiResponse> {
    const response = await api.post<ApiResponse>('/batch/random-light/stop');
    return response.data;
  }

  /**
   * 获取随机点亮状态
   */
  static async getStatus(): Promise<ApiResponse<RandomLightStatus>> {
    const response = await api.get<ApiResponse<RandomLightStatus>>(
      '/batch/random-light/status',
    );
    return response.data;
  }

  /**
   * 获取统计信息
   */
  static async getStatistics(): Promise<ApiResponse<RandomLightStatistics>> {
    const response = await api.get<ApiResponse<RandomLightStatistics>>(
      '/batch/random-light/statistics',
    );
    return response.data;
  }

  /**
   * 获取执行历史
   */
  static async getHistory(): Promise<ApiResponse<RandomLightExecution[]>> {
    const response = await api.get<ApiResponse<RandomLightExecution[]>>(
      '/batch/random-light/history',
    );
    return response.data;
  }

  /**
   * 获取健康状态
   */
  static async getHealth(): Promise<ApiResponse> {
    const response = await api.get<ApiResponse>('/batch/random-light/health');
    return response.data;
  }

  /**
   * 创建事件源连接
   */
  static createEventSource(): EventSource {
    return EventSourceUtils.createEventSource({
      endpoint: '/batch/random-light/events',
      requireAuth: true,
    });
  }

  /**
   * 解析服务器发送的事件
   */
  static parseEvent(event: MessageEvent): RandomLightEvent | null {
    try {
      console.log('收到原始事件:', { type: event.type, data: event.data });
      const data = JSON.parse(event.data);
      console.log('解析后的数据:', data);
      
      // 从数据中提取事件类型
      let eventType: RandomLightEvent['type'];
      
      if (data.type) {
        // 对于 connected 事件，type 在 data 中
        eventType = data.type;
        console.log('从 data.type 提取事件类型:', eventType);
      } else if (data.eventType) {
        // 对于 execution 事件，eventType 在 data 中
        eventType = data.eventType;
        console.log('从 data.eventType 提取事件类型:', eventType);
      } else {
        // 对于 status 事件，根据数据结构判断
        if (data.isRunning !== undefined) {
          eventType = 'status';
          console.log('根据 isRunning 字段判断为 status 事件');
        } else {
          eventType = 'status'; // 默认为 status
          console.log('默认为 status 事件');
        }
      }
      
      const parsedEvent = {
        type: eventType,
        timestamp: data.timestamp || new Date().toISOString(),
        data: data.type ? data : data, // 如果有 type 字段，保持原数据；否则使用整个数据
      };
      
      console.log('最终解析的事件:', parsedEvent);
      return parsedEvent;
    } catch (error) {
      console.error('Failed to parse event:', error);
      return null;
    }
  }

  /**
   * 格式化成功率
   * @deprecated 请直接使用 rate.toFixed(1) + '%' 或 NumberUtils 中的方法
   */
  static formatSuccessRate(rate: number): string {
    return `${rate.toFixed(1)}%`;
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: string): string {
    switch (status) {
      case 'success':
        return '#52c41a';
      case 'failed':
        return '#ff4d4f';
      case 'error':
        return '#faad14';
      default:
        return '#d9d9d9';
    }
  }

  /**
   * 获取状态文本
   */
  static getStatusText(status: string): string {
    switch (status) {
      case 'success':
        return '成功';
      case 'failed':
        return '失败';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  }
}