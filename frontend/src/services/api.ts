import axios from 'axios';
import type { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import Cookies from 'js-cookie';

/**
 * ===========================
 * API 配置和实例创建
 * ===========================
 */

/**
 * 获取 API 基础 URL
 * 优先级：环境变量 > 开发代理 > 生产推断
 */
const getApiBaseUrl = (): string => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // 开发环境使用代理
  if (import.meta.env.DEV) {
    return '/api';
  }

  // 生产环境根据当前域名推断
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = import.meta.env.VITE_API_PORT || '3000';

  return `${protocol}//${hostname}:${port}`;
};

/**
 * 默认 API 实例配置
 */
const createApiInstance = (timeout: number = 10000): AxiosInstance => {
  return axios.create({
    baseURL: getApiBaseUrl(),
    timeout,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// 创建默认API实例（10秒超时）
const api = createApiInstance(10000);

// 创建批量操作API实例（5分钟超时）
const batchApi = createApiInstance(300000);

/**
 * ===========================
 * Token 工具函数
 * ===========================
 */

/**
 * 检查本地 token 是否过期
 */
const isTokenExpiredLocally = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp <= currentTime;
  } catch {
    return true; // token 格式错误视为过期
  }
};

/**
 * 安全地跳转到登录页
 */
const redirectToLogin = (delay: number = 100): void => {
  setTimeout(() => {
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }, delay);
};

/**
 * 清除认证信息并跳转
 */
const clearAuthAndRedirect = (reason: string): void => {
  console.log(`🚫 ${reason}，清除认证信息`);
  Cookies.remove('auth_token');
  redirectToLogin();
};

/**
 * ===========================
 * 拦截器配置
 * ===========================
 */

/**
 * 配置请求拦截器 - 自动添加 Authorization header
 */
const setupRequestInterceptor = (axiosInstance: AxiosInstance): void => {
  axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const token = Cookies.get('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error: AxiosError) => Promise.reject(error)
  );
};

/**
 * 处理不同类型的401错误
 */
const handle401Error = (requestUrl: string, token: string): void => {
  const isLoginEndpoint = requestUrl.includes('/login');
  const isVerifyEndpoint = requestUrl.includes('/verify');
  const isAuthEndpoint = requestUrl.includes('/auth/');

  if (isLoginEndpoint) {
    // 登录接口401是正常的（用户名密码错误）
    console.log('🔑 登录接口返回401，用户名或密码错误');
    return;
  }

  if (isVerifyEndpoint) {
    // verify接口401需要谨慎处理
    console.log('🔍 Token验证接口返回401，检查本地token状态');

    if (isTokenExpiredLocally(token)) {
      clearAuthAndRedirect('本地检查确认token已过期');
    } else {
      console.log('🔄 Token仍有效，可能是临时的服务器问题，不强制登出');
    }
    return;
  }

  if (isAuthEndpoint) {
    // 其他认证接口401，token确实无效
    clearAuthAndRedirect('认证接口返回401，token确实无效');
    return;
  }

  // 非认证接口401，需要仔细判断
  console.warn(`⚠️ 非认证接口返回401: ${requestUrl}`);

  if (isTokenExpiredLocally(token)) {
    clearAuthAndRedirect('本地检查确认token已过期');
  } else {
    console.log('🔄 Token仍有效，可能是临时的服务器问题');
  }
};

/**
 * 配置响应拦截器 - 智能处理401错误
 */
const setupResponseInterceptor = (axiosInstance: AxiosInstance): void => {
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      if (error.response?.status === 401) {
        const currentToken = Cookies.get('auth_token');
        const requestUrl = error.config?.url || '';

        console.log(`🔒 收到401响应: ${requestUrl}`);

        if (currentToken) {
          handle401Error(requestUrl, currentToken);
        } else {
          console.log('📭 没有token，401响应是正常的');
        }
      }
      return Promise.reject(error);
    }
  );
};

/**
 * 为axios实例配置所有拦截器
 */
const setupInterceptors = (axiosInstance: AxiosInstance): void => {
  setupRequestInterceptor(axiosInstance);
  setupResponseInterceptor(axiosInstance);
};

/**
 * ===========================
 * 实例配置和导出
 * ===========================
 */

// 为两个实例配置拦截器
setupInterceptors(api);
setupInterceptors(batchApi);

// 导出API实例
export default api;
export { batchApi };