import api from './api';
import type {
  ApiResponse,
  InnerDiskNewCoinConfig,
  CreateConfigRequest,
  StartMonitoringRequest,
  StopMonitoringRequest,
  MonitoringStatus,
} from '../types/innerDiskNewCoin';

/**
 * 内盘打新服务
 * 
 * 提供内盘打新功能的API调用服务
 * 包括配置管理、监控控制、数据查询和实时事件推送
 */
export class InnerDiskNewCoinService {
  // ========================================
  // 配置管理
  // ========================================

  /**
   * 获取用户配置
   */
  static async getConfig(): Promise<ApiResponse<InnerDiskNewCoinConfig | null>> {
    const response = await api.get<ApiResponse<InnerDiskNewCoinConfig | null>>(
      '/inner-disk-new-coin/config'
    );
    return response.data;
  }

  /**
   * 创建或更新配置
   */
  static async createOrUpdateConfig(
    config: CreateConfigRequest
  ): Promise<ApiResponse<InnerDiskNewCoinConfig>> {
    const response = await api.post<ApiResponse<InnerDiskNewCoinConfig>>(
      '/inner-disk-new-coin/config',
      config
    );
    return response.data;
  }

  // ========================================
  // 监控控制
  // ========================================

  /**
   * 启动监控
   */
  static async startMonitoring(
    request: StartMonitoringRequest = {}
  ): Promise<ApiResponse<{ sessionId: string }>> {
    const response = await api.post<ApiResponse<{ sessionId: string }>>(
      '/inner-disk-new-coin/start',
      request
    );
    return response.data;
  }

  /**
   * 停止监控
   */
  static async stopMonitoring(
    request: StopMonitoringRequest = {}
  ): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>(
      '/inner-disk-new-coin/stop',
      request
    );
    return response.data;
  }

  /**
   * 获取监控状态
   */
  static async getStatus(): Promise<ApiResponse<MonitoringStatus>> {
    const response = await api.get<ApiResponse<MonitoringStatus>>(
      '/inner-disk-new-coin/status'
    );
    return response.data;
  }

  // ========================================
  // 数据查询 - 简化版本
  // ========================================



}
