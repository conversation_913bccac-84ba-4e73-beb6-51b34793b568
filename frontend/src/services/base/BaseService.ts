import { ErrorUtils } from '../../utils/commonUtils';

/**
 * 基础服务类
 * 提供统一的错误处理、日志记录和通用方法
 */
export abstract class BaseService {
  protected serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * 统一的错误处理方法
   * @param error 错误对象
   * @param operation 操作名称
   * @param defaultMessage 默认错误消息
   */
  protected handleError(
    error: unknown,
    operation: string,
    defaultMessage = '操作失败'
  ): never {
    const errorMessage = ErrorUtils.formatErrorMessage(error, defaultMessage);
    console.error(`[${this.serviceName}] ${operation} 失败:`, error);
    throw new Error(errorMessage);
  }

  /**
   * 记录操作日志（仅在开发环境）
   * @param operation 操作名称
   * @param details 详细信息
   */
  protected log(operation: string, details?: unknown): void {
    if (import.meta.env.DEV) {
      console.log(`[${this.serviceName}] ${operation}`, details || '');
    }
  }

  /**
   * 记录警告日志
   * @param operation 操作名称
   * @param details 详细信息
   */
  protected warn(operation: string, details?: unknown): void {
    console.warn(`[${this.serviceName}] ${operation}`, details || '');
  }

  /**
   * 验证必填参数
   * @param params 参数对象
   * @param requiredFields 必填字段列表
   */
  protected validateRequiredParams(
    params: Record<string, unknown>,
    requiredFields: string[]
  ): void {
    const missingFields = requiredFields.filter(
      field => params[field] === undefined || params[field] === null
    );

    if (missingFields.length > 0) {
      throw new Error(`缺少必填参数: ${missingFields.join(', ')}`);
    }
  }

  /**
   * 安全的异步操作执行
   * @param operation 操作函数
   * @param operationName 操作名称
   * @param defaultValue 默认返回值
   */
  protected async safeExecute<T>(
    operation: () => Promise<T>,
    operationName: string,
    defaultValue?: T
  ): Promise<T | undefined> {
    try {
      const result = await operation();
      return result;
    } catch (error) {
      this.warn(`${operationName} 执行失败`, error);
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw error;
    }
  }



  /**
   * 批量处理数据
   * @param items 数据项列表
   * @param processor 处理函数
   * @param batchSize 批次大小
   * @param delay 批次间延迟
   */
  protected async batchProcess<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    batchSize = 10,
    delay = 100
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map((item, index) => 
        processor(item, i + index)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          this.warn(`批次处理失败 (索引 ${i + index})`, result.reason);
        }
      });

      // 批次间延迟
      if (i + batchSize < items.length && delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return results;
  }
}

/**
 * API服务基类
 * 专门用于API调用的服务
 */
export abstract class BaseApiService extends BaseService {
  /**
   * 统一的API响应处理
   * @param response API响应
   * @param operation 操作名称
   */
  protected handleApiResponse<T>(
    response: { code: number; msg: string; data: T },
    operation: string
  ): T {
    if (response.code === 200) {
      return response.data;
    } else {
      this.handleError(
        new Error(response.msg),
        operation,
        `${operation} 失败`
      );
    }
  }

  /**
   * 检查网络错误
   * @param error 错误对象
   */
  protected isNetworkError(error: unknown): boolean {
    return ErrorUtils.isNetworkError(error);
  }

  /**
   * 直接API调用（无重试）
   * @param apiCall API调用函数
   * @param operation 操作名称
   */
  protected async apiCall<T>(
    apiCall: () => Promise<T>,
    operation: string
  ): Promise<T> {
    try {
      return await apiCall();
    } catch (error) {
      this.handleError(error, operation);
    }
  }
}

export default BaseService;
