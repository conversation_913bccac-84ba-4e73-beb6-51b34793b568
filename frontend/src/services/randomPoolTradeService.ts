import api from './api';
// NumberUtils 和 TimeUtils 已移至页面组件中直接使用
import { EventSourceUtils } from '../utils/eventSourceUtils';
import { ValidationUtils } from '../utils/validationUtils';
import { TradeStatusUtils, type PoolTradeStatusType } from '../utils/tradeStatusUtils';
import type {
  StartRandomPoolTradeRequest,
  RandomPoolTradeStatusResponse,
  TradeHistoryResponse,
  SelectablePoolsResponse,
  ApiResponse,
} from '../types/randomPoolTrade';

/**
 * 随机池子刷量服务
 * 
 * 提供随机池子刷量功能的API调用方法
 */
export class RandomPoolTradeService {
  /**
   * 启动随机池子刷量
   */
  static async startRandomPoolTrade(
    request: StartRandomPoolTradeRequest,
  ): Promise<ApiResponse<{ sessionId: string }>> {
    const response = await api.post<ApiResponse<{ sessionId: string }>>(
      '/random-pool-trade/start',
      {
        minBuyAmount: request.minBuyAmount,
        maxBuyAmount: request.maxBuyAmount,
        minBuyIntervalMs: request.minBuyIntervalMs,
        maxBuyIntervalMs: request.maxBuyIntervalMs,
        minSellDelayMs: request.minSellDelayMs,
        maxSellDelayMs: request.maxSellDelayMs,
        buyCount: request.buyCount,
        poolCooldownMs: request.poolCooldownMs || 300000,
      }
    );
    return response.data;
  }

  /**
   * 停止随机池子刷量
   */
  static async stopRandomPoolTrade(): Promise<ApiResponse> {
    const response = await api.post<ApiResponse>('/random-pool-trade/stop');
    return response.data;
  }

  /**
   * 获取随机池子刷量状态
   */
  static async getStatus(): Promise<ApiResponse<RandomPoolTradeStatusResponse | null>> {
    const response = await api.get<ApiResponse<RandomPoolTradeStatusResponse | null>>(
      '/random-pool-trade/status'
    );
    return response.data;
  }

  /**
   * 获取交易历史记录
   */
  static async getTradeHistory(params: {
    sessionId?: string;
    page?: number;
    pageSize?: number;
    startTime?: number;
    endTime?: number;
  } = {}): Promise<ApiResponse<TradeHistoryResponse>> {
    const queryParams = new URLSearchParams();
    
    if (params.sessionId) queryParams.append('sessionId', params.sessionId);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.startTime) queryParams.append('startTime', params.startTime.toString());
    if (params.endTime) queryParams.append('endTime', params.endTime.toString());

    const response = await api.get<ApiResponse<TradeHistoryResponse>>(
      `/random-pool-trade/history?${queryParams.toString()}`
    );
    return response.data;
  }

  /**
   * 获取可选择的池子列表
   */
  static async getSelectablePools(): Promise<ApiResponse<SelectablePoolsResponse>> {
    const response = await api.get<ApiResponse<SelectablePoolsResponse>>(
      '/random-pool-trade/pools'
    );
    return response.data;
  }

  /**
   * 创建事件源连接
   */
  static createEventSource(): EventSource {
    return EventSourceUtils.createEventSource({
      endpoint: '/random-pool-trade/events',
      requireAuth: true,
    });
  }

  /**
   * 格式化时间显示
   */
  static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  // 已移除已弃用的格式化方法，请使用 NumberUtils 和 TimeUtils 中的对应方法

  /**
   * 获取交易类型的中文显示
   */
  static getTradeTypeText(tradeType: 'BUY' | 'SELL'): string {
    return tradeType === 'BUY' ? '买入' : '卖出';
  }

  /**
   * 获取池子状态的中文显示
   */
  static getPoolStatusText(status: string): string {
    return TradeStatusUtils.getStatusText(status as PoolTradeStatusType);
  }

  /**
   * 获取交易状态的颜色
   */
  static getTradeStatusColor(success: boolean): string {
    return success ? 'success' : 'error';
  }

  /**
   * 获取池子状态的颜色
   */
  static getPoolStatusColor(status: string): string {
    return TradeStatusUtils.getStatusColor(status as PoolTradeStatusType);
  }

  /**
   * 验证配置参数
   */
  static validateConfig(config: StartRandomPoolTradeRequest): string[] {
    const errors: string[] = [];

    // 验证买入金额区间
    errors.push(
      ...ValidationUtils.validateBuyAmountRange(
        config.minBuyAmount,
        config.maxBuyAmount,
      ),
    );

    // 验证买入间隔时间区间
    errors.push(
      ...ValidationUtils.validateBuyIntervalRange(
        config.minBuyIntervalMs / 1000,
        config.maxBuyIntervalMs / 1000,
      ),
    );

    // 验证卖出延迟时间区间
    errors.push(
      ...ValidationUtils.validateSellDelayRange(
        config.minSellDelayMs / 1000,
        config.maxSellDelayMs / 1000,
      ),
    );

    // 验证买入次数
    const buyCountError = ValidationUtils.validateBuyCount(config.buyCount);
    if (buyCountError) {
      errors.push(buyCountError);
    }

    // 验证池子冷却时间
    if (config.poolCooldownMs) {
      const cooldownError = ValidationUtils.validatePoolCooldown(
        config.poolCooldownMs / 60000,
      );
      if (cooldownError) {
        errors.push(cooldownError);
      }
    }

    return errors.filter(Boolean); // 过滤空值
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): StartRandomPoolTradeRequest {
    return {
      minBuyAmount: 1,
      maxBuyAmount: 10,
      minBuyIntervalMs: 1000,
      maxBuyIntervalMs: 5000,
      minSellDelayMs: 5000,
      maxSellDelayMs: 10000,
      buyCount: 2,
      poolCooldownMs: 300000,
    };
  }
}
