/**
 * 通用类型定义
 * 统一管理项目中的公共类型
 */

// ===== 基础类型 =====

/**
 * API响应基础接口
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp?: string;
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

/**
 * 分页响应数据
 */
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 排序参数
 */
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

/**
 * 筛选参数
 */
export interface FilterParams {
  [key: string]: unknown;
}

// ===== 组件通用类型 =====

/**
 * 基础组件Props
 */
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

/**
 * 解析后的账户缓存
 */
export interface ParsedAccountsCache {
  accounts: Array<{ apiKey: string; secret: string }>;
  count: number;
}

/**
 * 加载状态Props
 */
export interface LoadingProps {
  loading?: boolean;
  loadingText?: string;
}

/**
 * 错误状态Props
 */
export interface ErrorProps {
  error?: string | null;
  onRetry?: () => void;
}

/**
 * 空状态Props
 */
export interface EmptyProps {
  empty?: boolean;
  emptyText?: string;
  onRefresh?: () => void;
}

// ===== 表单相关类型 =====

/**
 * 表单字段配置
 */
export interface FormFieldConfig {
  name: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'number' | 'switch' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: unknown }>;
  rules?: unknown[];
}

/**
 * 表单验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: Record<string, string>;
}

// ===== 业务相关类型 =====

/**
 * 用户信息基础接口
 */
export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  roles?: string[];
}

/**
 * 操作日志接口
 */
export interface OperationLog {
  id: string;
  userId: string;
  action: string;
  target: string;
  details?: Record<string, unknown>;
  timestamp: Date;
  ip?: string;
  userAgent?: string;
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadTime: Date;
  uploader?: string;
}

// ===== 状态管理类型 =====

/**
 * 异步状态
 */
export interface AsyncState<T = unknown> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * 列表状态
 */
export interface ListState<T = unknown> extends AsyncState<T[]> {
  pagination: PaginationParams;
  filters: FilterParams;
  sort: SortParams | null;
}

// ===== 工具类型 =====

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 提取Promise返回类型
 */
export type PromiseType<T> = T extends Promise<infer U> ? U : T;

/**
 * 函数参数类型
 */
export type FunctionParams<T> = T extends (...args: infer P) => unknown ? P : never;

/**
 * 函数返回类型
 */
export type FunctionReturn<T> = T extends (...args: unknown[]) => infer R ? R : never;

// ===== 事件类型 =====

/**
 * 自定义事件接口
 */
export interface CustomEvent<T = unknown> {
  type: string;
  payload: T;
  timestamp: Date;
  source?: string;
}

/**
 * 事件处理器类型
 */
export type EventHandler<T = unknown> = (event: CustomEvent<T>) => void;

// ===== 配置类型 =====

/**
 * 应用配置接口
 */
export interface AppConfig {
  apiUrl: string;
  timeout: number;
  retryCount: number;
  enableLogging: boolean;
  enableAnalytics: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
}

/**
 * 功能开关配置
 */
export interface FeatureFlags {
  [key: string]: boolean;
}

export default {};
