/**
 * 随机池子刷量功能前端类型定义
 */

import type { PoolTradeStatusType, OperationTypeType } from '../utils/tradeStatusUtils';

/**
 * 启动随机池子刷量请求
 */
export interface StartRandomPoolTradeRequest {
  /** 最小买入量 */
  minBuyAmount: number;
  /** 最大买入量 */
  maxBuyAmount: number;
  /** 买入间隔时间最小值（毫秒） */
  minBuyIntervalMs: number;
  /** 买入间隔时间最大值（毫秒） */
  maxBuyIntervalMs: number;
  /** 卖出延迟时间最小值（毫秒） */
  minSellDelayMs: number;
  /** 卖出延迟时间最大值（毫秒） */
  maxSellDelayMs: number;
  /** 买入次数 */
  buyCount: number;
  /** 池子冷却时间（毫秒） */
  poolCooldownMs?: number;
}

/**
 * 随机池子刷量配置
 */
export interface RandomPoolTradeConfig {
  minBuyAmount: number;
  maxBuyAmount: number;
  minBuyIntervalMs: number;
  maxBuyIntervalMs: number;
  minSellDelayMs: number;
  maxSellDelayMs: number;
  buyCount: number;
  poolCooldownMs: number;
}

/**
 * 池子累积状态
 */
export interface PoolAccumulationState {
  poolId: number;
  symbol: string;
  accumulatedAmount: number;
  completedBuyCount: number;
  targetBuyCount: number;
  status: PoolTradeStatusType;
  createdAt: number;
  updatedAt: number;
}

/**
 * 随机池子刷量会话信息
 */
export interface RandomPoolTradeSession {
  sessionId: string;
  isRunning: boolean;
  startTime: number;
  endTime?: number;
  runningDuration: number;
  config: RandomPoolTradeConfig;
}

/**
 * 操作统计
 */
export interface OperationStatistics {
  totalBuyOperations: number;
  totalSellOperations: number;
  successfulBuyOperations: number;
  successfulSellOperations: number;
  buySuccessRate: number;
  sellSuccessRate: number;
}

/**
 * 池子统计
 */
export interface PoolStatistics {
  activePoolsCount: number;
  completedPoolsCount: number;
  totalPoolsProcessed: number;
}

/**
 * 交易量统计
 */
export interface VolumeStatistics {
  totalBuyVolume: number;
  totalSellVolume: number;
  totalVolume: number;
  averageBuyAmount: number;
  averageSellAmount: number;
}

/**
 * 随机池子刷量统计信息
 */
export interface RandomPoolTradeStatistics {
  session: RandomPoolTradeSession;
  operations: OperationStatistics;
  pools: PoolStatistics;
  volume: VolumeStatistics;
}

/**
 * 随机池子刷量状态响应
 */
export interface RandomPoolTradeStatusResponse {
  session: RandomPoolTradeSession;
  activePools: PoolAccumulationState[];
  statistics: {
    operations: OperationStatistics;
    pools: PoolStatistics;
    volume: VolumeStatistics;
  };
}

/**
 * 交易操作记录
 */
export interface TradeOperationRecord {
  id: string;
  sessionId: string;
  poolId: number;
  symbol: string;
  tradeType: OperationTypeType;
  amount: number;
  timestamp: number;
  success: boolean;
  error?: string;
  transactionId?: string;
  trader?: {
    email: string;
    apiKey: string;
  };
  accumulationSnapshot?: {
    accumulatedAmount: number;
    completedBuyCount: number;
    targetBuyCount: number;
  };
}

/**
 * 交易历史响应
 */
export interface TradeHistoryResponse {
  records: TradeOperationRecord[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

/**
 * 可选择的池子信息
 */
export interface SelectablePool {
  poolId: number;
  symbol: string;
  stableCoinSymbol: string;
  price: number;
  liquidity: number;
  volume24h: number;
  poolStatus: string;
  isStop: number;
  lastSelectedAt?: number;
}

/**
 * 可选择池子列表响应
 */
export interface SelectablePoolsResponse {
  pools: SelectablePool[];
  total: number;
  filters: {
    activeOnly: boolean;
    minLiquidity: number;
    excludeCooldown: boolean;
  };
}

/**
 * 实时事件类型
 */
export type RandomPoolTradeEventType =
  | 'heartbeat'
  | 'session_started'
  | 'session_stopped'
  | 'pool_selected'
  | 'buy_operation_started'
  | 'buy_operation_completed'
  | 'buy_operation_failed'
  | 'sell_operation_started'
  | 'sell_operation_completed'
  | 'sell_operation_failed'
  | 'pool_completed'
  | 'positions_cleared'
  | 'statistics_updated'
  | 'error_occurred';

/**
 * 实时事件数据
 */
export interface RandomPoolTradeEvent {
  type: RandomPoolTradeEventType;
  timestamp: number;
  sessionId?: string; // 心跳事件可能没有sessionId
  sequence?: number; // 心跳事件的序列号
  data?: {
    pool?: {
      poolId: number;
      symbol: string;
    };
    trade?: {
      type: OperationTypeType;
      amount: number;
      success: boolean;
      error?: string;
    };
    accumulation?: {
      accumulatedAmount: number;
      completedBuyCount: number;
      targetBuyCount: number;
    };
    statistics?: RandomPoolTradeStatistics;
    error?: string;
  };
}

/**
 * API响应基础接口
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: number;
}

/**
 * 连接状态
 */
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';

/**
 * 加载状态
 */
export interface LoadingState {
  start: boolean;
  stop: boolean;
  refresh: boolean;
  history: boolean;
  pools: boolean;
}

/**
 * 表单值接口
 */
export interface RandomPoolTradeFormValues {
  minBuyAmount: number;
  maxBuyAmount: number;
  minBuyIntervalSeconds: number;
  maxBuyIntervalSeconds: number;
  minSellDelaySeconds: number;
  maxSellDelaySeconds: number;
  buyCount: number;
  poolCooldownMinutes: number;
}
