/**
 * 内盘打新功能相关类型定义
 */

// ========================================
// 基础类型
// ========================================

/**
 * 购买类型
 */
export type PurchaseType = 'FIXED' | 'PERCENTAGE';



/**
 * 交易类型
 */
export type NewCoinTradeType = 'BUY' | 'SELL';

/**
 * 触发类型
 */
export type TriggerType = 'PROFIT_TARGET' | 'STOP_LOSS' | 'MANUAL';

/**
 * 会话状态
 */
export type SessionStatus = 'RUNNING' | 'STOPPED' | 'ERROR';

/**
 * 交易状态
 */
export type TradeStatus = 'PENDING' | 'SUCCESS' | 'FAILED';

/**
 * 持仓状态
 */
export type PositionStatus = 'HOLDING' | 'SOLD' | 'EXPIRED';

/**
 * 事件类型
 */
export type NewCoinEventType = 
  | 'status' 
  | 'detection' 
  | 'trade' 
  | 'position_update' 
  | 'error' 
  | 'stopped' 
  | 'connected';

// ========================================
// API 请求/响应类型
// ========================================

/**
 * API 响应基础格式
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  timestamp: string;
}

/**
 * 分页响应格式
 */
export type PaginatedResponse<T = unknown> = ApiResponse<{
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}>;

// ========================================
// 配置相关类型
// ========================================

/**
 * 内盘打新配置
 */
export interface InnerDiskNewCoinConfig {
  id?: number;
  userId: number;
  isEnabled: boolean;
  apiKey?: string;
  secret?: string;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval: number;
  maxPositions: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens?: string[];

  createdAt?: string;
  updatedAt?: string;
}

/**
 * 配置创建/更新请求
 */
export interface CreateConfigRequest {
  isEnabled?: boolean;
  apiKey?: string;
  secret?: string;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval?: number;
  maxPositions?: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens?: string[];

}

/**
 * 启动监控请求
 */
export interface StartMonitoringRequest {
  apiKey?: string;
  secret?: string;
}

/**
 * 停止监控请求
 */
export interface StopMonitoringRequest {
  reason?: string;
}

// ========================================
// 状态相关类型
// ========================================

/**
 * 监控状态
 */
export interface MonitoringStatus {
  isRunning: boolean;
  sessionId?: string;
  startTime?: string;
  uptime: number;
  totalDetected: number;
  totalTrades: number;
  successfulTrades: number;
  currentPositions: number;
  totalProfit: number;
  lastDetection?: string;
  lastTrade?: string;
  errorCount: number;
  lastError?: string;
  config?: InnerDiskNewCoinConfig;
}

/**
 * 统计信息
 */
export interface Statistics {
  totalSessions: number;
  activeSessions: number;
  totalDetected: number;
  totalTrades: number;
  successfulTrades: number;
  totalProfit: number;
  successRate: number;
  averageProfit: number;
  currentSession?: {
    id: string;
    duration: number;
    detected: number;
    trades: number;
    profit: number;
  };
  dailyStats?: Array<{
    date: string;
    detected: number;
    trades: number;
    profit: number;
  }>;
}

// ========================================
// 交易相关类型
// ========================================

/**
 * 交易记录
 */
export interface TradeRecord {
  id: string;
  configId: number;
  sessionId: string;
  detectionId?: number;
  poolId: number;
  tokenSymbol: string;
  tradeType: NewCoinTradeType;
  amount: number;
  price: number;
  totalValue: number;
  status: TradeStatus;
  txHash?: string;
  errorMessage?: string;
  executedAt: string;
  buyPrice?: number;
  buyAmount?: number;
  buyTime?: string;
  profitLoss?: number;
  profitLossRate?: number;
  triggerType?: TriggerType;
  targetPrice?: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 持仓信息
 */
export interface Position {
  id: number;
  configId: number;
  sessionId: string;
  poolId: number;
  tokenSymbol: string;
  buyTradeId: string;
  amount: number;
  buyPrice: number;
  buyValue: number;
  currentPrice?: number;
  currentValue?: number;
  unrealizedPnl?: number;
  unrealizedPnlRate?: number;
  profitTarget: number;
  stopLossTarget?: number;
  status: PositionStatus;
  boughtAt: string;
  soldAt?: string;
  holdingDuration: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 检测记录
 */
export interface DetectionRecord {
  id: number;
  sessionId: string;
  poolId: number;
  tokenSymbol: string;
  tokenName?: string;
  stableCoinSymbol: string;
  initialPrice: number;
  marketCap?: number;
  liquidity?: number;
  volume24h?: number;
  chain: string;
  detectedAt: string;
  isTraded: boolean;
  tradeReason?: string;
  createdAt: string;
}

// ========================================
// 事件相关类型
// ========================================

/**
 * 实时事件
 */
export interface NewCoinEvent {
  type: NewCoinEventType;
  timestamp: string;
  data: unknown;
}

/**
 * 事件数据类型
 */
export interface NewCoinEventData {
  status?: MonitoringStatus;
  detection?: DetectionRecord;
  trade?: TradeRecord;
  position?: Position;
  error?: {
    message: string;
    code?: string;
    details?: unknown;
  };
}

// ========================================
// 查询参数类型
// ========================================

/**
 * 基础查询参数
 */
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sessionId?: string;
  tokenSymbol?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * 交易查询参数
 */
export interface TradeQueryParams extends BaseQueryParams {
  tradeType?: NewCoinTradeType;
  status?: TradeStatus;
  poolId?: number;
}

/**
 * 持仓查询参数
 */
export interface PositionQueryParams extends BaseQueryParams {
  status?: PositionStatus;
  poolId?: number;
}

/**
 * 检测查询参数
 */
export interface DetectionQueryParams extends BaseQueryParams {
  isTraded?: boolean;
  chain?: string;
}

/**
 * 统计查询参数
 */
export interface StatisticsQueryParams {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
}

// ========================================
// 表单相关类型
// ========================================

/**
 * 配置表单数据
 */
export interface ConfigFormData {
  isEnabled: boolean;
  apiKey?: string;
  secret?: string;
  purchaseAmount: number;
  purchaseType: PurchaseType;
  purchasePercentage?: number;
  profitRate: number;
  stopLossRate?: number;
  monitorInterval: number;
  maxPositions: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  excludeTokens: string[];

}

/**
 * 启动监控表单数据
 */
export interface StartMonitoringFormData {
  apiKey?: string;
  secret?: string;
}

// ========================================
// 组件状态类型
// ========================================

/**
 * 加载状态
 */
export interface LoadingState {
  config: boolean;
  status: boolean;
  start: boolean;
  stop: boolean;
  trades: boolean;
  positions: boolean;
  detections: boolean;
  statistics: boolean;
}

/**
 * 连接状态
 */
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting';

/**
 * 页面状态
 */
export interface PageState {
  config: InnerDiskNewCoinConfig | null;
  status: MonitoringStatus | null;
  statistics: Statistics | null;
  trades: TradeRecord[];
  positions: Position[];
  detections: DetectionRecord[];
  loading: LoadingState;
  connectionStatus: ConnectionStatus;
  error: string | null;
}
