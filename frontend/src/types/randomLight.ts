/**
 * 随机点亮相关类型定义
 */

/**
 * 启动随机点亮请求
 */
export interface StartRandomLightRequest {
  intervalMs?: number;
  amount?: number;
}

/**
 * 随机点亮状态
 */
export interface RandomLightStatus {
  isRunning: boolean;
  startTime: string | null;
  intervalMs: number;
  amount: number;
  totalExecutions: number;
  successCount: number;
  failedCount: number;
  lastExecution: string | null;
  currentAccount: string | null;
  currentAssetId: number | null;
  currentAssetSymbol: string | null;
  uptime: number;
}

/**
 * 随机点亮统计信息
 */
export interface RandomLightStatistics {
  totalSessions: number;
  totalExecutions: number;
  totalSuccessCount: number;
  totalFailedCount: number;
  successRate: number;
  averageInterval: number;
  longestSession: number;
  currentSession: {
    isActive: boolean;
    duration: number;
    executions: number;
    successCount: number;
    failedCount: number;
  };
}

/**
 * 随机点亮执行记录
 */
export interface RandomLightExecution {
  id: string;
  timestamp: string;
  accountApiKey: string;
  accountEmail: string;
  assetId: number;
  assetSymbol: string;
  assetName: string;
  amount: number;
  status: 'success' | 'failed' | 'error';
  message: string;
  duration: number;
  lightNumBefore: number;
  lightNumAfter?: number;
}

/**
 * 随机点亮事件
 */
export interface RandomLightEvent {
  type: 'status' | 'execution' | 'error' | 'stopped' | 'connected';
  timestamp: string;
  data: unknown;
}

/**
 * API响应格式
 */
export interface ApiResponse<T = unknown> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 随机点亮配置
 */
export interface RandomLightConfig {
  intervalMs: number;
  amount: number;
  minBalance: number;
  maxLightNum: number;
  retryAttempts: number;
  retryDelay: number;
}