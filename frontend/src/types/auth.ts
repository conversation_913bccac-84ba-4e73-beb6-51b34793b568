export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  data: {
    access_token: string;
  };
  meta?: {
    timestamp: string;
    path: string;
    method: string;
    requestId: string;
    duration: number;
  };
  // 兼容旧格式
  access_token?: string;
}

export interface VerifyTokenResponse {
  success: boolean;
  message: string;
  user: User;
}

export interface User {
  username: string;
  sub: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  verifyToken: () => Promise<boolean>;
  isAuthenticated: boolean;
  loading: boolean;
}