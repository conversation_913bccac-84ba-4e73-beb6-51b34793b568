import React, { useState, useEffect, useCallback } from 'react';
import type { ReactNode } from 'react';
import type { AuthContextType, LoginRequest, User } from '../types/auth';
import { AuthService } from '../services/authService';
import { message } from 'antd';
import { AuthContext } from './authContextCreate';

/**
 * ===========================
 * 类型定义
 * ===========================
 */
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * ===========================
 * AuthProvider 组件
 * ===========================
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // 状态管理
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  /**
   * ===========================
   * 核心认证函数
   * ===========================
   */

  /**
   * 验证当前token的有效性
   */
  const verifyToken = useCallback(async (): Promise<boolean> => {
    const currentToken = AuthService.getToken();
    if (!currentToken || !AuthService.isTokenValid(currentToken)) {
      return false;
    }

    try {
      const verifyResponse = await AuthService.verifyToken();
      if (verifyResponse.success) {
        setUser(verifyResponse.user);
        setToken(currentToken);
        return true;
      }
    } catch (error) {
      console.error('Token verification failed:', error);
    }

    return false;
  }, []);

  /**
   * 检查token剩余时间（分钟）
   */
  const getTokenRemainingTime = useCallback((token: string): number => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return Math.max(0, (payload.exp - currentTime) / 60);
    } catch {
      return 0;
    }
  }, []);

  /**
   * 处理网络验证失败的容错逻辑
   */
  const handleVerificationFailure = useCallback((token: string, userData: User): boolean => {
    const remainingMinutes = getTokenRemainingTime(token);

    if (remainingMinutes > 5) {
      console.log(`🔄 网络问题但token仍有效(剩余${remainingMinutes.toFixed(1)}分钟)，允许继续使用`);
      setUser(userData);
      setToken(token);
      return true;
    } else {
      console.log('⏰ Token即将过期且验证失败，清除存储');
      AuthService.clearToken();
      return false;
    }
  }, [getTokenRemainingTime]);

  /**
   * ===========================
   * 初始化逻辑
   * ===========================
   */

  /**
   * 应用启动时的认证初始化
   */
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('� 开始认证初始化...');
      const storedToken = AuthService.getToken();

      if (!storedToken) {
        console.log('📭 没有找到本地token');
        setLoading(false);
        return;
      }

      if (!AuthService.isTokenValid(storedToken)) {
        console.log('⏰ Token已过期，清除本地存储');
        AuthService.clearToken();
        setLoading(false);
        return;
      }

      console.log('📱 发现有效的本地token，开始验证...');

      try {
        // 解析本地token获取用户信息
        const userData = AuthService.parseJWT(storedToken);
        if (!userData) {
          console.log('❌ 无法解析token，清除本地存储');
          AuthService.clearToken();
          setLoading(false);
          return;
        }

        // 进行后端验证
        try {
          console.log('� 验证token有效性...');
          const verifyResponse = await AuthService.verifyToken();

          if (verifyResponse.success) {
            console.log('✅ Token验证成功，设置用户状态');
            setUser(userData);
            setToken(storedToken);
          } else {
            console.log('❌ 后端验证失败，token无效');
            AuthService.clearToken();
          }
        } catch (error) {
          console.warn('⚠️ 后端验证失败，可能是网络问题:', error);
          handleVerificationFailure(storedToken, userData);
        }
      } catch (error) {
        console.error('❌ Token初始化失败:', error);
        AuthService.clearToken();
      }

      console.log('✅ 认证初始化完成');
      setLoading(false);
    };

    initializeAuth();
  }, [handleVerificationFailure]);

  /**
   * ===========================
   * 认证操作函数
   * ===========================
   */

  /**
   * 计算认证状态
   */
  const isAuthenticated = !!user && !!token;

  /**
   * 登出函数
   */
  const logout = useCallback(() => {
    AuthService.clearToken();
    setUser(null);
    setToken(null);
    message.success('已成功登出');
  }, []);

  /**
   * 执行定期token验证的核心逻辑
   */
  const performPeriodicVerification = useCallback(async (): Promise<void> => {
    console.log('🔍 开始定期token验证...');

    // 检查本地token状态
    const currentToken = AuthService.getToken();
    if (!currentToken || !AuthService.isTokenValid(currentToken)) {
      console.log('⏰ 本地token已过期，执行登出');
      logout();
      message.warning('登录已过期，请重新登录');
      return;
    }

    // 检查token剩余时间
    const remainingMinutes = getTokenRemainingTime(currentToken);
    if (remainingMinutes < 5) {
      console.log(`⏰ Token即将过期(剩余${remainingMinutes.toFixed(1)}分钟)，跳过网络验证`);
      return;
    }

    // 进行网络验证（带重试机制）
    const maxRetries = 2;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 网络验证尝试 ${attempt}/${maxRetries}`);

        const verifyResponse = await AuthService.verifyToken();

        if (verifyResponse.success) {
          console.log('✅ 定期验证通过');
          if (verifyResponse.user) {
            setUser(verifyResponse.user);
          }
          return; // 验证成功，退出
        } else {
          console.log('❌ 后端验证失败，token无效');
          logout();
          message.warning('登录已过期，请重新登录');
          return;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`⚠️ Token验证失败 (尝试 ${attempt}/${maxRetries}):`, errorMessage);

        if (attempt >= maxRetries) {
          // 最后一次尝试失败，检查错误类型
          const isNetworkError = errorMessage.toLowerCase().includes('network') ||
                                errorMessage.toLowerCase().includes('timeout') ||
                                errorMessage.toLowerCase().includes('fetch') ||
                                errorMessage.toLowerCase().includes('connection');

          if (isNetworkError) {
            console.log('🌐 网络问题导致验证失败，保持登录状态');
          } else {
            console.log('❌ 验证失败，可能是认证问题，但不强制登出');
          }
        } else {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
  }, [logout, getTokenRemainingTime]);

  /**
   * ===========================
   * 定期验证逻辑
   * ===========================
   */

  /**
   * 定期检查token有效性（每15分钟检查一次）
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(performPeriodicVerification, 15 * 60 * 1000);
    return () => clearInterval(interval);
  }, [isAuthenticated, performPeriodicVerification]);

  /**
   * 用户登录函数
   */
  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await AuthService.login(credentials);

      // 从标准化响应中获取access_token
      const access_token = response.data?.access_token || response.access_token;

      if (!access_token) {
        message.error('登录失败：未收到有效的访问令牌');
        return false;
      }

      // 解析token获取用户信息
      const userData = AuthService.parseJWT(access_token);
      if (!userData) {
        message.error('登录失败：无效的用户信息');
        return false;
      }

      // 保存token和用户信息
      AuthService.saveToken(access_token);
      setToken(access_token);
      setUser(userData);

      message.success('登录成功！');
      return true;
    } catch (error: unknown) {
      console.error('Login error:', error);

      // 提取错误信息
      const errorMessage = (() => {
        if (error && typeof error === 'object' && 'response' in error &&
            error.response && typeof error.response === 'object' && 'data' in error.response &&
            error.response.data && typeof error.response.data === 'object' && 'message' in error.response.data) {
          return (error.response.data as { message: string }).message;
        }
        return '登录失败，请检查用户名和密码';
      })();

      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * ===========================
   * Context 值和导出
   * ===========================
   */

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    verifyToken,
    isAuthenticated,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};