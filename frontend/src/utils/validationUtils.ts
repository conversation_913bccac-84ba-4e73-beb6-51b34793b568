/**
 * 区间配置接口
 */
export interface RangeConfig {
  min: number;
  max: number;
  fieldName: string;
  unit?: string;
}

/**
 * 验证规则配置
 */
export interface ValidationRule {
  /** 是否必填 */
  required?: boolean;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 自定义验证函数 */
  validator?: (value: unknown) => string | null;
}

/**
 * 表单验证工具类
 * 提供通用的表单验证方法
 */
export class ValidationUtils {
  /**
   * 验证数字范围
   * @param value 值
   * @param min 最小值
   * @param max 最大值
   * @param fieldName 字段名称
   * @returns 错误信息，无错误返回null
   */
  static validateNumber(
    value: number,
    min: number,
    max: number,
    fieldName: string,
  ): string | null {
    if (typeof value !== 'number' || isNaN(value)) {
      return `${fieldName}必须是有效数字`;
    }
    
    if (value < min) {
      return `${fieldName}不能小于${min}`;
    }
    
    if (value > max) {
      return `${fieldName}不能大于${max}`;
    }
    
    return null;
  }

  /**
   * 验证区间配置
   * @param minValue 最小值
   * @param maxValue 最大值
   * @param config 区间配置
   * @returns 错误信息数组
   */
  static validateRange(
    minValue: number,
    maxValue: number,
    config: RangeConfig,
  ): string[] {
    const errors: string[] = [];
    const { min, max, fieldName } = config;

    // 验证最小值
    const minError = this.validateNumber(minValue, min, max, `${fieldName}最小值`);
    if (minError) {
      errors.push(minError);
    }

    // 验证最大值
    const maxError = this.validateNumber(maxValue, min, max, `${fieldName}最大值`);
    if (maxError) {
      errors.push(maxError);
    }

    // 验证区间关系
    if (minValue >= maxValue) {
      errors.push(`${fieldName}最小值必须小于最大值`);
    }

    return errors;
  }

  /**
   * 验证买入金额区间
   * @param minAmount 最小买入金额
   * @param maxAmount 最大买入金额
   * @returns 错误信息数组
   */
  static validateBuyAmountRange(minAmount: number, maxAmount: number): string[] {
    return this.validateRange(minAmount, maxAmount, {
      min: 0.01,
      max: 10000,
      fieldName: '买入金额',
      unit: 'USDT',
    });
  }

  /**
   * 验证时间间隔区间（秒）
   * @param minSeconds 最小间隔（秒）
   * @param maxSeconds 最大间隔（秒）
   * @param fieldName 字段名称
   * @returns 错误信息数组
   */
  static validateTimeIntervalRange(
    minSeconds: number,
    maxSeconds: number,
    fieldName: string,
  ): string[] {
    return this.validateRange(minSeconds, maxSeconds, {
      min: 1,
      max: 3600, // 最大1小时
      fieldName,
      unit: '秒',
    });
  }

  /**
   * 验证买入间隔时间区间
   * @param minSeconds 最小间隔（秒）
   * @param maxSeconds 最大间隔（秒）
   * @returns 错误信息数组
   */
  static validateBuyIntervalRange(minSeconds: number, maxSeconds: number): string[] {
    return this.validateTimeIntervalRange(minSeconds, maxSeconds, '买入间隔时间');
  }

  /**
   * 验证卖出延迟时间区间
   * @param minSeconds 最小延迟（秒）
   * @param maxSeconds 最大延迟（秒）
   * @returns 错误信息数组
   */
  static validateSellDelayRange(minSeconds: number, maxSeconds: number): string[] {
    return this.validateTimeIntervalRange(minSeconds, maxSeconds, '卖出延迟时间');
  }

  /**
   * 验证买入次数
   * @param buyCount 买入次数
   * @returns 错误信息，无错误返回null
   */
  static validateBuyCount(buyCount: number): string | null {
    return this.validateNumber(buyCount, 1, 10, '买入次数');
  }

  /**
   * 验证池子冷却时间（分钟）
   * @param cooldownMinutes 冷却时间（分钟）
   * @returns 错误信息，无错误返回null
   */
  static validatePoolCooldown(cooldownMinutes: number): string | null {
    return this.validateNumber(cooldownMinutes, 1, 60, '池子冷却时间');
  }

  /**
   * 批量验证字段
   * @param fields 字段配置数组
   * @returns 错误信息数组
   */
  static validateFields(
    fields: Array<{
      value: unknown;
      rules: ValidationRule;
      fieldName: string;
    }>,
  ): string[] {
    const errors: string[] = [];

    for (const field of fields) {
      const { value, rules, fieldName } = field;

      // 必填验证
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`${fieldName}不能为空`);
        continue;
      }

      // 数字范围验证
      if (typeof value === 'number' && !isNaN(value)) {
        if (rules.min !== undefined && value < rules.min) {
          errors.push(`${fieldName}不能小于${rules.min}`);
        }
        if (rules.max !== undefined && value > rules.max) {
          errors.push(`${fieldName}不能大于${rules.max}`);
        }
      }

      // 自定义验证
      if (rules.validator) {
        const customError = rules.validator(value);
        if (customError) {
          errors.push(customError);
        }
      }
    }

    return errors;
  }

  /**
   * 创建Ant Design表单验证规则
   * @param config 验证配置
   * @returns Ant Design规则数组
   */
  static createAntdRules(config: {
    required?: boolean;
    min?: number;
    max?: number;
    fieldName: string;
    type?: 'number' | 'string';
  }) {
    const { required, min, max, fieldName, type = 'number' } = config;
    const rules: unknown[] = [];

    if (required) {
      rules.push({
        required: true,
        message: `请输入${fieldName}`,
      });
    }

    if (type === 'number') {
      rules.push({
        type: 'number',
        message: `${fieldName}必须是数字`,
      });

      if (min !== undefined) {
        rules.push({
          type: 'number',
          min,
          message: `${fieldName}不能小于${min}`,
        });
      }

      if (max !== undefined) {
        rules.push({
          type: 'number',
          max,
          message: `${fieldName}不能大于${max}`,
        });
      }
    }

    return rules;
  }
}
