/**
 * 前端交易状态工具类
 * 与后端的TradeStatusUtils保持一致
 */

/**
 * 池子交易状态类型
 */
export type PoolTradeStatusType = 
  | 'buying'
  | 'waiting_to_sell'
  | 'ready_to_sell'
  | 'selling'
  | 'completed';

/**
 * 操作类型
 */
export type OperationTypeType = 'BUY' | 'SELL';

/**
 * 状态工具类
 */
export class TradeStatusUtils {
  /**
   * 获取状态的中文描述
   */
  static getStatusText(status: PoolTradeStatusType): string {
    switch (status) {
      case 'buying':
        return '买入中';
      case 'waiting_to_sell':
        return '等待卖出';
      case 'ready_to_sell':
        return '准备卖出';
      case 'selling':
        return '卖出中';
      case 'completed':
        return '已完成';
      default:
        return status;
    }
  }

  /**
   * 获取状态对应的颜色
   */
  static getStatusColor(status: PoolTradeStatusType): string {
    switch (status) {
      case 'buying':
        return 'processing';
      case 'waiting_to_sell':
        return 'default';
      case 'ready_to_sell':
        return 'warning';
      case 'selling':
        return 'processing';
      case 'completed':
        return 'success';
      default:
        return 'default';
    }
  }

  /**
   * 检查状态是否为活跃状态
   */
  static isActiveStatus(status: PoolTradeStatusType): boolean {
    return ['buying', 'waiting_to_sell', 'ready_to_sell', 'selling'].includes(status);
  }

  /**
   * 检查状态是否为完成状态
   */
  static isCompletedStatus(status: PoolTradeStatusType): boolean {
    return status === 'completed';
  }

  /**
   * 获取操作类型的中文描述
   */
  static getOperationText(operation: OperationTypeType): string {
    switch (operation) {
      case 'BUY':
        return '买入';
      case 'SELL':
        return '卖出';
      default:
        return operation;
    }
  }

  /**
   * 获取操作类型对应的颜色
   */
  static getOperationColor(operation: OperationTypeType): string {
    switch (operation) {
      case 'BUY':
        return 'green';
      case 'SELL':
        return 'red';
      default:
        return 'default';
    }
  }
}
