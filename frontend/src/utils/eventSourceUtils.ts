import { AuthService } from '../services/authService';

/**
 * EventSource配置选项
 */
export interface EventSourceConfig {
  /** API端点路径 */
  endpoint: string;
  /** 是否需要认证token */
  requireAuth?: boolean;
  /** 额外的查询参数 */
  queryParams?: Record<string, string>;
  /** 连接选项 */
  options?: EventSourceInit;
}

/**
 * EventSource事件处理器
 */
export interface EventSourceHandlers {
  /** 连接打开时的处理器 */
  onOpen?: (event: Event) => void;
  /** 接收到消息时的处理器 */
  onMessage?: (event: MessageEvent) => void;
  /** 连接错误时的处理器 */
  onError?: (event: Event) => void;
  /** 连接关闭时的处理器 */
  onClose?: () => void;
}

/**
 * EventSource工具类
 * 提供统一的EventSource创建和管理功能
 */
export class EventSourceUtils {
  /**
   * 创建EventSource连接
   * @param config 配置选项
   * @param handlers 事件处理器
   * @returns EventSource实例
   */
  static createEventSource(
    config: EventSourceConfig,
    handlers: EventSourceHandlers = {},
  ): EventSource {
    const url = this.buildEventSourceUrl(config);

    console.log(`创建EventSource连接: ${config.endpoint}`);
    console.log(`完整URL: ${url}`);

    const eventSource = new EventSource(url, {
      withCredentials: true,
      ...config.options,
    });

    // 设置事件处理器
    this.setupEventHandlers(eventSource, handlers, config.endpoint);

    return eventSource;
  }

  /**
   * 构建EventSource URL
   * @param config 配置选项
   * @returns 完整的URL
   */
  private static buildEventSourceUrl(config: EventSourceConfig): string {
    const { endpoint, requireAuth = true, queryParams = {} } = config;

    // 获取认证token
    if (requireAuth) {
      const token = AuthService.getToken();
      if (!token) {
        throw new Error('未找到认证token，请重新登录');
      }
      queryParams.token = token;
    }

    // 构建查询参数
    const searchParams = new URLSearchParams(queryParams);
    const queryString = searchParams.toString();

    // 构建完整URL - 统一使用代理路径
    // 所有环境都使用 /api 代理路径
    // nginx会将 /api/* 请求代理到后端服务
    const eventSourceUrl = `/api${endpoint}`;

    return queryString ? `${eventSourceUrl}?${queryString}` : eventSourceUrl;
  }

  /**
   * 设置事件处理器
   * @param eventSource EventSource实例
   * @param handlers 事件处理器
   * @param endpointName 端点名称（用于日志）
   */
  private static setupEventHandlers(
    eventSource: EventSource,
    handlers: EventSourceHandlers,
    endpointName: string,
  ): void {
    // 连接打开事件
    eventSource.addEventListener('open', (event) => {
      console.log(`${endpointName} EventSource 连接已打开`);
      handlers.onOpen?.(event);
    });

    // 接收消息事件
    eventSource.addEventListener('message', (event) => {
      try {
        console.log(`${endpointName} 收到消息:`, event.data);
        handlers.onMessage?.(event);
      } catch (error) {
        console.error(`${endpointName} 处理消息失败:`, error);
      }
    });

    // 连接错误事件
    eventSource.addEventListener('error', (event) => {
      console.error(`${endpointName} EventSource 连接错误:`, event);
      handlers.onError?.(event);
    });

    // 连接关闭处理（通过状态检查）
    const checkConnectionState = () => {
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log(`${endpointName} EventSource 连接已关闭`);
        handlers.onClose?.();
      }
    };

    // 定期检查连接状态
    const stateCheckInterval = setInterval(() => {
      if (eventSource.readyState === EventSource.CLOSED) {
        clearInterval(stateCheckInterval);
        checkConnectionState();
      }
    }, 1000);
  }

  /**
   * 安全关闭EventSource连接
   * @param eventSource EventSource实例
   * @param endpointName 端点名称（用于日志）
   */
  static closeEventSource(eventSource: EventSource | null, endpointName: string): void {
    if (eventSource) {
      console.log(`关闭 ${endpointName} EventSource 连接`);
      eventSource.close();
    }
  }

  /**
   * 检查EventSource连接状态
   * @param eventSource EventSource实例
   * @returns 连接状态描述
   */
  static getConnectionStatus(eventSource: EventSource | null): string {
    if (!eventSource) {
      return 'disconnected';
    }

    switch (eventSource.readyState) {
      case EventSource.CONNECTING:
        return 'connecting';
      case EventSource.OPEN:
        return 'connected';
      case EventSource.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  /**
   * 创建重连机制
   * @param createFn 创建EventSource的函数
   * @param maxRetries 最大重试次数
   * @param retryDelay 重试延迟（毫秒）
   * @returns 带重连机制的EventSource创建函数
   */
  static createWithRetry(
    createFn: () => EventSource,
    maxRetries: number = 3,
    retryDelay: number = 5000,
  ): () => EventSource {
    let retryCount = 0;
    
    return function createWithRetryLogic(): EventSource {
      const eventSource = createFn();
      
      eventSource.addEventListener('error', () => {
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`EventSource连接失败，${retryDelay}ms后进行第${retryCount}次重试`);
          
          setTimeout(() => {
            eventSource.close();
            createWithRetryLogic();
          }, retryDelay);
        } else {
          console.error(`EventSource连接失败，已达到最大重试次数(${maxRetries})`);
        }
      });
      
      eventSource.addEventListener('open', () => {
        retryCount = 0; // 连接成功后重置重试计数
      });
      
      return eventSource;
    };
  }
}
