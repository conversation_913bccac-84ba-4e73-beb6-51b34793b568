/**
 * 通用工具函数
 * 消除重复代码，提供统一的工具方法
 */

/**
 * API Key显示工具
 */
export class ApiKeyUtils {
  /**
   * 格式化API Key显示（截断中间部分）
   * @param apiKey 完整的API Key
   * @param startLength 开始显示的字符数，默认8
   * @param endLength 结尾显示的字符数，默认8
   * @returns 格式化后的显示字符串
   */
  static formatDisplay(apiKey: string, startLength = 8, endLength = 8): string {
    if (!apiKey || apiKey.length <= startLength + endLength) {
      return apiKey;
    }
    return `${apiKey.slice(0, startLength)}...${apiKey.slice(-endLength)}`;
  }

  /**
   * 验证API Key格式
   * @param apiKey API Key字符串
   * @returns 是否为有效格式
   */
  static isValidFormat(apiKey: string): boolean {
    return typeof apiKey === 'string' && apiKey.length > 0 && apiKey.trim() === apiKey;
  }

  /**
   * 清理API Key（移除空格等）
   * @param apiKey 原始API Key
   * @returns 清理后的API Key
   */
  static sanitize(apiKey: string): string {
    return apiKey?.trim() || '';
  }
}

/**
 * 批次ID工具
 */
export class BatchIdUtils {
  /**
   * 生成批次ID
   * @param prefix 前缀，默认'batch'
   * @returns 唯一的批次ID
   */
  static generate(prefix = 'batch'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * 格式化批次ID显示
   * @param batchId 完整的批次ID
   * @param maxLength 最大显示长度，默认16
   * @returns 格式化后的显示字符串
   */
  static formatDisplay(batchId: string, maxLength = 16): string {
    if (!batchId || batchId.length <= maxLength) {
      return batchId;
    }
    const startLength = Math.floor(maxLength / 2) - 2;
    const endLength = maxLength - startLength - 3;
    return `${batchId.slice(0, startLength)}...${batchId.slice(-endLength)}`;
  }

  /**
   * 解析批次ID中的时间戳
   * @param batchId 批次ID
   * @returns 时间戳或null
   */
  static extractTimestamp(batchId: string): number | null {
    const parts = batchId.split('_');
    if (parts.length >= 2) {
      const timestamp = parseInt(parts[1], 10);
      return isNaN(timestamp) ? null : timestamp;
    }
    return null;
  }
}

/**
 * 状态格式化工具
 */
export class StatusUtils {
  /**
   * 格式化批次状态显示文本
   */
  static formatBatchStatus(status: string): string {
    const statusMap: Record<string, string> = {
      pending: '等待中',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  }

  /**
   * 获取状态对应的颜色
   */
  static getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      pending: 'default',
      processing: 'blue',
      completed: 'green',
      failed: 'red',
      cancelled: 'orange',
    };
    return colorMap[status] || 'default';
  }

  /**
   * 格式化点亮状态
   */
  static formatLightStatus(status: string): string {
    const statusMap: Record<string, string> = {
      first_light: '待点亮',
      already_lighted: '已点亮',
      unknown: '未知',
    };
    return statusMap[status] || status;
  }
}

/**
 * 时间格式化工具
 * 统一使用东八区（UTC+8）时间格式
 */
export class TimeUtils {
  /**
   * 东八区时区偏移量（毫秒）
   */
  private static readonly UTC8_OFFSET = 8 * 60 * 60 * 1000;

  /**
   * 将任何时间转换为东八区时间
   * @param timestamp 时间戳、Date对象或时间字符串
   * @returns 东八区Date对象
   */
  static toUTC8Date(timestamp: string | Date | number): Date {
    const date = new Date(timestamp);
    const utc = date.getTime() + (date.getTimezoneOffset() * 60 * 1000);
    return new Date(utc + this.UTC8_OFFSET);
  }

  /**
   * 格式化为东八区标准时间格式
   * @param timestamp 时间戳、Date对象或时间字符串
   * @param showTimezone 是否显示时区标识，默认true
   * @returns 格式化的时间字符串 YYYY-MM-DD HH:mm:ss（东八区）
   */
  static formatUTC8DateTime(timestamp: string | Date | number, showTimezone = true): string {
    const utc8Date = this.toUTC8Date(timestamp);
    
    const year = utc8Date.getFullYear();
    const month = String(utc8Date.getMonth() + 1).padStart(2, '0');
    const day = String(utc8Date.getDate()).padStart(2, '0');
    const hours = String(utc8Date.getHours()).padStart(2, '0');
    const minutes = String(utc8Date.getMinutes()).padStart(2, '0');
    const seconds = String(utc8Date.getSeconds()).padStart(2, '0');
    
    const timeStr = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    return showTimezone ? `${timeStr}（东八区）` : timeStr;
  }

  /**
   * 格式化为东八区日期格式
   * @param timestamp 时间戳、Date对象或时间字符串
   * @returns 格式化的日期字符串 YYYY-MM-DD
   */
  static formatUTC8Date(timestamp: string | Date | number): string {
    const utc8Date = this.toUTC8Date(timestamp);
    
    const year = utc8Date.getFullYear();
    const month = String(utc8Date.getMonth() + 1).padStart(2, '0');
    const day = String(utc8Date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }

  /**
   * 格式化为东八区时间格式
   * @param timestamp 时间戳、Date对象或时间字符串
   * @returns 格式化的时间字符串 HH:mm:ss
   */
  static formatUTC8Time(timestamp: string | Date | number): string {
    const utc8Date = this.toUTC8Date(timestamp);
    
    const hours = String(utc8Date.getHours()).padStart(2, '0');
    const minutes = String(utc8Date.getMinutes()).padStart(2, '0');
    const seconds = String(utc8Date.getSeconds()).padStart(2, '0');
    
    return `${hours}:${minutes}:${seconds}`;
  }

  /**
   * 获取当前东八区时间字符串
   * @param showTimezone 是否显示时区标识，默认true
   * @returns 当前东八区时间字符串
   */
  static getCurrentUTC8Time(showTimezone = true): string {
    return this.formatUTC8DateTime(new Date(), showTimezone);
  }

  /**
   * 格式化执行时间（保持原有功能）
   * @param startTime 开始时间
   * @param endTime 结束时间（可选）
   * @returns 格式化的时间字符串
   */
  static formatExecutionTime(startTime: string | Date, endTime?: string | Date): string {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();
    
    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 格式化相对时间（基于东八区时间计算）
   * @param timestamp 时间戳
   * @returns 相对时间字符串
   */
  static formatRelativeTime(timestamp: string | Date | number): string {
    const now = Date.now();
    const time = new Date(timestamp).getTime();
    const diff = now - time;

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return `${seconds}秒前`;
    }
  }

  /**
   * 格式化时长（毫秒转换为可读格式）
   * @param durationMs 持续时间（毫秒）
   * @returns 格式化的时长字符串
   */
  static formatDuration(durationMs: number | undefined | null): string {
    // 处理无效输入
    if (durationMs === undefined || durationMs === null || isNaN(durationMs) || durationMs < 0) {
      return '0秒';
    }

    // 确保是有限数值
    if (!isFinite(durationMs)) {
      return '0秒';
    }

    // 转换为整数避免浮点数问题
    const safeDurationMs = Math.floor(Number(durationMs));
    
    const seconds = Math.floor(safeDurationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 检查时间是否为今天（基于东八区）
   * @param timestamp 时间戳
   * @returns 是否为今天
   */
  static isToday(timestamp: string | Date | number): boolean {
    const targetDate = this.toUTC8Date(timestamp);
    const today = this.toUTC8Date(new Date());
    
    return targetDate.toDateString() === today.toDateString();
  }

  /**
   * 获取东八区时间的Unix时间戳
   * @param timestamp 时间戳、Date对象或时间字符串
   * @returns Unix时间戳（秒）
   */
  static getUTC8Unix(timestamp: string | Date | number): number {
    return Math.floor(this.toUTC8Date(timestamp).getTime() / 1000);
  }
}

/**
 * 数值格式化工具
 */
export class NumberUtils {
  /**
   * 格式化余额显示
   * @param balance 余额数值
   * @param precision 小数位数，默认6
   * @returns 格式化的余额字符串
   */
  static formatBalance(balance: number, precision = 6): string {
    if (balance === 0) {
      return '0';
    }
    return balance.toFixed(precision).replace(/\.?0+$/, '');
  }

  /**
   * 格式化百分比
   * @param value 数值
   * @param total 总数
   * @param precision 小数位数，默认1
   * @returns 百分比字符串
   */
  static formatPercentage(value: number, total: number, precision = 1): string {
    if (total === 0) {
      return '0%';
    }
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(precision)}%`;
  }

  /**
   * 格式化大数字（添加千分位分隔符）
   * @param num 数字
   * @returns 格式化的数字字符串
   */
  static formatLargeNumber(num: number): string {
    return num.toLocaleString();
  }

  /**
   * 格式化价格显示（支持极小数值）
   * @param price 价格数值
   * @returns 格式化的价格字符串
   */
  static formatPrice(price: number): string {
    if (price === 0) {
      return '0';
    }

    // 对于非常小的数值，使用科学计数法或显示更多小数位
    if (price < 0.000001) {
      // 小于 0.000001 时使用科学计数法
      return price.toExponential(2);
    } else if (price < 0.01) {
      // 小于 0.01 时显示 8 位小数
      return price.toFixed(8);
    } else if (price < 1) {
      // 小于 1 时显示 4 位小数
      return price.toFixed(4);
    } else if (price >= 1000000) {
      return `${(price / 1000000).toFixed(2)}M`;
    } else if (price >= 1000) {
      return `${(price / 1000).toFixed(2)}K`;
    } else {
      return price.toFixed(2);
    }
  }
}

/**
 * 验证工具
 */
export class ValidationUtils {
  /**
   * 验证邮箱格式
   * @param email 邮箱地址
   * @returns 是否为有效邮箱
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证数字范围
   * @param value 数值
   * @param min 最小值
   * @param max 最大值
   * @returns 是否在有效范围内
   */
  static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }

  /**
   * 验证必填字段
   * @param value 值
   * @returns 是否为有效值
   */
  static isRequired(value: unknown): boolean {
    if (value === null || value === undefined) {
      return false;
    }
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return true;
  }
}

/**
 * 错误处理工具
 */
export class ErrorUtils {
  /**
   * 格式化错误消息
   * @param error 错误对象
   * @param defaultMessage 默认消息
   * @returns 格式化的错误消息
   */
  static formatErrorMessage(error: unknown, defaultMessage = '操作失败'): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return defaultMessage;
  }

  /**
   * 判断是否为网络错误
   * @param error 错误对象
   * @returns 是否为网络错误
   */
  static isNetworkError(error: unknown): boolean {
    if (!error || typeof error !== 'object') {
      return false;
    }

    const err = error as { code?: string; message?: string };
    return !!(
      err.code === 'NETWORK_ERROR' ||
      err.code === 'ECONNABORTED' ||
      err.message?.includes('Network Error') ||
      err.message?.includes('timeout')
    );
  }
}

// 所有工具类已通过 export class 导出，无需重复导出
