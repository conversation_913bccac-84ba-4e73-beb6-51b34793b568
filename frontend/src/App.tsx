import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import ErrorBoundary from './components/ErrorBoundary';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('./pages/LoginPage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const UserManagementPage = React.lazy(() => import('./pages/UserManagementPage'));
const EnhancedBatchLightPage = React.lazy(() => import('./pages/EnhancedBatchLightPage'));
const RandomLightPage = React.lazy(() => import('./pages/RandomLightPage'));
const RandomPoolTradePage = React.lazy(() => import('./pages/RandomPoolTradePage'));
const InnerDiskNewCoinPage = React.lazy(() => import('./pages/InnerDiskNewCoinPage'));


// 加载中组件
const PageLoading: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" tip="页面加载中..." />
  </div>
);

import './App.css';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <ErrorBoundary>
        <AuthProvider>
          <Router>
            <Suspense fallback={<PageLoading />}>
              <Routes>
                {/* 登录页面 */}
                <Route path="/login" element={<LoginPage />} />

            {/* 受保护的路由 */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <MainLayout>
                  <DashboardPage />
                </MainLayout>
              </ProtectedRoute>
            } />

            <Route path="/users" element={
              <ProtectedRoute>
                <MainLayout>
                  <UserManagementPage />
                </MainLayout>
              </ProtectedRoute>
            } />

            <Route path="/batch-light" element={
              <ProtectedRoute>
                <MainLayout>
                  <EnhancedBatchLightPage />
                </MainLayout>
              </ProtectedRoute>
            } />

            <Route path="/random-light" element={
              <ProtectedRoute>
                <MainLayout>
                  <RandomLightPage />
                </MainLayout>
              </ProtectedRoute>
            } />

            <Route path="/random-pool-trade" element={
              <ProtectedRoute>
                <MainLayout>
                  <RandomPoolTradePage />
                </MainLayout>
              </ProtectedRoute>
            } />

            <Route path="/inner-disk-new-coin" element={
              <ProtectedRoute>
                <MainLayout>
                  <InnerDiskNewCoinPage />
                </MainLayout>
              </ProtectedRoute>
            } />

            {/* 默认重定向 */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            
                {/* 404页面 */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </Suspense>
          </Router>
        </AuthProvider>
      </ErrorBoundary>
    </ConfigProvider>
  );
}

export default App;
