# 多阶段构建 - 构建阶段
FROM node:20-alpine AS builder

# 配置 npm 镜像源（解决网络问题）
RUN npm config set registry https://registry.npmmirror.com/

# 设置工作目录
WORKDIR /app

# 复制 package 文件（优化缓存层）
COPY package*.json yarn.lock ./

# 安装依赖
RUN yarn install --frozen-lockfile && yarn cache clean

# 复制构建配置文件
COPY tsconfig*.json vite.config.ts index.html ./

# 复制源代码和公共资源（放在依赖安装之后，优化缓存）
COPY src ./src/
COPY public ./public/

# 构建参数（从统一配置传入）
ARG VITE_API_URL=http://localhost:3000
ARG VITE_APP_TITLE=Nine Light
ARG VITE_APP_VERSION=1.0.0
ARG VITE_DEBUG=false

# 设置环境变量
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_APP_TITLE=$VITE_APP_TITLE
ENV VITE_APP_VERSION=$VITE_APP_VERSION
ENV VITE_DEBUG=$VITE_DEBUG

# 构建应用
RUN yarn build

# 生产阶段 - Nginx
FROM nginx:alpine AS production

# 安装 curl (用于健康检查)
RUN apk add --no-cache curl

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建非 root 用户
RUN addgroup -g 1001 -S nginx-user
RUN adduser -S nginx-user -u 1001 -G nginx-user

# 设置权限
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html
RUN chown -R nginx-user:nginx-user /var/cache/nginx
RUN chown -R nginx-user:nginx-user /var/log/nginx
RUN chown -R nginx-user:nginx-user /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R nginx-user:nginx-user /var/run/nginx.pid

# 切换到非 root 用户
USER nginx-user

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80 || exit 1

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
