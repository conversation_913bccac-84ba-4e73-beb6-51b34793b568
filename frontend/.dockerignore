# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
dist-ssr/
build/

# 开发文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 测试文件
coverage/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# OS文件
.DS_Store
.DS_Store?
._*
Thumbs.db

# Git文件
.git/
.gitignore

# 文档文件
README.md
docs/
*.md

# 临时文件
tmp/
temp/

# Vite相关
.vite/
