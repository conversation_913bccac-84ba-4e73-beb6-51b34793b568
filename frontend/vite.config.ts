import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 尝试从根目录加载环境变量（统一配置源）
  let env = loadEnv(mode, '..', '')

  // 如果根目录没有找到环境变量，则从当前目录加载（向后兼容）
  if (!env.VITE_API_URL && !env.VITE_DEV_PORT) {
    env = loadEnv(mode, '.', '')
  }

  return {
    plugins: [react()],

    // 开发服务器配置
    server: {
      port: parseInt(env.VITE_DEV_PORT) || 5173,
      host: true, // 允许外部访问
      proxy: {
        // 开发环境 API 代理
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:3001',
          changeOrigin: true,
          // 不重写路径，保持 /api 前缀
        },
      },
    },

    // 构建配置
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      minify: mode === 'production' ? 'esbuild' : false,
      rollupOptions: {
        output: {
          manualChunks: {
            // React核心库
            'react-vendor': ['react', 'react-dom'],
            // 路由库
            'router': ['react-router-dom'],
            // Ant Design核心
            'antd-core': ['antd'],
            // Ant Design图标
            'antd-icons': ['@ant-design/icons'],
            // 工具库
            'utils': ['axios', 'dayjs'],
          },
        },
      },
    },

    // 环境变量前缀
    envPrefix: 'VITE_',

    // 预览服务器配置
    preview: {
      port: parseInt(env.VITE_PREVIEW_PORT) || 4173,
      host: true,
    },
  }
})
