#!/bin/bash

# SSE连接和认证测试脚本

echo "🧪 测试前端认证和SSE事件处理修复"
echo "========================================="

BASE_URL="http://localhost:3000"
API_URL="http://localhost:3001"

# 1. 测试登录认证
echo "1. 测试登录认证..."
LOGIN_RESPONSE=$(curl -s -X POST \
  "$API_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "VXR&y3QX&by75w"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，无法获取token"
  exit 1
fi

echo "✅ 登录成功，token: ${TOKEN:0:20}..."

# 2. 测试token验证
echo ""
echo "2. 测试token验证..."
VERIFY_RESPONSE=$(curl -s -X POST \
  "$API_URL/api/auth/verify" \
  -H "Authorization: Bearer $TOKEN")

echo "验证响应: $VERIFY_RESPONSE"

# 3. 测试SSE端点连接（模拟）
echo ""
echo "3. 测试SSE端点连接..."
echo "注意：这只是测试端点是否可访问，实际SSE连接需要在浏览器中测试"

SSE_URL="$API_URL/api/random-pool-trade/events?token=$TOKEN"
echo "SSE端点: $SSE_URL"

# 使用curl测试SSE端点（只获取前几行）
timeout 5 curl -s -N "$SSE_URL" | head -n 5 || echo "SSE端点测试完成（5秒超时）"

echo ""
echo "✅ 认证和SSE基础连接测试完成"
echo "请在浏览器中访问前端页面进行完整的SSE事件处理测试"
echo "前端地址: $BASE_URL"