#!/bin/bash

# Nine Light 停止服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查部署模式
check_deploy_mode() {
    # 优先检查standalone模式（通过PID文件）
    if [ -d ".pids" ] && ([ -f ".pids/backend.pid" ] || [ -f ".pids/frontend.pid" ]); then
        echo "standalone"
    # 检查Docker模式
    elif docker ps --format "table {{.Names}}" 2>/dev/null | grep -q "nine-light"; then
        echo "docker"
    # 检查.env文件中的配置
    elif [ -f ".env" ] && grep -q "DEPLOY_MODE=docker" .env; then
        echo "docker"
    else
        echo "unknown"
    fi
}

# 停止 Docker 服务
stop_docker_services() {
    log_info "停止 Docker 服务..."
    
    if docker compose version &> /dev/null; then
        docker compose down
    elif command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        log_error "Docker Compose 未找到"
        exit 1
    fi
    
    log_success "Docker 服务已停止"
}

# 停止独立服务
stop_standalone_services() {
    log_info "停止独立服务..."

    local stopped_any=false

    # 停止后端服务
    if [ -f ".pids/backend.pid" ]; then
        local backend_pid=$(cat .pids/backend.pid)
        if kill -0 $backend_pid 2>/dev/null; then
            log_info "停止后端服务 (PID: $backend_pid)..."
            kill $backend_pid
            # 等待进程结束
            sleep 2
            if kill -0 $backend_pid 2>/dev/null; then
                log_warning "后端服务未响应，强制终止..."
                kill -9 $backend_pid 2>/dev/null || true
            fi
            stopped_any=true
        else
            log_warning "后端服务进程 (PID: $backend_pid) 已不存在"
        fi
        rm -f .pids/backend.pid
    fi

    # 停止前端服务
    if [ -f ".pids/frontend.pid" ]; then
        local frontend_pid=$(cat .pids/frontend.pid)
        if kill -0 $frontend_pid 2>/dev/null; then
            log_info "停止前端服务 (PID: $frontend_pid)..."
            kill $frontend_pid
            # 等待进程结束
            sleep 2
            if kill -0 $frontend_pid 2>/dev/null; then
                log_warning "前端服务未响应，强制终止..."
                kill -9 $frontend_pid 2>/dev/null || true
            fi
            stopped_any=true
        else
            log_warning "前端服务进程 (PID: $frontend_pid) 已不存在"
        fi
        rm -f .pids/frontend.pid
    fi

    # 额外检查：查找可能遗漏的Nine Light相关进程
    local remaining_pids=$(pgrep -f "nine-light.*node.*dist/main|serve.*dist.*3000" 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        log_info "发现遗漏的进程，正在清理: $remaining_pids"
        echo $remaining_pids | xargs kill 2>/dev/null || true
        sleep 1
        # 如果还在运行，强制终止
        remaining_pids=$(pgrep -f "nine-light.*node.*dist/main|serve.*dist.*3000" 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            echo $remaining_pids | xargs kill -9 2>/dev/null || true
        fi
        stopped_any=true
    fi

    # 清理 PID 目录
    if [ -d ".pids" ] && [ -z "$(ls -A .pids 2>/dev/null)" ]; then
        rmdir .pids
        log_info "已清理 PID 目录"
    fi

    if [ "$stopped_any" = true ]; then
        log_success "独立服务已停止"
    else
        log_warning "没有找到运行中的服务"
    fi
}

# 强制停止所有相关进程
force_stop() {
    log_warning "强制停止所有相关进程..."
    
    # 查找并停止 Node.js 进程
    local node_pids=$(pgrep -f "node.*nine-light\|nest start\|vite.*dev" || true)
    if [ -n "$node_pids" ]; then
        log_info "停止 Node.js 进程: $node_pids"
        echo $node_pids | xargs kill -9 2>/dev/null || true
    fi
    
    # 查找并停止 Python HTTP 服务器
    local python_pids=$(pgrep -f "python.*http.server\|python.*SimpleHTTPServer" || true)
    if [ -n "$python_pids" ]; then
        log_info "停止 Python HTTP 服务器: $python_pids"
        echo $python_pids | xargs kill -9 2>/dev/null || true
    fi
    
    log_success "强制停止完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
Nine Light 停止服务脚本

用法: $0 [选项]

选项:
    -f, --force     强制停止所有相关进程
    -h, --help      显示此帮助信息

EOF
}

# 主函数
main() {
    local force_stop_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_stop_flag=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "Nine Light 停止服务脚本"
    echo "========================"
    echo
    
    if [ "$force_stop_flag" = true ]; then
        force_stop
        exit 0
    fi
    
    # 检查部署模式
    local deploy_mode=$(check_deploy_mode)
    
    case $deploy_mode in
        docker)
            stop_docker_services
            ;;
        standalone)
            stop_standalone_services
            ;;
        unknown)
            log_warning "无法确定部署模式，尝试停止所有可能的服务..."
            stop_docker_services 2>/dev/null || true
            stop_standalone_services 2>/dev/null || true
            ;;
    esac
    
    log_success "服务停止完成"
}

# 执行主函数
main "$@"
