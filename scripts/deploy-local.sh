#!/bin/bash

# 本地部署脚本

deploy_local() {
    log_info "开始本地 Docker 部署..."

    # 创建必要的目录
    create_required_directories

    # 创建 .env 文件
    create_env_file_local

    # 停止现有服务
    stop_existing_services_local

    # 构建和启动服务
    build_and_start_services_local

    # 等待服务启动
    wait_for_services_local

    log_success "本地 Docker 部署完成"
}

create_required_directories() {
    log_info "创建必要的目录..."

    # 创建后端日志目录
    mkdir -p backend/logs
    log_info "创建后端日志目录: backend/logs"

    # 创建后端数据目录
    mkdir -p backend/data/persistence
    log_info "创建后端数据目录: backend/data"

    # 设置目录权限（确保Docker容器可以写入）
    chmod 755 backend/logs backend/data
    chmod 755 backend/data/persistence

    log_success "必要目录创建完成"
}

create_env_file_local() {
    log_info "检查本地环境配置文件..."

    # 检查是否已有 .env 文件
    if [ -f ".env" ]; then
        log_info "使用现有的 .env 配置文件"

        # 验证必要的配置项
        if ! grep -q "DATABASE_URL=" .env || grep -q "mysql://user:password@your-database-host" .env; then
            log_warning "检测到默认的数据库配置，请确保已正确配置 DATABASE_URL"
        fi

        # 验证管理员密码配置
        validate_admin_credentials

        # 更新端口配置（如果通过命令行指定）
        if [ "$FRONTEND_PORT" != "8080" ] || [ "$BACKEND_PORT" != "3000" ]; then
            log_info "更新端口配置..."
            sed -i.bak "s/^FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" .env
            sed -i.bak "s/^BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" .env
            sed -i.bak "s/^PORT=.*/PORT=$BACKEND_PORT/" .env
        fi
    else
        log_info "创建新的环境配置文件..."
        cp .env.example .env 2>/dev/null || {
            log_error "未找到 .env.example 文件，请手动创建 .env 文件"
            exit 1
        }

        # 设置端口配置
        sed -i.bak "s/^FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" .env
        sed -i.bak "s/^BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" .env
        sed -i.bak "s/^PORT=.*/PORT=$BACKEND_PORT/" .env

        # 生成随机密钥
        local jwt_secret="jwt_secret_$(openssl rand -hex 32)"
        local admin_password="admin_$(openssl rand -hex 8)"

        sed -i.bak "s/^JWT_SECRET=.*/JWT_SECRET=$jwt_secret/" .env
        sed -i.bak "s/^ADMIN_PASSWORD=.*/ADMIN_PASSWORD=$admin_password/" .env

        log_warning "请编辑 .env 文件中的 DATABASE_URL 以连接到您的数据库"
    fi

    # 清理备份文件
    rm -f .env.bak

    log_success "本地环境配置检查完成"
}

stop_existing_services_local() {
    log_info "停止现有本地服务..."
    
    # 使用本地 compose 文件
    if docker compose -f docker-compose.local.yml ps -q | grep -q .; then
        docker compose -f docker-compose.local.yml down
        log_info "已停止现有本地 Docker 服务"
    fi

    # 清理悬挂的镜像（如果强制重建）
    if [[ "$FORCE_REBUILD" == true ]]; then
        log_info "清理旧镜像..."
        docker system prune -f
        docker compose -f docker-compose.local.yml build --no-cache
    fi
}

build_and_start_services_local() {
    log_info "构建和启动本地服务..."

    local compose_args=""
    if [[ "$VERBOSE" == true ]]; then
        compose_args="--verbose"
    fi

    # 首先构建后端代码
    log_info "构建后端TypeScript代码..."
    cd backend
    if [[ "$SKIP_DEPS" != true ]]; then
        log_info "安装后端依赖..."
        yarn install --frozen-lockfile
    fi
    yarn build
    cd ..

    # 根据FORCE_REBUILD决定是否清理缓存
    if [[ "$FORCE_REBUILD" == true ]]; then
        log_info "强制重新构建 - 清理本地镜像和缓存..."
        docker rmi nine-light-frontend-local nine-light-backend-local 2>/dev/null || true
        docker builder prune -f
        docker compose -f docker-compose.local.yml build --no-cache $compose_args
    else
        log_info "增量构建Docker镜像 - 利用Docker缓存..."
        docker compose -f docker-compose.local.yml build $compose_args
    fi

    # 启动服务
    docker compose -f docker-compose.local.yml up -d $compose_args

    log_success "本地服务已启动"
}

wait_for_services_local() {
    log_info "等待本地服务启动..."

    local max_attempts=30
    local attempt=0

    # 等待后端服务 (本地环境固定使用3001端口)
    log_info "等待后端服务启动..."
    while [ $attempt -lt $max_attempts ]; do
        if curl -f http://localhost:3001/api/auth/health >/dev/null 2>&1; then
            log_success "后端服务已启动"
            break
        fi

        attempt=$((attempt + 1))
        if [ $attempt -eq $max_attempts ]; then
            log_error "后端服务启动超时"
            log_info "请检查数据库连接配置和日志: docker compose -f docker-compose.local.yml logs backend"
            exit 1
        fi

        sleep 3
    done

    # 等待前端服务 (本地环境固定使用3000端口)
    log_info "等待前端服务启动..."
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        # 前端是静态文件服务器，检查根路径即可
        if curl -f http://localhost:3000/ >/dev/null 2>&1; then
            log_success "前端服务已启动"
            break
        fi

        attempt=$((attempt + 1))
        if [ $attempt -eq $max_attempts ]; then
            log_error "前端服务启动超时"
            log_info "请检查日志: docker compose -f docker-compose.local.yml logs frontend"
            exit 1
        fi

        sleep 2
    done

    log_success "所有本地服务已启动"
}

# 验证管理员凭据配置
validate_admin_credentials() {
    log_info "验证管理员凭据配置..."

    local admin_username=$(grep '^ADMIN_USERNAME=' .env 2>/dev/null | cut -d'=' -f2- | tr -d '"' || echo "")
    local admin_password=$(grep '^ADMIN_PASSWORD=' .env 2>/dev/null | cut -d'=' -f2- | tr -d '"' || echo "")

    if [ -z "$admin_username" ]; then
        log_error "ADMIN_USERNAME 未配置"
        exit 1
    fi

    if [ -z "$admin_password" ]; then
        log_error "ADMIN_PASSWORD 未配置"
        exit 1
    fi

    log_success "管理员凭据配置验证完成"
    log_info "用户名: $admin_username"
    log_info "密码: ${admin_password:0:3}***${admin_password: -3} (已部分隐藏)"
}