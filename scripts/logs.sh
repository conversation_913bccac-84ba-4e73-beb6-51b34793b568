#!/bin/bash

# Nine Light 日志查看脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Nine Light 日志查看脚本

用法: $0 [选项] [服务名]

服务名:
    frontend    前端服务日志
    backend     后端服务日志
    mysql       数据库服务日志 (仅 Docker 模式)
    all         所有服务日志 (默认)

选项:
    -f, --follow    实时跟踪日志
    -n, --lines N   显示最后 N 行 (默认: 100)
    -t, --tail      等同于 --follow
    -h, --help      显示此帮助信息

示例:
    $0                      # 显示所有服务的最后 100 行日志
    $0 backend              # 显示后端服务日志
    $0 -f frontend          # 实时跟踪前端日志
    $0 --lines 50 mysql     # 显示数据库最后 50 行日志

EOF
}

# 检查部署模式
check_deploy_mode() {
    # 优先检查standalone模式（通过PID文件或日志文件）
    if [ -d ".pids" ] && ([ -f ".pids/backend.pid" ] || [ -f ".pids/frontend.pid" ]); then
        echo "standalone"
    # 检查是否有standalone模式的日志文件
    elif [ -d "logs" ] && ([ -f "logs/backend.log" ] || [ -f "logs/frontend.log" ]); then
        echo "standalone"
    # 检查Docker模式
    elif docker ps --format "table {{.Names}}" 2>/dev/null | grep -q "nine-light"; then
        echo "docker"
    # 检查.env文件中的配置
    elif [ -f ".env" ] && grep -q "DEPLOY_MODE=docker" .env; then
        echo "docker"
    else
        echo "unknown"
    fi
}

# Docker 模式查看日志
view_docker_logs() {
    local service=$1
    local follow=$2
    local lines=$3
    
    local compose_cmd=""
    if command -v docker-compose &> /dev/null; then
        compose_cmd="docker-compose"
    elif docker compose version &> /dev/null; then
        compose_cmd="docker compose"
    else
        log_error "Docker Compose 未找到"
        exit 1
    fi
    
    local args=""
    if [ "$follow" = true ]; then
        args="$args -f"
    fi
    
    if [ -n "$lines" ]; then
        args="$args --tail $lines"
    fi
    
    if [ "$service" = "all" ]; then
        log_info "显示所有 Docker 服务日志..."
        $compose_cmd logs $args
    else
        log_info "显示 $service 服务日志..."
        $compose_cmd logs $args $service
    fi
}

# 独立模式查看日志
view_standalone_logs() {
    local service=$1
    local follow=$2
    local lines=$3
    
    local tail_args=""
    if [ "$follow" = true ]; then
        tail_args="$tail_args -f"
    fi
    
    if [ -n "$lines" ]; then
        tail_args="$tail_args -n $lines"
    else
        tail_args="$tail_args -n 100"
    fi
    
    case $service in
        frontend)
            if [ -f "logs/frontend.log" ]; then
                log_info "显示前端服务日志..."
                tail $tail_args logs/frontend.log
            else
                log_error "前端日志文件不存在: logs/frontend.log"
                exit 1
            fi
            ;;
        backend)
            if [ -f "logs/backend.log" ]; then
                log_info "显示后端服务日志..."
                tail $tail_args logs/backend.log
            else
                log_error "后端日志文件不存在: logs/backend.log"
                exit 1
            fi
            ;;
        mysql)
            log_warning "独立模式下没有 MySQL 服务日志"
            exit 1
            ;;
        all)
            log_info "显示所有独立服务日志..."
            if [ -f "logs/backend.log" ] && [ -f "logs/frontend.log" ]; then
                echo "=== 后端日志 ==="
                tail -n 50 logs/backend.log
                echo
                echo "=== 前端日志 ==="
                tail -n 50 logs/frontend.log
                
                if [ "$follow" = true ]; then
                    log_info "实时跟踪所有日志..."
                    tail -f logs/backend.log logs/frontend.log
                fi
            else
                log_error "日志文件不存在，请检查服务是否正在运行"
                exit 1
            fi
            ;;
        *)
            log_error "未知服务: $service"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    local service="all"
    local follow=false
    local lines=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow|-t|--tail)
                follow=true
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            frontend|backend|mysql|all)
                service="$1"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "Nine Light 日志查看器"
    echo "==================="
    echo
    
    # 检查部署模式
    local deploy_mode=$(check_deploy_mode)
    
    case $deploy_mode in
        docker)
            log_info "检测到 Docker 部署模式"
            view_docker_logs "$service" "$follow" "$lines"
            ;;
        standalone)
            log_info "检测到独立部署模式"
            view_standalone_logs "$service" "$follow" "$lines"
            ;;
        unknown)
            log_error "无法确定部署模式"
            log_info "请确保在 Nine Light 项目根目录下运行此脚本"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
