#!/bin/bash

# Nine Light 重启服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Nine Light 重启服务脚本

用法: $0 [选项]

选项:
    --force-rebuild     强制重新构建镜像/重新安装依赖
    --quick            快速重启 (不重新构建)
    -h, --help         显示此帮助信息

示例:
    $0                 # 标准重启
    $0 --quick         # 快速重启
    $0 --force-rebuild # 强制重建后重启

EOF
}

# 检查部署模式
check_deploy_mode() {
    if [ -f ".env" ] && grep -q "DEPLOY_MODE=docker" .env; then
        echo "docker"
    elif [ -d ".pids" ] || [ -d "logs" ]; then
        echo "standalone"
    else
        echo "unknown"
    fi
}

# 获取当前配置
get_current_config() {
    local frontend_port=8080
    local backend_port=3000
    local db_port=3306
    local deploy_mode="docker"
    local node_env="production"
    
    if [ -f ".env" ]; then
        frontend_port=$(grep "FRONTEND_PORT=" .env | cut -d'=' -f2 2>/dev/null || echo "8080")
        backend_port=$(grep "BACKEND_PORT=" .env | cut -d'=' -f2 2>/dev/null || echo "3000")
        db_port=$(grep "DB_PORT=" .env | cut -d'=' -f2 2>/dev/null || echo "3306")
        deploy_mode=$(grep "DEPLOY_MODE=" .env | cut -d'=' -f2 2>/dev/null || echo "docker")
        node_env=$(grep "NODE_ENV=" .env | cut -d'=' -f2 2>/dev/null || echo "production")
    fi
    
    echo "$frontend_port $backend_port $db_port $deploy_mode $node_env"
}

# Docker 模式重启
restart_docker_services() {
    local force_rebuild=$1
    local quick=$2
    
    log_info "重启 Docker 服务..."
    
    local compose_cmd=""
    if docker compose version &> /dev/null; then
        compose_cmd="docker compose"
    elif command -v docker-compose &> /dev/null; then
        compose_cmd="docker-compose"
    else
        log_error "Docker Compose 未找到"
        exit 1
    fi
    
    # 停止服务
    log_info "停止现有服务..."
    $compose_cmd down
    
    if [ "$quick" = false ]; then
        if [ "$force_rebuild" = true ]; then
            log_info "强制重新构建镜像..."
            $compose_cmd build --no-cache
        else
            log_info "检查并构建镜像..."
            $compose_cmd build
        fi
    fi
    
    # 启动服务
    log_info "启动服务..."
    $compose_cmd up -d
    
    log_success "Docker 服务重启完成"
}

# 独立模式重启
restart_standalone_services() {
    local force_rebuild=$1
    local quick=$2
    local config=($3)
    
    log_info "重启独立服务..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    ./scripts/stop.sh
    
    # 等待进程完全停止
    sleep 2
    
    # 重新部署
    local deploy_args=""
    deploy_args="$deploy_args --mode standalone"
    deploy_args="$deploy_args --frontend-port ${config[0]}"
    deploy_args="$deploy_args --backend-port ${config[1]}"
    deploy_args="$deploy_args --env ${config[4]}"
    
    if [ "$force_rebuild" = true ]; then
        deploy_args="$deploy_args --force-rebuild"
    fi
    
    if [ "$quick" = true ]; then
        deploy_args="$deploy_args --skip-deps"
    fi
    
    log_info "重新部署服务..."
    ./deploy.sh $deploy_args
    
    log_success "独立服务重启完成"
}

# 等待服务就绪
wait_for_services() {
    local config=($1)
    local frontend_port=${config[0]}
    local backend_port=${config[1]}
    
    log_info "等待服务就绪..."
    
    local max_attempts=30
    local attempt=0
    
    # 等待后端
    while [ $attempt -lt $max_attempts ]; do
        if curl -f -s --max-time 5 "http://localhost:$backend_port/api/auth/health" >/dev/null 2>&1; then
            log_success "后端服务已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        if [ $attempt -eq $max_attempts ]; then
            log_warning "后端服务启动超时，请检查日志"
            break
        fi
        
        sleep 2
    done
    
    # 等待前端
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if curl -f -s --max-time 5 "http://localhost:$frontend_port" >/dev/null 2>&1; then
            log_success "前端服务已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        if [ $attempt -eq $max_attempts ]; then
            log_warning "前端服务启动超时，请检查日志"
            break
        fi
        
        sleep 2
    done
}

# 主函数
main() {
    local force_rebuild=false
    local quick=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-rebuild)
                force_rebuild=true
                shift
                ;;
            --quick)
                quick=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "Nine Light 服务重启"
    echo "==================="
    echo
    
    # 获取当前配置
    local config=($(get_current_config))
    
    log_info "当前配置:"
    echo "  前端端口: ${config[0]}"
    echo "  后端端口: ${config[1]}"
    echo "  部署模式: ${config[3]}"
    echo "  运行环境: ${config[4]}"
    echo
    
    # 检查部署模式
    local deploy_mode=$(check_deploy_mode)
    
    case $deploy_mode in
        docker)
            restart_docker_services "$force_rebuild" "$quick"
            ;;
        standalone)
            restart_standalone_services "$force_rebuild" "$quick" "${config[*]}"
            ;;
        unknown)
            log_error "无法确定部署模式"
            log_info "请先运行部署脚本: ./deploy.sh"
            exit 1
            ;;
    esac
    
    # 等待服务就绪
    wait_for_services "${config[*]}"
    
    echo
    log_success "服务重启完成!"
    echo
    log_info "访问地址:"
    echo "  前端: http://localhost:${config[0]}"
    echo "  后端: http://localhost:${config[1]}"
    echo
    log_info "检查服务状态: ./scripts/health-check.sh"
}

# 执行主函数
main "$@"
