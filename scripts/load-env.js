#!/usr/bin/env node

/**
 * 环境变量加载工具
 * 用于从根目录的 .env 文件加载环境变量到子进程
 */

const fs = require('fs');
const path = require('path');

function loadEnvFromRoot() {
  const rootDir = path.resolve(__dirname, '..');
  const envPath = path.join(rootDir, '.env');
  
  if (!fs.existsSync(envPath)) {
    console.warn('Warning: Root .env file not found at', envPath);
    return;
  }
  
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      line = line.trim();
      
      // 跳过注释和空行
      if (!line || line.startsWith('#')) {
        return;
      }
      
      // 解析环境变量
      const equalIndex = line.indexOf('=');
      if (equalIndex === -1) {
        return;
      }
      
      const key = line.substring(0, equalIndex).trim();
      let value = line.substring(equalIndex + 1).trim();
      
      // 移除引号
      if ((value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith("'") && value.endsWith("'"))) {
        value = value.slice(1, -1);
      }
      
      // 只设置未定义的环境变量（不覆盖已存在的）
      if (!process.env[key]) {
        process.env[key] = value;
      }
    });
    
    console.log('✓ Loaded environment variables from root .env file');
  } catch (error) {
    console.error('Error loading root .env file:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  loadEnvFromRoot();
  
  // 如果提供了命令参数，则执行该命令
  if (process.argv.length > 2) {
    const { spawn } = require('child_process');
    const command = process.argv[2];
    const args = process.argv.slice(3);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      env: process.env,
      shell: true
    });
    
    child.on('exit', (code) => {
      process.exit(code);
    });
  }
}

module.exports = { loadEnvFromRoot };
