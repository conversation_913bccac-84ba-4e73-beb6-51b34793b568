#!/bin/bash

# Nine Light 健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取配置
get_config() {
    local frontend_port=8080
    local backend_port=3000
    local database_url=""

    if [ -f ".env" ]; then
        frontend_port=$(grep "FRONTEND_PORT=" .env | cut -d'=' -f2 || echo "8080")
        backend_port=$(grep "BACKEND_PORT=" .env | cut -d'=' -f2 || echo "3000")
        database_url=$(grep "DATABASE_URL=" .env | cut -d'=' -f2 || echo "")
    fi

    echo "$frontend_port $backend_port $database_url"
}

# 检查端口是否可访问
check_port() {
    local host=$1
    local port=$2
    local service_name=$3
    
    if curl -f -s --max-time 5 "http://$host:$port" >/dev/null 2>&1; then
        log_success "$service_name 服务正常 (http://$host:$port)"
        return 0
    else
        log_error "$service_name 服务异常 (http://$host:$port)"
        return 1
    fi
}

# 检查 API 健康状态
check_api_health() {
    local backend_port=$1
    
    log_info "检查 API 健康状态..."
    
    local response=$(curl -s --max-time 10 "http://localhost:$backend_port/api/auth/health" 2>/dev/null || echo "")
    
    if [ -n "$response" ]; then
        log_success "API 健康检查通过"
        echo "  响应: $response"
        return 0
    else
        log_error "API 健康检查失败"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    local database_url=$1

    log_info "检查数据库连接..."

    if [ -z "$database_url" ] || [ "$database_url" = "mysql://user:password@your-database-host:3306/database_name" ]; then
        log_warning "数据库连接未配置或使用默认模板"
        echo "  请在 .env 文件中配置正确的 DATABASE_URL"
        return 1
    fi

    # 从 DATABASE_URL 提取主机和端口
    local host_port=$(echo "$database_url" | sed -n 's/.*@\([^/]*\).*/\1/p')
    local host=$(echo "$host_port" | cut -d':' -f1)
    local port=$(echo "$host_port" | cut -d':' -f2)

    if [ -n "$host" ] && [ -n "$port" ]; then
        if nc -z "$host" "$port" 2>/dev/null; then
            log_success "外部数据库连接正常 ($host:$port)"
            return 0
        else
            log_error "无法连接到外部数据库 ($host:$port)"
            return 1
        fi
    else
        log_warning "无法解析数据库连接信息"
        return 1
    fi
}

# 检查 Docker 服务状态
check_docker_services() {
    log_info "检查 Docker 服务状态..."
    
    if command -v docker-compose &> /dev/null; then
        local compose_cmd="docker-compose"
    elif docker compose version &> /dev/null; then
        local compose_cmd="docker compose"
    else
        log_warning "Docker Compose 未找到，跳过 Docker 检查"
        return 1
    fi
    
    local services=$($compose_cmd ps --services 2>/dev/null || echo "")
    
    if [ -z "$services" ]; then
        log_warning "没有运行中的 Docker 服务"
        return 1
    fi
    
    echo "Docker 服务状态:"
    $compose_cmd ps
    
    return 0
}

# 检查独立服务状态
check_standalone_services() {
    log_info "检查独立服务状态..."
    
    local any_running=false
    
    # 检查后端进程
    if [ -f ".pids/backend.pid" ]; then
        local backend_pid=$(cat .pids/backend.pid)
        if kill -0 $backend_pid 2>/dev/null; then
            log_success "后端服务运行中 (PID: $backend_pid)"
            any_running=true
        else
            log_error "后端服务未运行 (PID 文件存在但进程不存在)"
        fi
    else
        log_warning "后端服务 PID 文件不存在"
    fi
    
    # 检查前端进程
    if [ -f ".pids/frontend.pid" ]; then
        local frontend_pid=$(cat .pids/frontend.pid)
        if kill -0 $frontend_pid 2>/dev/null; then
            log_success "前端服务运行中 (PID: $frontend_pid)"
            any_running=true
        else
            log_error "前端服务未运行 (PID 文件存在但进程不存在)"
        fi
    else
        log_warning "前端服务 PID 文件不存在"
    fi
    
    if [ "$any_running" = false ]; then
        log_warning "没有运行中的独立服务"
        return 1
    fi
    
    return 0
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."

    # 检查内存使用 (兼容 macOS 和 Linux)
    local mem_usage=""
    if command -v free &> /dev/null; then
        # Linux
        mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    elif command -v vm_stat &> /dev/null; then
        # macOS
        local vm_stat_output=$(vm_stat)
        local pages_free=$(echo "$vm_stat_output" | grep "Pages free" | awk '{print $3}' | sed 's/\.//')
        local pages_active=$(echo "$vm_stat_output" | grep "Pages active" | awk '{print $3}' | sed 's/\.//')
        local pages_inactive=$(echo "$vm_stat_output" | grep "Pages inactive" | awk '{print $3}' | sed 's/\.//')
        local pages_wired=$(echo "$vm_stat_output" | grep "Pages wired down" | awk '{print $4}' | sed 's/\.//')

        if [ -n "$pages_free" ] && [ -n "$pages_active" ] && [ -n "$pages_inactive" ] && [ -n "$pages_wired" ]; then
            local total_pages=$((pages_free + pages_active + pages_inactive + pages_wired))
            local used_pages=$((pages_active + pages_inactive + pages_wired))
            mem_usage=$(echo "scale=1; $used_pages * 100 / $total_pages" | bc 2>/dev/null || echo "N/A")
        else
            mem_usage="N/A"
        fi
    else
        mem_usage="N/A"
    fi
    echo "  内存使用率: ${mem_usage}%"

    # 检查磁盘使用
    local disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    echo "  磁盘使用率: ${disk_usage}%"

    # 检查 CPU 负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//' | xargs)
    echo "  CPU 负载: $load_avg"

    # 警告检查 (只在有有效数据时检查)
    if [ "$mem_usage" != "N/A" ] && command -v bc &> /dev/null; then
        if (( $(echo "$mem_usage > 90" | bc -l 2>/dev/null || echo "0") )); then
            log_warning "内存使用率过高: ${mem_usage}%"
        fi
    fi

    if [ -n "$disk_usage" ] && [ "$disk_usage" -gt 90 ] 2>/dev/null; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
    fi
}

# 生成健康报告
generate_health_report() {
    local frontend_port=$1
    local backend_port=$2
    local database_url=$3
    
    echo
    echo "=================================="
    echo "Nine Light 健康检查报告"
    echo "=================================="
    echo "检查时间: $(date)"
    echo
    
    local overall_status="健康"
    local issues=0
    
    # 检查前端
    echo "前端服务:"
    if check_port "localhost" "$frontend_port" "前端"; then
        echo "  状态: ✅ 正常"
    else
        echo "  状态: ❌ 异常"
        overall_status="异常"
        issues=$((issues + 1))
    fi
    echo "  地址: http://localhost:$frontend_port"
    echo
    
    # 检查后端
    echo "后端服务:"
    if check_port "localhost" "$backend_port" "后端"; then
        echo "  状态: ✅ 正常"
        check_api_health "$backend_port"
    else
        echo "  状态: ❌ 异常"
        overall_status="异常"
        issues=$((issues + 1))
    fi
    echo "  地址: http://localhost:$backend_port"
    echo
    
    # 检查数据库
    echo "数据库服务:"
    if check_database "$database_url"; then
        echo "  状态: ✅ 正常"
    else
        echo "  状态: ❌ 异常"
        overall_status="异常"
        issues=$((issues + 1))
    fi
    echo "  类型: 外部数据库"
    echo
    
    # 检查系统资源
    echo "系统资源:"
    check_system_resources
    echo
    
    # 总体状态
    echo "总体状态: $overall_status"
    if [ $issues -gt 0 ]; then
        echo "发现 $issues 个问题"
        return 1
    else
        echo "所有服务正常运行"
        return 0
    fi
}

# 主函数
main() {
    echo "Nine Light 健康检查"
    echo "==================="
    echo
    
    # 获取配置
    local config=($(get_config))
    local frontend_port=${config[0]}
    local backend_port=${config[1]}
    local database_url=${config[2]}
    
    # 检查部署模式
    if [ -f ".env" ] && grep -q "DEPLOY_MODE=docker" .env; then
        log_info "检测到 Docker 部署模式"
        check_docker_services
    elif [ -d ".pids" ]; then
        log_info "检测到独立部署模式"
        check_standalone_services
    else
        log_warning "无法确定部署模式"
    fi
    
    echo
    
    # 生成健康报告
    if generate_health_report "$frontend_port" "$backend_port" "$database_url"; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
