#!/bin/bash

echo "🚀 Nine Light E2E 测试脚本"
echo "================================"

# 检查服务是否运行
echo "🔍 检查服务状态..."

# 检查后端服务
if curl -f http://localhost:3001/api/auth/health > /dev/null 2>&1; then
    echo "✅ 后端服务正在运行 (http://localhost:3001)"
else
    echo "❌ 后端服务未运行，请先启动后端服务"
    echo "   运行: ./scripts/deploy-local.sh"
    exit 1
fi

# 检查前端服务
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务正在运行 (http://localhost:3000)"
else
    echo "❌ 前端服务未运行，请先启动前端服务"
    exit 1
fi

echo ""
echo "🧪 开始运行 E2E 测试..."
echo ""

# 运行 Playwright 测试
npx playwright test --reporter=line

echo ""
echo "📊 测试完成！"
echo "如需查看详细报告，运行: npm run test:e2e:report"