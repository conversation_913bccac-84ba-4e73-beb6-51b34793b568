services:
  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nine-light-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      DATABASE_URL: ${DATABASE_URL}
      NINE_LIGHT_DATABASE_URL: ${NINE_LIGHT_DATABASE_URL:-file:./data/nine-light.db}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      NINE_API_BASE_URL: ${NINE_API_BASE_URL:-https://openapi.binineex.com}
      ADMIN_USERNAME: ${ADMIN_USERNAME:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
    # 后端不需要直接暴露端口，通过前端 Nginx 代理访问
    # ports:
    #   - "3001:3000"
    networks:
      - nine-light-network
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data  # 挂载数据目录，解决权限问题
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/auth/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: ${VITE_API_URL:-http://localhost:3001}
        VITE_APP_TITLE: ${VITE_APP_TITLE:-Nine Light}
        VITE_APP_VERSION: ${VITE_APP_VERSION:-1.0.0}
        VITE_DEBUG: ${VITE_DEBUG:-false}
    container_name: nine-light-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    depends_on:
      - backend
    networks:
      - nine-light-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  nine-light-network:
    driver: bridge
